local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1, L16_1, L17_1, L18_1, L19_1, L20_1, L21_1, L22_1, L23_1, L24_1, L25_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["面粉"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["面粉"]
    L6_2 = {}
    L7_2 = 0
    L8_2 = 0
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L7_2 = {}
    L8_2 = 604
    L9_2 = 171
    L10_2 = 714
    L11_2 = 279
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L7_2[3] = L10_2
    L7_2[4] = L11_2
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    if L4_2 then
      L4_2 = _tap
      L5_2 = 1726
      L6_2 = 783
      L4_2(L5_2, L6_2)
      L4_2 = _find_cx
      L5_2 = Color
      L5_2 = L5_2["跑商"]
      L5_2 = L5_2["帮派键盘已显示"]
      L6_2 = {}
      L7_2 = 50
      L8_2 = 70
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = _tap
        L5_2 = 1793
        L6_2 = 342
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1336
        L6_2 = 343
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1794
        L6_2 = 487
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1796
        L6_2 = 642
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1503
        L6_2 = 975
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 40
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
        end
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
        return
      end
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["买面粉"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["符"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["符"]
    L6_2 = {}
    L7_2 = 0
    L8_2 = 0
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L7_2 = {}
    L8_2 = 604
    L9_2 = 171
    L10_2 = 714
    L11_2 = 279
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L7_2[3] = L10_2
    L7_2[4] = L11_2
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    if L4_2 then
      L4_2 = _tap
      L5_2 = 1726
      L6_2 = 783
      L4_2(L5_2, L6_2)
      L4_2 = _find_cx
      L5_2 = Color
      L5_2 = L5_2["跑商"]
      L5_2 = L5_2["帮派键盘已显示"]
      L6_2 = {}
      L7_2 = 50
      L8_2 = 70
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = _tap
        L5_2 = 1793
        L6_2 = 342
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1489
        L6_2 = 490
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1796
        L6_2 = 642
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1503
        L6_2 = 975
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 40
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
        end
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
        return
      end
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["买符"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["鹿茸"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["鹿茸"]
    L6_2 = {}
    L7_2 = 0
    L8_2 = 0
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L7_2 = {}
    L8_2 = 604
    L9_2 = 171
    L10_2 = 714
    L11_2 = 279
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L7_2[3] = L10_2
    L7_2[4] = L11_2
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    if L4_2 then
      L4_2 = _tap
      L5_2 = 1726
      L6_2 = 783
      L4_2(L5_2, L6_2)
      L4_2 = _find_cx
      L5_2 = Color
      L5_2 = L5_2["跑商"]
      L5_2 = L5_2["帮派键盘已显示"]
      L6_2 = {}
      L7_2 = 50
      L8_2 = 70
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = _tap
        L5_2 = 1793
        L6_2 = 342
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1489
        L6_2 = 490
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1796
        L6_2 = 642
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 200
        L6_2 = 300
        L4_2(L5_2, L6_2)
        L4_2 = _tap
        L5_2 = 1503
        L6_2 = 975
        L7_2 = 1
        L8_2 = 10
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 50
        L8_2 = 100
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
        end
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
        return
      end
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["买鹿茸"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  while true do
    L0_2 = _ENV["起号"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 396 - L0_2
    L3_2 = 172 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长安城"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 396
        L9_2 = 172
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = _tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 288
        L4_2(L5_2, L6_2)
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["出现重叠"]
        L4_2, L5_2, L6_2 = L4_2(L5_2)
        if L4_2 then
          L7_2 = L5_2 - 20
          L8_2 = L6_2 + 20
          L9_2 = L5_2 + 100
          L10_2 = L6_2 + 540
          L11_2 = _find_tb
          L12_2 = Color
          L12_2 = L12_2["跑商"]
          L12_2 = L12_2["商人重叠"]
          L13_2 = {}
          L14_2 = 0
          L15_2 = 0
          L13_2[1] = L14_2
          L13_2[2] = L15_2
          L14_2 = {}
          L15_2 = L7_2
          L16_2 = L8_2
          L17_2 = L9_2
          L18_2 = L10_2
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L14_2[3] = L17_2
          L14_2[4] = L18_2
          L11_2 = L11_2(L12_2, L13_2, L14_2)
          if L11_2 then
            L11_2 = _Sleep
            L12_2 = 100
            L13_2 = 200
            L11_2(L12_2, L13_2)
          end
        end
        L7_2 = _find_cx
        L8_2 = Color
        L8_2 = L8_2["跑商"]
        L8_2 = L8_2["我要交易"]
        L9_2 = {}
        L10_2 = 70
        L11_2 = 50
        L9_2[1] = L10_2
        L9_2[2] = L11_2
        L7_2 = L7_2(L8_2, L9_2)
        if L7_2 then
        end
        L7_2 = _find_cx
        L8_2 = Color
        L8_2 = L8_2["跑商"]
        L8_2 = L8_2["跑商交易界面"]
        L9_2 = {}
        L10_2 = 70
        L11_2 = 50
        L9_2[1] = L10_2
        L9_2[2] = L11_2
        L7_2 = L7_2(L8_2, L9_2)
        if L7_2 then
        L7_2 = 1
        L8_2 = 30
        L9_2 = 1
        for L10_2 = L7_2, L8_2, L9_2 do
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["跑商"]
          L12_2 = L12_2["鹿茸"]
          L13_2 = {}
          L14_2 = 0
          L15_2 = 0
          L13_2[1] = L14_2
          L13_2[2] = L15_2
          L14_2 = {}
          L15_2 = 998
          L16_2 = 178
          L17_2 = 1677
          L18_2 = 722
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L14_2[3] = L17_2
          L14_2[4] = L18_2
          L11_2 = L11_2(L12_2, L13_2, L14_2)
          if not L11_2 then
            L11_2 = _find
            L12_2 = Color
            L12_2 = L12_2["跑商"]
            L12_2 = L12_2["面粉"]
            L13_2 = {}
            L14_2 = 0
            L15_2 = 0
            L13_2[1] = L14_2
            L13_2[2] = L15_2
            L14_2 = {}
            L15_2 = 998
            L16_2 = 178
            L17_2 = 1677
            L18_2 = 722
            L14_2[1] = L15_2
            L14_2[2] = L16_2
            L14_2[3] = L17_2
            L14_2[4] = L18_2
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            if not L11_2 then
              L11_2 = _find
              L12_2 = Color
              L12_2 = L12_2["跑商"]
              L12_2 = L12_2["符"]
              L13_2 = {}
              L14_2 = 0
              L15_2 = 0
              L13_2[1] = L14_2
              L13_2[2] = L15_2
              L14_2 = {}
              L15_2 = 998
              L16_2 = 178
              L17_2 = 1677
              L18_2 = 722
              L14_2[1] = L15_2
              L14_2[2] = L16_2
              L14_2[3] = L17_2
              L14_2[4] = L18_2
              L11_2 = L11_2(L12_2, L13_2, L14_2)
              if L11_2 then
                L11_2 = _Sleep
                L12_2 = 500
                L13_2 = 588
                L11_2(L12_2, L13_2)
              end
            end
          end
          -- 移除 lbl_152 标签
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["跑商"]
          L12_2 = L12_2["最大"]
          L11_2 = L11_2(L12_2)
          if L11_2 then
            L11_2 = _Sleep
            L12_2 = 500
            L13_2 = 588
            L11_2(L12_2, L13_2)
          end
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["跑商"]
          L12_2 = L12_2["卖出"]
          L11_2 = L11_2(L12_2)
          if L11_2 then
            L11_2 = _Sleep
            L12_2 = 500
            L13_2 = 588
            L11_2(L12_2, L13_2)
          end
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["跑商"]
          L12_2 = L12_2["鹿茸"]
          L13_2 = {}
          L14_2 = 0
          L15_2 = 0
          L13_2[1] = L14_2
          L13_2[2] = L15_2
          L14_2 = {}
          L15_2 = 998
          L16_2 = 178
          L17_2 = 1677
          L18_2 = 722
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L14_2[3] = L17_2
          L14_2[4] = L18_2
          L11_2 = L11_2(L12_2, L13_2, L14_2)
          if L11_2 == false then
            L11_2 = _find
            L12_2 = Color
            L12_2 = L12_2["跑商"]
            L12_2 = L12_2["面粉"]
            L13_2 = {}
            L14_2 = 0
            L15_2 = 0
            L13_2[1] = L14_2
            L13_2[2] = L15_2
            L14_2 = {}
            L15_2 = 998
            L16_2 = 178
            L17_2 = 1677
            L18_2 = 722
            L14_2[1] = L15_2
            L14_2[2] = L16_2
            L14_2[3] = L17_2
            L14_2[4] = L18_2
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            if L11_2 == false then
              L11_2 = _find
              L12_2 = Color
              L12_2 = L12_2["跑商"]
              L12_2 = L12_2["符"]
              L13_2 = {}
              L14_2 = 0
              L15_2 = 0
              L13_2[1] = L14_2
              L13_2[2] = L15_2
              L14_2 = {}
              L15_2 = 998
              L16_2 = 178
              L17_2 = 1677
              L18_2 = 722
              L14_2[1] = L15_2
              L14_2[2] = L16_2
              L14_2[3] = L17_2
              L14_2[4] = L18_2
              L11_2 = L11_2(L12_2, L13_2, L14_2)
              if L11_2 == false then
                L11_2 = _find
                L12_2 = Color
                L12_2 = L12_2["跑商"]
                L12_2 = L12_2["跑商交易界面"]
                L11_2 = L11_2(L12_2)
                if L11_2 then
                  L11_2 = _Sleep
                  L12_2 = 500
                  L13_2 = 588
                  L11_2(L12_2, L13_2)
                  L11_2 = _ENV["通用功能"]
                  L11_2 = L11_2["关闭"]
                  L11_2()
                  L11_2 = true
                  return L11_2
                end
              end
            end
          end
          L11_2 = mSleep
          L12_2 = 50
          L11_2(L12_2)
        end
    end
    else
      L4_2 = _ENV["通用功能"]
      L4_2 = L4_2["关闭"]
      L4_2()
      L4_2 = _ENV["_前往"]
      L4_2 = L4_2["固定坐标"]
      L5_2 = "长安城"
      L6_2 = 1128
      L7_2 = 443
      L4_2(L5_2, L6_2, L7_2)
    end
    -- 移除 lbl_255 标签
    end
  end
end

_ENV["长安卖货"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  _ENV["购买帮派物品"] = true
  L0_2 = _find
  L1_2 = Color
  L1_2 = L1_2["跑商"]
  L1_2 = L1_2["面粉"]
  L0_2 = L0_2(L1_2)
  if L0_2 then
    L0_2 = _ENV["买面粉"]
    L0_2()
  else
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["符"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["买符"]
      L0_2()
    else
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["跑商"]
      L1_2 = L1_2["鹿茸"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _ENV["买鹿茸"]
        L0_2()
      else
        _ENV["购买帮派物品"] = false
        L0_2 = _ENV["通用功能"]
        L0_2 = L0_2["关闭"]
        L0_2()
      end
    end
  end
end

_ENV["帮派购买物品"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = 0
  while true do
    L1_2 = scrnLog
    L2_2 = "帮派购买"
    L1_2(L2_2)
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["跑商"]
    L2_2 = L2_2["帮派购买界面"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = closeScrnLog
      L2_2 = "帮派购买"
      L1_2(L2_2)
      L1_2 = true
      return L1_2
    else
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["跑商"]
      L2_2 = L2_2["帮派商品已卖完"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = closeScrnLog
        L2_2 = "帮派购买"
        L1_2(L2_2)
        L1_2 = _ENV["通用功能"]
        L1_2 = L1_2["关闭"]
        L1_2()
        L1_2 = false
        return L1_2
      else
        L1_2 = findFeat
        L2_2 = color
        L2_2 = L2_2.npcPos
        L2_2 = L2_2.exchequerDirector
        L1_2, L2_2 = L1_2(L2_2)
        if 0 < L1_2 then
          L0_2 = L0_2 + 1
          if 5 < L0_2 then
            L3_2 = randomTap
            L4_2 = 612
            L5_2 = 180
            L6_2 = 30
            L3_2(L4_2, L5_2, L6_2)
            L3_2 = mSleep
            L4_2 = 3333
            L3_2(L4_2)
            L3_2 = _ENV["moveJudge押镖"]
            L3_2()
            L3_2 = enterExchequer
            L3_2()
            L0_2 = 0
          end
          L3_2 = {}
          L4_2 = L1_2 + 45
          L5_2 = L2_2 - 70
          L3_2[1] = L4_2
          L3_2[2] = L5_2
          L4_2 = singleTap
          L5_2 = L3_2
          L6_2 = 20
          L4_2(L5_2, L6_2)
          L4_2 = _find_cx
          L5_2 = Color
          L5_2 = L5_2["跑商"]
          L5_2 = L5_2["帮派购买"]
          L6_2 = {}
          L7_2 = 50
          L8_2 = 50
          L6_2[1] = L7_2
          L6_2[2] = L8_2
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 then
          end
          L4_2 = 1
          L5_2 = 100
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["跑商"]
            L9_2 = L9_2["帮派购买界面"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              break
            end
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["跑商"]
            L9_2 = L9_2["帮派商品已卖完"]
            L8_2 = L8_2(L9_2)
            if L8_2 then
              break
            end
            L8_2 = mSleep
            L9_2 = 50
            L8_2(L9_2)
          end
        else
          L3_2 = getCurMap
          L3_2 = L3_2()
          if L3_2 == "WMC" then
            L4_2 = _cmp_tb
            L5_2 = Color
            L5_2 = L5_2["主界面"]
            L4_2 = L4_2(L5_2)
            if L4_2 then
              L4_2 = randomTap
              L5_2 = 612
              L6_2 = 180
              L7_2 = 30
              L4_2(L5_2, L6_2, L7_2)
              L4_2 = _ENV["moveJudge押镖"]
              L4_2()
              L4_2 = enterExchequer
              L4_2()
          end
          else
            L4_2 = findFeat
            L5_2 = color
            L5_2 = L5_2.VERINF
            L5_2 = L5_2.VERmoveWord
            L4_2 = L4_2(L5_2)
            if 0 < L4_2 then
              L4_2 = _ENV["_验证"]
              L4_2 = L4_2["漂浮2"]
              L4_2()
            else
              L4_2 = findFeat
              L5_2 = color
              L5_2 = L5_2.VERINF
              L5_2 = L5_2.VERidiom
              L4_2 = L4_2(L5_2)
              if 0 < L4_2 then
                L4_2 = _ENV["_验证"]
                L4_2 = L4_2["成语"]
                L4_2 = L4_2["验证"]
                L4_2()
              else
                L4_2 = myBlockcolor
                L5_2 = sevenColor
                L5_2 = L5_2["叉叉按钮"]
                L4_2 = L4_2(L5_2)
                if L4_2 then
                  L4_2 = randomTap
                  L5_2 = L1_2
                  L6_2 = L2_2
                  L7_2 = 15
                  L4_2(L5_2, L6_2, L7_2)
                  L4_2 = _ENV["_随机延时"]
                  L5_2 = 411
                  L4_2(L5_2)
                end
                L4_2 = _find
                L5_2 = Color
                L5_2 = L5_2.npc
                L5_2 = L5_2["通用关闭对话框"]
                L4_2 = L4_2(L5_2)
                if L4_2 then
                  L4_2 = mSleep
                  L5_2 = 440
                  L4_2(L5_2)
                end
                L4_2 = _cmp_tb
                L5_2 = Color
                L5_2 = L5_2["初始化"]
                L5_2 = L5_2["展开任务栏"]
                L4_2(L5_2)
                L4_2 = mSleep
                L5_2 = math
                L5_2 = L5_2.random
                L6_2 = 450
                L7_2 = 550
                L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L5_2(L6_2, L7_2)
                L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["进入帮派购买界面"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = _tap_tb
  L2_2 = 637
  L3_2 = 129
  L4_2 = 731
  L5_2 = 153
  L6_2 = 150
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  L1_2 = _tap_tb
  L2_2 = 637
  L3_2 = 129
  L4_2 = 731
  L5_2 = 153
  L6_2 = 150
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  L1_2 = _tap_tb
  L2_2 = 1178
  L3_2 = 708
  L4_2 = 1233
  L5_2 = 746
  L6_2 = 150
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  L1_2 = _cmp_cx
  L2_2 = Color
  L2_2 = L2_2["跑商"]
  L2_2 = L2_2["二药小键盘"]
  L3_2 = {}
  L4_2 = 30
  L5_2 = 100
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L1_2(L2_2, L3_2)
  L1_2 = _tap
  L2_2 = 1260
  L3_2 = 292
  L4_2 = 1
  L5_2 = 30
  L1_2(L2_2, L3_2, L4_2, L5_2)
  L1_2 = _Sleep
  L2_2 = 55
  L3_2 = 60
  L1_2(L2_2, L3_2)
  L1_2 = _tap
  L2_2 = 1260
  L3_2 = 292
  L4_2 = 1
  L5_2 = 30
  L1_2(L2_2, L3_2, L4_2, L5_2)
  L1_2 = _Sleep
  L2_2 = 100
  L3_2 = 120
  L1_2(L2_2, L3_2)
  if A0_2 == 5 then
    L1_2 = _tap_tb
    L2_2 = 879
    L3_2 = 960
    L4_2 = 1076
    L5_2 = 1015
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    L1_2 = 1
    L2_2 = 20
    L3_2 = 1
    for L4_2 = L1_2, L2_2, L3_2 do
      L5_2 = _tap
      L6_2 = 1277
      L7_2 = 729
      L8_2 = 1
      L9_2 = 10
      L5_2(L6_2, L7_2, L8_2, L9_2)
    end
  elseif A0_2 == 20 then
    L1_2 = _tap
    L2_2 = 954
    L3_2 = 293
    L4_2 = 1
    L5_2 = 30
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    L1_2 = _tap
    L2_2 = 1257
    L3_2 = 442
    L4_2 = 1
    L5_2 = 30
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    L1_2 = _tap_tb
    L2_2 = 879
    L3_2 = 960
    L4_2 = 1076
    L5_2 = 1015
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  else
    L1_2 = _tap
    L2_2 = 801
    L3_2 = 292
    L4_2 = 1
    L5_2 = 30
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    L1_2 = _tap
    L2_2 = 1257
    L3_2 = 442
    L4_2 = 1
    L5_2 = 30
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    L1_2 = _tap_tb
    L2_2 = 879
    L3_2 = 960
    L4_2 = 1076
    L5_2 = 1015
    L6_2 = 150
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
  end
  L1_2 = _tap_tb
  L2_2 = 879
  L3_2 = 960
  L4_2 = 1076
  L5_2 = 1015
  L6_2 = 150
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
end

_ENV["购买药品"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = 0
  L1_2 = 1
  L2_2 = 30
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = _ENV["购买药品"]
    L6_2 = 10
    L5_2(L6_2)
    L5_2 = mSleep
    L6_2 = _ENV["卡顿掉帧"]
    L6_2 = L6_2 * 2
    L5_2(L6_2)
    L5_2 = _find_cx
    L6_2 = Color
    L6_2 = L6_2["起号"]
    L6_2 = L6_2["出现对话框"]
    L7_2 = {}
    L8_2 = 20
    L9_2 = 10
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L5_2 = L5_2(L6_2, L7_2)
    if L5_2 then
      L5_2 = 1
      L6_2 = 10
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = _find
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["帮派药品已卖完"]
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L0_2 = 10
          break
        end
        L9_2 = _find
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["帮派药品帮贡不足"]
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L0_2 = 10
          break
        end
        L9_2 = _find
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["银子不够"]
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L0_2 = 10
          break
        end
        L9_2 = _find
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["帮派药品不够"]
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L9_2 = _find
          L10_2 = Color
          L10_2 = L10_2.npc
          L10_2 = L10_2["通用关闭对话框"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            L9_2 = _ENV["_随机延时"]
            L10_2 = 111
            L9_2(L10_2)
          end
          L9_2 = _ENV["购买药品"]
          L10_2 = 5
          L9_2(L10_2)
          L0_2 = L0_2 + 1
        end
        L9_2 = _find
        L10_2 = Color
        L10_2 = L10_2["跑商"]
        L10_2 = L10_2["帮派药品没有出售"]
        L9_2 = L9_2(L10_2)
        if L9_2 then
          L0_2 = 10
          break
        end
        L9_2 = mSleep
        L10_2 = 50
        L9_2(L10_2)
      end
    end
    if L4_2 == 30 or 5 < L0_2 then
      repeat
        repeat
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["通用"]
          L6_2 = L6_2["关闭对话框"]
          L7_2 = {}
          L8_2 = math
          L8_2 = L8_2.random
          L9_2 = -100
          L10_2 = 100
          L8_2 = L8_2(L9_2, L10_2)
          L8_2 = -1000 + L8_2
          L9_2 = math
          L9_2 = L9_2.random
          L10_2 = -1
          L11_2 = 100
          L9_2 = L9_2(L10_2, L11_2)
          L9_2 = -100 + L9_2
          L7_2[1] = L8_2
          L7_2[2] = L9_2
          L5_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            L5_2 = _Sleep
            L6_2 = 190
            L7_2 = 290
            L5_2(L6_2, L7_2)
          end
          L5_2 = _find_tb
          L6_2 = Color
          L6_2 = L6_2["通用"]
          L6_2 = L6_2["关闭窗口"]
          L5_2 = L5_2(L6_2)
          if L5_2 then
            L5_2 = _Sleep
            L6_2 = 190
            L7_2 = 290
            L5_2(L6_2, L7_2)
          end
          L5_2 = _find
          L6_2 = Color
          L6_2 = L6_2["跑商"]
          L6_2 = L6_2["帮派二药购买界面"]
          L5_2 = L5_2(L6_2)
        until L5_2 == false
        L5_2 = _find_tb
        L6_2 = Color
        L6_2 = L6_2["起号"]
        L6_2 = L6_2["绘卷"]
        L5_2 = L5_2(L6_2)
      until L5_2
      L5_2 = true
      return L5_2
    end
  end
end

_ENV["买药品"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  while true do
    L0_2 = scrnLog
    L1_2 = "前往书院"
    L0_2(L1_2)
    L0_2 = getCurMap
    L0_2 = L0_2()
    L1_2 = findFeat
    L2_2 = color
    L2_2 = L2_2.npcPos
    L2_2 = L2_2["书院"]
    L1_2, L2_2 = L1_2(L2_2)
    L3_2 = findFeat
    L4_2 = color
    L4_2 = L4_2.npcPos
    L4_2 = L4_2["书院红"]
    L3_2, L4_2 = L3_2(L4_2)
    if L0_2 == "WMC" and 0 < L1_2 then
      L5_2 = {}
      L6_2 = L1_2 + 200
      L7_2 = L2_2
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L6_2 = singleTap
      L7_2 = L5_2
      L8_2 = 20
      L6_2(L7_2, L8_2)
      L6_2 = mSleep
      L7_2 = math
      L7_2 = L7_2.random
      L8_2 = 1000
      L9_2 = 1400
      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
      L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L6_2 = _ENV["moveJudge押镖"]
      L6_2()
      L6_2 = closeScrnLog
      L7_2 = "前往书院"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L0_2 == "WMC" and L1_2 < 0 and L3_2 < 0 then
      L5_2 = {}
      L6_2 = 617
      L7_2 = 177
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L6_2 = singleTap
      L7_2 = L5_2
      L8_2 = 20
      L6_2(L7_2, L8_2)
      L6_2 = mSleep
      L7_2 = 1000
      L6_2(L7_2)
      L6_2 = _ENV["moveJudge押镖"]
      L6_2()
    elseif L0_2 == "WMC" and 0 < L3_2 then
      L5_2 = {}
      L6_2 = L3_2 + 200
      L7_2 = L4_2
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L6_2 = singleTap
      L7_2 = L5_2
      L8_2 = 20
      L6_2(L7_2, L8_2)
      L6_2 = mSleep
      L7_2 = math
      L7_2 = L7_2.random
      L8_2 = 1000
      L9_2 = 1400
      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L7_2(L8_2, L9_2)
      L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L6_2 = _ENV["moveJudge押镖"]
      L6_2()
      L6_2 = closeScrnLog
      L7_2 = "前往书院"
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    elseif L0_2 == "" then
      L5_2 = fightSystem
      L5_2()
    elseif L0_2 == "BP" then
      L5_2 = _ENV["UI_车夫寻找方式"]
      if L5_2 == "自动识别" then
        L5_2 = mSleep
        L6_2 = 500
        L5_2(L6_2)
        L5_2 = tsFindText
        L6_2 = _ENV["车夫坐标"]
        L7_2 = "车夫"
        L8_2 = 100
        L9_2 = 100
        L10_2 = 1820
        L11_2 = 980
        L12_2 = "D5C636 , 17170D # E62C19 , 192C19"
        L13_2 = 90
        L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L2_2 = L6_2
        L1_2 = L5_2
        L5_2 = _tap
        L6_2 = L1_2 - 10
        L7_2 = L2_2 - 45
        L5_2(L6_2, L7_2)
        L5_2 = _find_cx
        L6_2 = Color
        L6_2 = L6_2["跑商"]
        L6_2 = L6_2["进入书院"]
        L7_2 = {}
        L8_2 = 50
        L9_2 = 70
        L7_2[1] = L8_2
        L7_2[2] = L9_2
        L5_2 = L5_2(L6_2, L7_2)
        if L5_2 then
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "无名城"
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = mSleep
          L6_2 = 777
          L5_2(L6_2)
        end
        L5_2 = _find
        L6_2 = Color
        L6_2 = L6_2["跑商"]
        L6_2 = L6_2["车夫金钱不足"]
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = _call_script
          L6_2 = "dd_7"
          L5_2(L6_2)
        end
      else
        L5_2 = _ENV["起号"]
        L5_2 = L5_2["坐标"]
        L5_2, L6_2 = L5_2()
        L7_2 = _ENV["车夫x"]
        L7_2 = L7_2 - L5_2
        L8_2 = _ENV["车夫y"]
        L8_2 = L8_2 - L6_2
        L9_2 = math
        L9_2 = L9_2.abs
        L10_2 = L7_2
        L9_2 = L9_2(L10_2)
        if L9_2 <= 12 then
          L9_2 = math
          L9_2 = L9_2.abs
          L10_2 = L8_2
          L9_2 = L9_2(L10_2)
          if L9_2 <= 12 then
            L9_2 = _ENV["_计算"]
            L9_2 = L9_2["取目标屏幕坐标"]
            L10_2 = "帮派"
            L11_2 = L5_2
            L12_2 = L6_2
            L13_2 = _ENV["车夫x"]
            L14_2 = _ENV["车夫y"]
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
            L6_2 = L10_2
            L5_2 = L9_2
            L9_2 = _tap
            L10_2 = L5_2
            L11_2 = L6_2
            L9_2(L10_2, L11_2)
            L9_2 = _find_cx
            L10_2 = Color
            L10_2 = L10_2["跑商"]
            L10_2 = L10_2["进入书院"]
            L11_2 = {}
            L12_2 = 50
            L13_2 = 70
            L11_2[1] = L12_2
            L11_2[2] = L13_2
            L9_2 = L9_2(L10_2, L11_2)
            if L9_2 then
            end
            L9_2 = _ENV["_返回"]
            L9_2 = L9_2["地图"]
            L10_2 = "无名城"
            L9_2 = L9_2(L10_2)
            if L9_2 then
              L9_2 = mSleep
              L10_2 = 663
              L9_2(L10_2)
            end
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["跑商"]
            L10_2 = L10_2["车夫金钱不足"]
            L9_2 = L9_2(L10_2)
            if L9_2 then
              L9_2 = _call_script
              L10_2 = "dd_7"
              L9_2(L10_2)
            end
          end
        end
      end
    else
      L5_2 = goToBP
      L6_2 = travelMod
      L5_2(L6_2)
    end
  end
end

_ENV["帮派前往书院"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  while true do
    L0_2 = scrnLog
    L1_2 = "准备去书院"
    L0_2(L1_2)
    L0_2 = getCurMap
    L0_2 = L0_2()
    if L0_2 == "BP" then
      L1_2 = true
      return L1_2
    elseif L0_2 == "WMC" then
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.npcPos
      L2_2 = L2_2["机关人"]
      L1_2, L2_2 = L1_2(L2_2)
      L3_2 = findFeat
      L4_2 = color
      L4_2 = L4_2.npcPos
      L4_2 = L4_2["机关人红"]
      L3_2, L4_2 = L3_2(L4_2)
      if 0 < L1_2 then
        L5_2 = {}
        L6_2 = L1_2 + 1
        L7_2 = L2_2 - 70
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
      elseif 0 < L3_2 then
        L5_2 = {}
        L6_2 = L3_2 + 170
        L7_2 = L4_2 + 70
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 15
        L6_2(L7_2, L8_2)
      else
        L5_2 = math
        L5_2 = L5_2.random
        L6_2 = 1
        L7_2 = 5
        L5_2 = L5_2(L6_2, L7_2)
        if 3 < L5_2 then
          L6_2 = randomTap
          L7_2 = 409
          L8_2 = 348
          L9_2 = 40
          L6_2(L7_2, L8_2, L9_2)
        else
          L6_2 = randomTap
          L7_2 = 1467
          L8_2 = 398
          L9_2 = 5
          L6_2(L7_2, L8_2, L9_2)
        end
        L6_2 = mSleep
        L7_2 = 1111
        L6_2(L7_2)
        L6_2 = _ENV["moveJudge押镖"]
        L6_2()
      end
      L5_2 = _find_cx
      L6_2 = Color
      L6_2 = L6_2["跑商"]
      L6_2 = L6_2["送我回出口"]
      L7_2 = {}
      L8_2 = 30
      L9_2 = 30
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "帮派"
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = mSleep
          L6_2 = 222
          L5_2(L6_2)
          break
        end
      end
    else
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.VERINF
      L2_2 = L2_2.VERmoveWord
      L1_2 = L1_2(L2_2)
      if 0 < L1_2 then
        L1_2 = _ENV["_验证"]
        L1_2 = L1_2["漂浮2"]
        L1_2()
      else
        L1_2 = findFeat
        L2_2 = color
        L2_2 = L2_2.VERINF
        L2_2 = L2_2.VERidiom
        L1_2 = L1_2(L2_2)
        if 0 < L1_2 then
          L1_2 = _ENV["_验证"]
          L1_2 = L1_2["成语"]
          L1_2 = L1_2["验证"]
          L1_2()
        else
          L1_2 = _cmp_tb
          L2_2 = Color
          L2_2 = L2_2["初始化"]
          L2_2 = L2_2["展开任务栏"]
          L1_2(L2_2)
          L1_2 = mSleep
          L2_2 = math
          L2_2 = L2_2.random
          L3_2 = 450
          L4_2 = 550
          L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L2_2(L3_2, L4_2)
          L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
        end
      end
      L1_2 = myBlockcolor
      L2_2 = sevenColor
      L2_2 = L2_2["叉叉按钮"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = randomTap
        L2_2 = x
        L3_2 = y
        L4_2 = 15
        L1_2(L2_2, L3_2, L4_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 211
        L1_2(L2_2)
      end
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2.npc
      L2_2 = L2_2["通用关闭对话框"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = mSleep
        L2_2 = 240
        L1_2(L2_2)
      end
    end
  end
end

_ENV["金库到帮派"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  while true do
    L0_2 = scrnLog
    L1_2 = "帮派"
    L0_2(L1_2)
    L0_2 = getCurMap
    L0_2 = L0_2()
    if L0_2 == "BP" then
      L1_2 = true
      return L1_2
    elseif L0_2 == "WMC" then
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.npcPos
      L2_2 = L2_2["书院"]
      L1_2, L2_2 = L1_2(L2_2)
      if 0 < L1_2 then
        L3_2 = {}
        L4_2 = L1_2 + 1
        L5_2 = L2_2 - 70
        L3_2[1] = L4_2
        L3_2[2] = L5_2
        L4_2 = singleTap
        L5_2 = L3_2
        L6_2 = 15
        L4_2(L5_2, L6_2)
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["跑商"]
        L5_2 = L5_2["送我回出口"]
        L6_2 = {}
        L7_2 = 30
        L8_2 = 30
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = _ENV["_返回"]
          L4_2 = L4_2["地图"]
          L5_2 = "帮派"
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = mSleep
            L5_2 = 222
            L4_2(L5_2)
            break
          end
        else
          L4_2 = findFeat
          L5_2 = color
          L5_2 = L5_2.npcPos
          L5_2 = L5_2["书院红"]
          L4_2, L5_2 = L4_2(L5_2)
          if 0 < L4_2 then
            L6_2 = {}
            L7_2 = L4_2 + 170
            L8_2 = L5_2 + 70
            L6_2[1] = L7_2
            L6_2[2] = L8_2
            L7_2 = singleTap
            L8_2 = L6_2
            L9_2 = 15
            L7_2(L8_2, L9_2)
          end
        end
        L4_2 = _ENV["_返回"]
        L4_2 = L4_2["地图"]
        L5_2 = "帮派"
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = mSleep
          L5_2 = 222
          L4_2(L5_2)
          break
        end
      else
        L3_2 = randomTap
        L4_2 = 612
        L5_2 = 180
        L6_2 = 30
        L3_2(L4_2, L5_2, L6_2)
        L3_2 = mSleep
        L4_2 = 2222
        L3_2(L4_2)
      end
    else
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.VERINF
      L2_2 = L2_2.VERmoveWord
      L1_2 = L1_2(L2_2)
      if 0 < L1_2 then
        L1_2 = _ENV["_验证"]
        L1_2 = L1_2["漂浮2"]
        L1_2()
      else
        L1_2 = findFeat
        L2_2 = color
        L2_2 = L2_2.VERINF
        L2_2 = L2_2.VERidiom
        L1_2 = L1_2(L2_2)
        if 0 < L1_2 then
          L1_2 = _ENV["_验证"]
          L1_2 = L1_2["成语"]
          L1_2 = L1_2["验证"]
          L1_2()
        else
          L1_2 = _cmp_tb
          L2_2 = Color
          L2_2 = L2_2["初始化"]
          L2_2 = L2_2["展开任务栏"]
          L1_2(L2_2)
          L1_2 = mSleep
          L2_2 = math
          L2_2 = L2_2.random
          L3_2 = 450
          L4_2 = 550
          L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L2_2(L3_2, L4_2)
          L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
        end
      end
      L1_2 = myBlockcolor
      L2_2 = sevenColor
      L2_2 = L2_2["叉叉按钮"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = randomTap
        L2_2 = x
        L3_2 = y
        L4_2 = 15
        L1_2(L2_2, L3_2, L4_2)
        L1_2 = _ENV["_随机延时"]
        L2_2 = 211
        L1_2(L2_2)
      end
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2.npc
      L2_2 = L2_2["通用关闭对话框"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = mSleep
        L2_2 = 240
        L1_2(L2_2)
      end
    end
  end
end

_ENV["书院到帮派"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  while true do
    L0_2 = scrnLog
    L1_2 = "帮派购买"
    L0_2(L1_2)
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["帮派二药购买界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = closeScrnLog
      L1_2 = "帮派购买"
      L0_2(L1_2)
      L0_2 = true
      return L0_2
    else
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["跑商"]
      L1_2 = L1_2["帮派商品已卖完"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = closeScrnLog
        L1_2 = "帮派购买"
        L0_2(L1_2)
        L0_2 = _ENV["通用功能"]
        L0_2 = L0_2["关闭"]
        L0_2()
        L0_2 = false
        return L0_2
      else
        L0_2 = _ENV["押镖屏蔽"]
        L0_2()
        L0_2 = findFeat
        L1_2 = color
        L1_2 = L1_2.npcPos
        L1_2 = L1_2["书院"]
        L0_2, L1_2 = L0_2(L1_2)
        if 0 < L0_2 then
          L2_2 = {}
          L3_2 = L0_2 + 4
          L4_2 = L1_2 - 70
          L2_2[1] = L3_2
          L2_2[2] = L4_2
          L3_2 = singleTap
          L4_2 = L2_2
          L5_2 = 20
          L3_2(L4_2, L5_2)
          L3_2 = _find_cx
          L4_2 = Color
          L4_2 = L4_2["跑商"]
          L4_2 = L4_2["帮派购买"]
          L5_2 = {}
          L6_2 = 30
          L7_2 = 30
          L5_2[1] = L6_2
          L5_2[2] = L7_2
          L3_2 = L3_2(L4_2, L5_2)
          if L3_2 then
          end
          L3_2 = 1
          L4_2 = 30
          L5_2 = 1
          for L6_2 = L3_2, L4_2, L5_2 do
            L7_2 = _find
            L8_2 = Color
            L8_2 = L8_2["跑商"]
            L8_2 = L8_2["帮派二药购买界面"]
            L7_2 = L7_2(L8_2)
            if L7_2 then
              break
            end
            L7_2 = _find
            L8_2 = Color
            L8_2 = L8_2["跑商"]
            L8_2 = L8_2["帮派商品已卖完"]
            L7_2 = L7_2(L8_2)
            if L7_2 then
              break
            end
            L7_2 = mSleep
            L8_2 = 30
            L7_2(L8_2)
          end
          L3_2 = findFeat
          L4_2 = color
          L4_2 = L4_2.npcPos
          L4_2 = L4_2["书院红"]
          L3_2, L4_2 = L3_2(L4_2)
          yy1 = L4_2
          xx1 = L3_2
          L3_2 = xx1
          if 0 < L3_2 then
            L3_2 = xx1
            if 1000 < L3_2 then
              zgzz = -200
            else
              zgzz = 200
            end
            L3_2 = {}
            L4_2 = xx1
            L5_2 = zgzz
            L4_2 = L4_2 + L5_2
            L5_2 = yy1
            L5_2 = L5_2 + 10
            L3_2[1] = L4_2
            L3_2[2] = L5_2
            L4_2 = singleTap
            L5_2 = L3_2
            L6_2 = 20
            L4_2(L5_2, L6_2)
            L4_2 = mSleep
            L5_2 = math
            L5_2 = L5_2.random
            L6_2 = 1450
            L7_2 = 1550
            L5_2, L6_2, L7_2, L8_2, L9_2 = L5_2(L6_2, L7_2)
            L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
          end
        else
          L2_2 = getCurMap
          L2_2 = L2_2()
          L3_2 = findFeat
          L4_2 = color
          L4_2 = L4_2.npcPos
          L4_2 = L4_2["书院红"]
          L3_2, L4_2 = L3_2(L4_2)
          yy1 = L4_2
          xx1 = L3_2
          if L2_2 == "WMC" then
            L3_2 = _cmp_tb
            L4_2 = Color
            L4_2 = L4_2["主界面"]
            L3_2 = L3_2(L4_2)
            if L3_2 then
              L3_2 = xx1
              if L3_2 < 0 then
                L3_2 = randomTap
                L4_2 = 912
                L5_2 = 180
                L6_2 = 30
                L3_2(L4_2, L5_2, L6_2)
                L3_2 = _ENV["moveJudge押镖"]
                L3_2()
                L3_2 = enterExchequer
                L3_2()
            end
          end
          else
            L3_2 = findFeat
            L4_2 = color
            L4_2 = L4_2.VERINF
            L4_2 = L4_2.VERmoveWord
            L3_2 = L3_2(L4_2)
            if 0 < L3_2 then
              L3_2 = _ENV["_验证"]
              L3_2 = L3_2["漂浮2"]
              L3_2()
            else
              L3_2 = findFeat
              L4_2 = color
              L4_2 = L4_2.VERINF
              L4_2 = L4_2.VERidiom
              L3_2 = L3_2(L4_2)
              if 0 < L3_2 then
                L3_2 = _ENV["_验证"]
                L3_2 = L3_2["成语"]
                L3_2 = L3_2["验证"]
                L3_2()
              else
                L3_2 = myBlockcolor
                L4_2 = sevenColor
                L4_2 = L4_2["叉叉按钮"]
                L3_2 = L3_2(L4_2)
                if L3_2 then
                  L3_2 = randomTap
                  L4_2 = L0_2
                  L5_2 = L1_2
                  L6_2 = 15
                  L3_2(L4_2, L5_2, L6_2)
                  L3_2 = _ENV["_随机延时"]
                  L4_2 = 211
                  L3_2(L4_2)
                end
                L3_2 = _find
                L4_2 = Color
                L4_2 = L4_2.npc
                L4_2 = L4_2["通用关闭对话框"]
                L3_2 = L3_2(L4_2)
                if L3_2 then
                  L3_2 = mSleep
                  L4_2 = 240
                  L3_2(L4_2)
                end
                L3_2 = _cmp_tb
                L4_2 = Color
                L4_2 = L4_2["初始化"]
                L4_2 = L4_2["展开任务栏"]
                L3_2(L4_2)
                L3_2 = mSleep
                L4_2 = math
                L4_2 = L4_2.random
                L5_2 = 250
                L6_2 = 350
                L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L4_2(L5_2, L6_2)
                L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["进入帮派购买药品界面"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["长安城仓库"]
  L0_2()
  L0_2 = 1
  L1_2 = 45
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["屏蔽"]
    L5_2 = L5_2["取消"]
    L4_2(L5_2)
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["仓库有位置"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _ENV["UI_活力处置"]
      if L4_2 == "飞行符" then
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["跑商"]
        L5_2 = L5_2["存飞行符"]
        L4_2 = L4_2(L5_2)
        if L4_2 == false then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
          L4_2 = true
          return L4_2
        end
      else
        L4_2 = _find_tb_cx
        L5_2 = Color
        L5_2 = L5_2["跑商"]
        L5_2 = L5_2["存二药"]
        L6_2 = {}
        L7_2 = 5
        L8_2 = 5
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 == false then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
          L4_2 = true
          return L4_2
        end
      end
    else
      L4_2 = _tap
      L5_2 = 543
      L6_2 = 936
      L4_2(L5_2, L6_2)
      L4_2 = mSleep
      L5_2 = 600
      L4_2(L5_2)
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["存物品"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  while true do
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["技能界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 == false then
      L0_2 = _tap
      L1_2 = 1749
      L2_2 = 69
      L0_2(L1_2, L2_2)
      L0_2 = _ENV["_随机延时"]
      L1_2 = 411
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["辅助技能"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = mSleep
      L1_2 = 300
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["烹饪包子"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["屏蔽"]
    L1_2 = L1_2["取消"]
    L0_2(L1_2)
  end
  L0_2 = 1
  L1_2 = 5
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["开始烹饪"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = mSleep
      L5_2 = 300
      L4_2(L5_2)
    end
    L4_2 = mSleep
    L5_2 = 50
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
  L0_2 = _ENV["丢弃烹饪"]
  L0_2()
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["长安城仓库"]
  L0_2()
  L0_2 = 1
  L1_2 = 25
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["屏蔽"]
    L5_2 = L5_2["取消"]
    L4_2(L5_2)
    L4_2 = _cmp
    L5_2 = Color
    L5_2 = L5_2["跑商"]
    L5_2 = L5_2["仓库有位置"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = _find_tb_cx
      L5_2 = Color
      L5_2 = L5_2["跑商"]
      L5_2 = L5_2["存烹饪"]
      L6_2 = {}
      L7_2 = 10
      L8_2 = 15
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 == false then
        L4_2 = _ENV["通用功能"]
        L4_2 = L4_2["关闭"]
        L4_2()
        L4_2 = true
        return L4_2
      end
    else
      L4_2 = _tap
      L5_2 = 543
      L6_2 = 936
      L4_2(L5_2, L6_2)
      L4_2 = mSleep
      L5_2 = 600
      L4_2(L5_2)
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
  L0_2 = _ENV["通用功能"]
  L0_2 = L0_2["关闭"]
  L0_2()
end

_ENV["烹饪"] = L0_1
L0_1 = {}
L1_1 = {}
L2_1 = 452
L3_1 = 292
L4_1 = 563
L5_1 = 401
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L1_1[4] = L5_1
L2_1 = {}
L3_1 = 598
L4_1 = 292
L5_1 = 708
L6_1 = 401
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L3_1 = {}
L4_1 = 744
L5_1 = 292
L6_1 = 855
L7_1 = 401
L3_1[1] = L4_1
L3_1[2] = L5_1
L3_1[3] = L6_1
L3_1[4] = L7_1
L4_1 = {}
L5_1 = 890
L6_1 = 292
L7_1 = 1002
L8_1 = 401
L4_1[1] = L5_1
L4_1[2] = L6_1
L4_1[3] = L7_1
L4_1[4] = L8_1
L5_1 = {}
L6_1 = 1035
L7_1 = 292
L8_1 = 1149
L9_1 = 401
L5_1[1] = L6_1
L5_1[2] = L7_1
L5_1[3] = L8_1
L5_1[4] = L9_1
L6_1 = {}
L7_1 = 452
L8_1 = 438
L9_1 = 563
L10_1 = 545
L6_1[1] = L7_1
L6_1[2] = L8_1
L6_1[3] = L9_1
L6_1[4] = L10_1
L7_1 = {}
L8_1 = 598
L9_1 = 438
L10_1 = 708
L11_1 = 545
L7_1[1] = L8_1
L7_1[2] = L9_1
L7_1[3] = L10_1
L7_1[4] = L11_1
L8_1 = {}
L9_1 = 744
L10_1 = 438
L11_1 = 855
L12_1 = 545
L8_1[1] = L9_1
L8_1[2] = L10_1
L8_1[3] = L11_1
L8_1[4] = L12_1
L9_1 = {}
L10_1 = 890
L11_1 = 438
L12_1 = 1002
L13_1 = 545
L9_1[1] = L10_1
L9_1[2] = L11_1
L9_1[3] = L12_1
L9_1[4] = L13_1
L10_1 = {}
L11_1 = 1035
L12_1 = 438
L13_1 = 1149
L14_1 = 545
L10_1[1] = L11_1
L10_1[2] = L12_1
L10_1[3] = L13_1
L10_1[4] = L14_1
L11_1 = {}
L12_1 = 452
L13_1 = 580
L14_1 = 563
L15_1 = 691
L11_1[1] = L12_1
L11_1[2] = L13_1
L11_1[3] = L14_1
L11_1[4] = L15_1
L12_1 = {}
L13_1 = 598
L14_1 = 580
L15_1 = 708
L16_1 = 691
L12_1[1] = L13_1
L12_1[2] = L14_1
L12_1[3] = L15_1
L12_1[4] = L16_1
L13_1 = {}
L14_1 = 744
L15_1 = 580
L16_1 = 855
L17_1 = 691
L13_1[1] = L14_1
L13_1[2] = L15_1
L13_1[3] = L16_1
L13_1[4] = L17_1
L14_1 = {}
L15_1 = 890
L16_1 = 580
L17_1 = 1002
L18_1 = 691
L14_1[1] = L15_1
L14_1[2] = L16_1
L14_1[3] = L17_1
L14_1[4] = L18_1
L15_1 = {}
L16_1 = 1035
L17_1 = 580
L18_1 = 1149
L19_1 = 691
L15_1[1] = L16_1
L15_1[2] = L17_1
L15_1[3] = L18_1
L15_1[4] = L19_1
L16_1 = {}
L17_1 = 452
L18_1 = 726
L19_1 = 563
L20_1 = 835
L16_1[1] = L17_1
L16_1[2] = L18_1
L16_1[3] = L19_1
L16_1[4] = L20_1
L17_1 = {}
L18_1 = 598
L19_1 = 726
L20_1 = 708
L21_1 = 835
L17_1[1] = L18_1
L17_1[2] = L19_1
L17_1[3] = L20_1
L17_1[4] = L21_1
L18_1 = {}
L19_1 = 744
L20_1 = 726
L21_1 = 855
L22_1 = 835
L18_1[1] = L19_1
L18_1[2] = L20_1
L18_1[3] = L21_1
L18_1[4] = L22_1
L19_1 = {}
L20_1 = 890
L21_1 = 726
L22_1 = 1002
L23_1 = 835
L19_1[1] = L20_1
L19_1[2] = L21_1
L19_1[3] = L22_1
L19_1[4] = L23_1
L20_1 = {}
L21_1 = 1035
L22_1 = 726
L23_1 = 1149
L24_1 = 835
L20_1[1] = L21_1
L20_1[2] = L22_1
L20_1[3] = L23_1
L20_1[4] = L24_1
L0_1[1] = L1_1
L0_1[2] = L2_1
L0_1[3] = L3_1
L0_1[4] = L4_1
L0_1[5] = L5_1
L0_1[6] = L6_1
L0_1[7] = L7_1
L0_1[8] = L8_1
L0_1[9] = L9_1
L0_1[10] = L10_1
L0_1[11] = L11_1
L0_1[12] = L12_1
L0_1[13] = L13_1
L0_1[14] = L14_1
L0_1[15] = L15_1
L0_1[16] = L16_1
L0_1[17] = L17_1
L0_1[18] = L18_1
L0_1[19] = L19_1
L0_1[20] = L20_1
ly_Range = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["长安城仓库"]
  L0_2()
  L0_2 = 0
  L1_2 = 1
  L2_2 = 25
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = _cmp
    L6_2 = Color
    L6_2 = L6_2["屏蔽"]
    L6_2 = L6_2["取消"]
    L5_2(L6_2)
    L5_2 = _find_tb_cx
    L6_2 = Color
    L6_2 = L6_2["跑商"]
    L6_2 = L6_2["二药"]
    L6_2 = L6_2["双击"]
    L7_2 = {}
    L8_2 = 10
    L9_2 = 15
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L8_2 = nil
    L9_2 = {}
    L10_2 = 203
    L11_2 = 303
    L12_2 = 887
    L13_2 = 848
    L9_2[1] = L10_2
    L9_2[2] = L11_2
    L9_2[3] = L12_2
    L9_2[4] = L13_2
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2)
    if L5_2 then
      L0_2 = L0_2 + 1
    else
      L5_2 = _tap
      L6_2 = 543
      L7_2 = 936
      L5_2(L6_2, L7_2)
      L5_2 = mSleep
      L6_2 = 600
      L5_2(L6_2)
    end
    if 1 < L0_2 then
      break
    end
    L5_2 = mSleep
    L6_2 = 300
    L5_2(L6_2)
  end
  L1_2 = _ENV["通用功能"]
  L1_2 = L1_2["关闭"]
  L1_2()
  while true do
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["起号"]
    L2_2 = L2_2["技能界面"]
    L1_2 = L1_2(L2_2)
    if L1_2 == false then
      L1_2 = _tap
      L2_2 = 1749
      L3_2 = 69
      L1_2(L2_2, L3_2)
      L1_2 = _ENV["_随机延时"]
      L2_2 = 411
      L1_2(L2_2)
    end
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["起号"]
    L2_2 = L2_2["辅助技能"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = mSleep
      L2_2 = 300
      L1_2(L2_2)
    end
    L1_2 = _find
    L2_2 = Color
    L2_2 = L2_2["起号"]
    L2_2 = L2_2["烹饪包子"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      L1_2 = _tap_tb
      L2_2 = 530
      L3_2 = 913
      L4_2 = 734
      L5_2 = 974
      L6_2 = 200
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
    end
    L1_2 = _find_tb
    L2_2 = Color
    L2_2 = L2_2["跑商"]
    L2_2 = L2_2["炼药界面"]
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = _cmp
    L2_2 = Color
    L2_2 = L2_2["屏蔽"]
    L2_2 = L2_2["取消"]
    L1_2(L2_2)
  end
  L1_2 = num
  if not L1_2 then
    L1_2 = 10
  end
  L2_2 = 1
  L3_2 = L1_2
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["跑商"]
    L7_2 = L7_2["炼药药品不足"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = 1
      L7_2 = 20
      L8_2 = 1
      for L9_2 = L6_2, L7_2, L8_2 do
        L10_2 = 1
        L11_2 = 4
        L12_2 = 1
        for L13_2 = L10_2, L11_2, L12_2 do
          L14_2 = _find_ps_tb
          L15_2 = Color
          L15_2 = L15_2["跑商"]
          L15_2 = L15_2["二药"]
          L15_2 = L15_2["单击"]
          L16_2 = nil
          L17_2 = {}
          L18_2 = ly_Range
          L18_2 = L18_2[L9_2]
          L18_2 = L18_2[1]
          L19_2 = ly_Range
          L19_2 = L19_2[L9_2]
          L19_2 = L19_2[2]
          L20_2 = ly_Range
          L20_2 = L20_2[L9_2]
          L20_2 = L20_2[3]
          L21_2 = ly_Range
          L21_2 = L21_2[L9_2]
          L21_2 = L21_2[4]
          L17_2[1] = L18_2
          L17_2[2] = L19_2
          L17_2[3] = L20_2
          L17_2[4] = L21_2
          L14_2 = L14_2(L15_2, L16_2, L17_2)
          if L14_2 then
            L14_2 = _Sleep
            L15_2 = 50
            L16_2 = 100
            L14_2(L15_2, L16_2)
          end
        end
        L10_2 = _tap_tb
        L11_2 = 371
        L12_2 = 9
        L13_2 = 1373
        L14_2 = 79
        L15_2 = 200
        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
        L10_2 = _cmp
        L11_2 = Color
        L11_2 = L11_2["跑商"]
        L11_2 = L11_2["炼药药品不足"]
        L10_2 = L10_2(L11_2)
        if L10_2 == false then
          break
        end
      end
    end
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["跑商"]
    L7_2 = L7_2["炼药药品不足"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      break
    end
    L6_2 = _find_ps_tb
    L7_2 = Color
    L7_2 = L7_2["跑商"]
    L7_2 = L7_2["活力不足"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      break
    end
    L6_2 = _tap_tb
    L7_2 = 1207
    L8_2 = 773
    L9_2 = 1474
    L10_2 = 845
    L6_2(L7_2, L8_2, L9_2, L10_2)
    L6_2 = _Sleep
    L7_2 = 300
    L8_2 = 400
    L6_2(L7_2, L8_2)
  end
  L2_2 = _ENV["通用功能"]
  L2_2 = L2_2["关闭"]
  L2_2()
  L2_2 = _ENV["_前往"]
  L2_2 = L2_2["长安城仓库"]
  L2_2()
  L2_2 = 1
  L3_2 = 25
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["屏蔽"]
    L7_2 = L7_2["取消"]
    L6_2(L7_2)
    L6_2 = _cmp
    L7_2 = Color
    L7_2 = L7_2["跑商"]
    L7_2 = L7_2["仓库有位置"]
    L6_2 = L6_2(L7_2)
    if L6_2 then
      L6_2 = _find_tb_cx
      L7_2 = Color
      L7_2 = L7_2["跑商"]
      L7_2 = L7_2["三药"]
      L7_2 = L7_2["双击"]
      L8_2 = {}
      L9_2 = 10
      L10_2 = 15
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L9_2 = nil
      L10_2 = {}
      L11_2 = 934
      L12_2 = 298
      L13_2 = 1615
      L14_2 = 846
      L10_2[1] = L11_2
      L10_2[2] = L12_2
      L10_2[3] = L13_2
      L10_2[4] = L14_2
      L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2)
      if L6_2 == false then
        L6_2 = _ENV["通用功能"]
        L6_2 = L6_2["关闭"]
        L6_2()
        L6_2 = true
        return L6_2
      end
    else
      L6_2 = _tap
      L7_2 = 543
      L8_2 = 936
      L6_2(L7_2, L8_2)
      L6_2 = mSleep
      L7_2 = 600
      L6_2(L7_2)
    end
    L6_2 = mSleep
    L7_2 = 300
    L6_2(L7_2)
  end
  L2_2 = _ENV["通用功能"]
  L2_2 = L2_2["关闭"]
  L2_2()
end

_ENV["炼药"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  while true do
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["起号"]
    L1_2 = L1_2["技能界面"]
    L0_2 = L0_2(L1_2)
    if L0_2 == false then
      L0_2 = _tap
      L1_2 = 1749
      L2_2 = 69
      L0_2(L1_2, L2_2)
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["符之术"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["师门技能"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
    end
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["到符之术"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      break
    end
    L0_2 = mSleep
    L1_2 = 100
    L0_2(L1_2)
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["屏蔽"]
    L1_2 = L1_2["取消"]
    L0_2(L1_2)
  end
  fxfxzcx = 0
  L0_2 = os
  L0_2 = L0_2.time
  L0_2 = L0_2()
  fxftime = L0_2
  while true do
    L0_2 = _find
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["选中飞行符"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = fxfxzcx
      L0_2 = L0_2 + 1
      fxfxzcx = L0_2
      L0_2 = _Sleep
      L1_2 = 100
      L2_2 = 150
      L0_2(L1_2, L2_2)
      L0_2 = 1
      L1_2 = 15
      L2_2 = 1
      for L3_2 = L0_2, L1_2, L2_2 do
        L4_2 = _tap
        L5_2 = 1537
        L6_2 = 943
        L4_2(L5_2, L6_2)
      end
    end
    L0_2 = _cmp
    L1_2 = Color
    L1_2 = L1_2["跑商"]
    L1_2 = L1_2["没蓝了"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["通用功能"]
      L0_2 = L0_2["关闭"]
      L0_2()
      L0_2 = true
      return L0_2
    end
    L0_2 = fxfxzcx
    if not (3 < L0_2) then
      L0_2 = os
      L0_2 = L0_2.time
      L0_2 = L0_2()
      L1_2 = fxftime
      L0_2 = L0_2 - L1_2
      if 30 < L0_2 then
        L0_2 = _ENV["通用功能"]
        L0_2 = L0_2["关闭"]
        L0_2()
        L0_2 = false
        return L0_2
      end
    else
      -- 替换 goto lbl_106，直接执行对应逻辑
    L0_2 = mSleep
    L1_2 = 100
    L0_2(L1_2)
  end
end

_ENV["制作飞行符"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  while true do
    L0_2 = _ENV["押镖屏蔽"]
    L0_2()
    L0_2 = _tap
    L1_2 = 647
    L2_2 = 806
    L3_2 = 1
    L4_2 = 50
    L0_2(L1_2, L2_2, L3_2, L4_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _tap
    L1_2 = 647
    L2_2 = 806
    L3_2 = 1
    L4_2 = 50
    L0_2(L1_2, L2_2, L3_2, L4_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _tap
    L1_2 = 647
    L2_2 = 806
    L3_2 = 1
    L4_2 = 50
    L0_2(L1_2, L2_2, L3_2, L4_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _tap
    L1_2 = 647
    L2_2 = 806
    L3_2 = 1
    L4_2 = 50
    L0_2(L1_2, L2_2, L3_2, L4_2)
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["寻路"]
    L0_2()
    L0_2 = _ENV["_返回"]
    L0_2 = L0_2["地图"]
    L1_2 = "长安城"
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _print
      L1_2 = "长安城"
      L0_2(L1_2)
      break
    end
    L0_2 = _ENV["通用功能"]
    L0_2 = L0_2["关闭"]
    L0_2()
  end
end

_ENV["酒店出长安"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["_前往"]
  L0_2 = L0_2["长安酒店2"]
  L0_2()
  while true do
    L0_2 = _ENV["制作飞行符"]
    L0_2 = L0_2()
    if L0_2 then
      L0_2 = _ENV["_回复"]
      L0_2 = L0_2["角色住店2"]
      L1_2 = true
      L0_2(L1_2)
    else
      L0_2 = _ENV["_回复"]
      L0_2 = L0_2["角色住店2"]
      L1_2 = true
      L0_2(L1_2)
      L0_2 = _ENV["酒店出长安"]
      L0_2()
      break
    end
  end
  -- 移除 lbl_14 和 lbl_20 标签
  L0_2 = _ENV["存物品"]
  L0_2()
end

_ENV["去做飞行符"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = _ENV["_功能"]
  L0_2 = L0_2["背包"]
  L1_2 = "open"
  L0_2(L1_2)
  L0_2 = 1
  L1_2 = 30
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["待丢弃烹饪"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = mSleep
      L5_2 = 500
      L4_2(L5_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["丢弃道具"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = mSleep
      L5_2 = 500
      L4_2(L5_2)
    end
    L4_2 = _find
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["丢弃道具确认"]
    L4_2 = L4_2(L5_2)
    if L4_2 then
      L4_2 = mSleep
      L5_2 = 900
      L4_2(L5_2)
    end
    L4_2 = _find_tb
    L5_2 = Color
    L5_2 = L5_2["起号"]
    L5_2 = L5_2["待丢弃烹饪"]
    L4_2 = L4_2(L5_2)
    if L4_2 == false then
      L4_2 = _cmp
      L5_2 = Color
      L5_2 = L5_2["背包"]
      L5_2 = L5_2["背包界面"]
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = _cmp
        L5_2 = Color
        L5_2 = L5_2["背包"]
        L5_2 = L5_2["确认背包界面"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["通用功能"]
          L4_2 = L4_2["关闭"]
          L4_2()
          break
        end
      end
    end
    L4_2 = mSleep
    L5_2 = 300
    L4_2(L5_2)
  end
end

_ENV["丢弃烹饪"] = L0_1
