local L0_1, L1_1, L2_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L0_2 = nil
  while true do
    L1_2 = _ENV["弹窗查看详情"]
    L1_2()
    L1_2 = getWordStock
    L2_2 = WordStock
    L3_2 = "长寿村"
    L4_2 = 154
    L5_2 = 37
    L6_2 = 308
    L7_2 = 78
    L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
    if not L1_2 then
      L1_2 = getNewYearView
      L2_2 = "长寿村"
      L1_2 = L1_2(L2_2)
      if L1_2 then
        -- 继续执行治疗逻辑
      else
        -- 替换 goto lbl_208，直接跳出循环
        break
      end
    end
    L1_2 = printLog
    L2_2 = "前往治疗宝宝忠诚"
    L1_2(L2_2)
    while true do
      L1_2 = getColour
      L2_2 = colorList
      L3_2 = "长寿小地图"
      L1_2 = L1_2(L2_2, L3_2)
      if L1_2 then
        L1_2 = randomClick
        L2_2 = 1
        L3_2 = 200
        L4_2 = 1253.8354430379748
        L5_2 = 548.7703349282297
        L1_2(L2_2, L3_2, L4_2, L5_2)
        L1_2 = randomClick
        L2_2 = 1
        L3_2 = 500
        L4_2 = 1253.8354430379748
        L5_2 = 548.7703349282297
        L1_2(L2_2, L3_2, L4_2, L5_2)
        L1_2 = randomClick
        L2_2 = 2
        L3_2 = 1000
        L4_2 = 1382
        L5_2 = 104
        L6_2 = 20
        L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
        break
      else
        L1_2 = randomClick
        L2_2 = 0
        L3_2 = 300
        L4_2 = 180
        L5_2 = 44
        L6_2 = 283
        L7_2 = 68
        L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
      end
    end
    while true do
      L1_2 = _ENV["弹窗查看详情"]
      L1_2()
      L1_2 = getAddressNum
      L1_2 = L1_2()
      if L0_2 == L1_2 then
        L1_2 = wordStock
        L1_2 = L1_2["隐藏摊位"]
        L1_2()
        L1_2 = true
        L2_2 = false
        L3_2 = 0
        while true do
          L4_2 = _ENV["弹窗查看详情"]
          L4_2()
          L4_2 = printLog
          L5_2 = "前往治疗宝宝忠诚"
          L4_2(L5_2)
          if L1_2 then
            L4_2 = randomClick
            L5_2 = 1
            L6_2 = 1000
            L7_2 = 883
            L8_2 = 462
            L4_2(L5_2, L6_2, L7_2, L8_2)
            L1_2 = false
            L4_2 = os
            L4_2 = L4_2.time
            L4_2 = L4_2()
            L3_2 = L4_2
          else
            L4_2 = getColour
            L5_2 = colorList
            L6_2 = "任务点击下"
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = randomClick
              L5_2 = 0
              L6_2 = 1000
              L7_2 = 1466
              L8_2 = 488
              L9_2 = 1785
              L10_2 = 558
              L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
              L2_2 = true
            else
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "任务点击下"
              L4_2 = L4_2(L5_2, L6_2)
              if not L4_2 then
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "关闭对话框"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  L4_2 = randomClick
                  L5_2 = 0
                  L6_2 = 1000
                  L7_2 = 925
                  L8_2 = 813
                  L9_2 = 1430
                  L10_2 = 952
                  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
              end
              else
                L4_2 = os
                L4_2 = L4_2.time
                L4_2 = L4_2()
                L4_2 = L4_2 - L3_2
                if 10 < L4_2 and L3_2 ~= 0 then
                  L4_2 = petTreatment
                  return L4_2()
                else
                  if L2_2 then
                    L4_2 = getColour
                    L5_2 = colorList
                    L6_2 = "动作按钮"
                    L4_2 = L4_2(L5_2, L6_2)
                    if L4_2 then
                      L4_2 = _ENV["师门任务返回"]
                      L4_2()
                      return
                    end
                  end
                  if L2_2 then
                    L4_2 = getColour
                    L5_2 = colorList
                    L6_2 = "道具按钮"
                    L4_2 = L4_2(L5_2, L6_2)
                    if L4_2 then
                      L4_2 = _ENV["师门任务返回"]
                      L4_2()
                      return
                    end
                  end
                  if L2_2 then
                    L4_2 = getColour
                    L5_2 = colorList
                    L6_2 = "设置按钮"
                    L4_2 = L4_2(L5_2, L6_2)
                    if L4_2 then
                      -- 移除 lbl_162 标签
                      L4_2 = _ENV["师门任务返回"]
                      L4_2()
                      return
                  end
                  else
                    L4_2 = {}
                    L5_2 = "00000000c0c3c007fe1c3c7fffe3c3cffffc3c3cffff03c3c70fe03c3c007e07ffeffff0ffffffff8ffffffffcffffffffc3e3e3e03c3c3c1c03c3c3c1c01e3c3c1c01e3c3c1c00e3c3e1c00f381f1ff0f300fbffcf700fbffcf701ffffef707f7c3ef78fe781ef7ff8781ef7ff0781ef7f80781ef7f04781ef780ff81ef700ff81ef700ff81ef700ff81ef780ff81ff781fbc3ff7fff3ffef3ffe3ffef3ff81ffcf1fc00ff0e000000004000000000000000000000000000000f0181c001f03c3c007f07c3c00ff0fc3c0fffbfc3c1fffff83cff8fff878ff0fff070f80fef0f0f01fcf0f000ff0f0f001fe0e1e003f81e1ef03f01c3ff3180001ff7000003ff70000fffef8007fffcfffffff0fffffffe0ffffffe01ffffffe03ef8007fc7cf0001fcfcf00003ff8f00003ff0f03c00fe0707c00fe07ffc03ff07ffc07ff07ffc3fcfc3f3eff87c003ffe03e001ffc03e001fc001f000f8000f00000000600000000000000000000000000000000070c700000f1ef00001f1ef00007f1ef0003fe1ef000ff81ef0fffe01ef1fffc01ef3fffc01ef3fffc01ef0007f81ef0003fc1ef00007c1ef00003c1ef0000001ef0000001effffffffeffffffffeffffffffeffffffffef80000fbef0000071ef00003f1ef00007f1ef000ffe1ef003ffc1ef1fffe01ef3fffc01ef3ffff01ef1f07f81ef0000fc1ef00007f1ef00001f1ef00000f1e70000001e30000000e00000000c0000000000000000000000000000000000001ffffffe07fffffffcffffffffcffffffffcf8000003ef00c700fef01ef01eef03ef03eef07ef03eefbf8f03eefff0f03cefff0f078efff0f0f8ef9f0f0f0ef0f0f1f0ef0f0ffe0ef0f0ffc0ef0ffff80ef0ffff00ef0ffff00ef0fffe00ef0f9ff00ef0f0ffc0ef0f0ffc0ef0f0f3e0ef0f0f1f0ef0f0f0f0ef0f0f0f0ef0f0f078ef0f0f03cef0e0f03eef0c0f03eef000f03eef000200ce70000000c$超级巫医$2889$36$157"
                    L4_2[1] = L5_2
                    L5_2 = addTSOcrDictEx
                    L6_2 = L4_2
                    L5_2 = L5_2(L6_2)
                    L6_2 = tsFindText
                    L7_2 = L5_2
                    L8_2 = "超级巫医"
                    L9_2 = 503
                    L10_2 = 261
                    L11_2 = 1790
                    L12_2 = 994
                    L13_2 = "D5DCE2 , 3E3732"
                    L14_2 = 90
                    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                    if 0 < L6_2 then
                      L8_2 = randomTap
                      L9_2 = L6_2 + 50
                      L10_2 = L7_2
                      L11_2 = 20
                      L8_2(L9_2, L10_2, L11_2)
                      L8_2 = mSleep
                      L9_2 = math
                      L9_2 = L9_2.random
                      L10_2 = 400
                      L11_2 = 600
                      L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L9_2(L10_2, L11_2)
                      L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
                    end
                  end
                end
              end
            end
          end
        end
      end
      L1_2 = getAddressNum
      L1_2 = L1_2()
      L0_2 = L1_2
      L1_2 = mSleep
      L2_2 = math
      L2_2 = L2_2.random
      L3_2 = 800
      L4_2 = 1500
      L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L2_2(L3_2, L4_2)
      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    end
    -- 移除 lbl_208 标签
  end
end

_ENV["原petTreatment"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  while true do
    L0_2 = _ENV["_返回"]
    L0_2 = L0_2["坐标"]
    L0_2, L1_2 = L0_2()
    L2_2 = 127 - L0_2
    L3_2 = 114 - L1_2
    L4_2 = math
    L4_2 = L4_2.abs
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    if L4_2 <= 5 then
      L4_2 = math
      L4_2 = L4_2.abs
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      if L4_2 <= 5 then
        L4_2 = _ENV["_功能"]
        L4_2 = L4_2["屏蔽"]
        L5_2 = 4
        L4_2(L5_2)
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "长寿村"
        L6_2 = L0_2
        L7_2 = L1_2
        L8_2 = 127
        L9_2 = 114
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        L1_2 = L5_2
        L0_2 = L4_2
        L4_2 = mSleep
        L5_2 = 1000
        L4_2(L5_2)
        L4_2 = tap
        L5_2 = L0_2
        L6_2 = L1_2
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 500
        L4_2(L5_2, L6_2)
        L4_2 = 1
        L5_2 = 40
        L6_2 = 1
        for L7_2 = L4_2, L5_2, L6_2 do
          L8_2 = _find_tb
          L9_2 = Color
          L9_2 = L9_2["出现重叠"]
          L8_2, L9_2, L10_2 = L8_2(L9_2)
          if L8_2 then
            L11_2 = L9_2 - 165
            L12_2 = L10_2 + 31
            L13_2 = L9_2 - 53
            L14_2 = L10_2 + 540
            L15_2 = _find_tb
            L16_2 = Color
            L16_2 = L16_2["回复"]
            L16_2 = L16_2["巫医重叠"]
            L17_2 = {}
            L18_2 = 0
            L19_2 = 0
            L17_2[1] = L18_2
            L17_2[2] = L19_2
            L18_2 = {}
            L19_2 = L11_2
            L20_2 = L12_2
            L21_2 = L13_2
            L22_2 = L14_2
            L18_2[1] = L19_2
            L18_2[2] = L20_2
            L18_2[3] = L21_2
            L18_2[4] = L22_2
            L15_2 = L15_2(L16_2, L17_2, L18_2)
            if L15_2 then
              L15_2 = mSleep
              L16_2 = 1000
              L15_2(L16_2)
            end
          end
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["起号"]
          L12_2 = L12_2["出现对话框"]
          L11_2 = L11_2(L12_2)
          if L11_2 then
            L11_2 = _find
            L12_2 = Color
            L12_2 = L12_2["起号"]
            L12_2 = L12_2["可选对话框"]
            L11_2 = L11_2(L12_2)
            if L11_2 == false then
              L11_2 = tap
              L12_2 = 963
              L13_2 = 857
              L11_2(L12_2, L13_2)
              L11_2 = _ENV["_随机延时"]
              L12_2 = 811
              L11_2(L12_2)
            end
          end
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2.npc
          L12_2 = L12_2["我要做其他事情"]
          L11_2 = L11_2(L12_2)
          if L11_2 then
            L11_2 = _Sleep
            L12_2 = 500
            L13_2 = 1000
            L11_2(L12_2, L13_2)
          end
          L11_2 = _find
          L12_2 = Color
          L12_2 = L12_2["回复"]
          L12_2 = L12_2["同时补满"]
          L11_2 = L11_2(L12_2)
          if L11_2 then
            L11_2 = _Sleep
            L12_2 = 500
            L13_2 = 1000
            L11_2(L12_2, L13_2)
            L11_2 = _cmp_cx
            L12_2 = Color
            L12_2 = L12_2["回复"]
            L12_2 = L12_2["关闭对话"]
            L13_2 = {}
            L14_2 = 50
            L15_2 = 60
            L13_2[1] = L14_2
            L13_2[2] = L15_2
            L11_2 = L11_2(L12_2, L13_2)
            if L11_2 then
              L11_2 = _ENV["_功能"]
              L11_2 = L11_2["屏蔽"]
              L12_2 = "close"
              L11_2(L12_2)
              L11_2 = _ENV["师门任务返回"]
              L11_2()
              return
            end
          end
          L11_2 = mSleep
          L12_2 = 50
          L11_2(L12_2)
        end
    end
    else
      L4_2 = _ENV["_前往"]
      L4_2 = L4_2["固定坐标"]
      L5_2 = "长寿村"
      L6_2 = 1245
      L7_2 = 558
      L4_2(L5_2, L6_2, L7_2)
    end
    L4_2 = mSleep
    L5_2 = 100
    L4_2(L5_2)
  end
end

petTreatment = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = _ENV["弹窗查看详情"]
    L0_2()
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "宠物忠诚"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      L0_2 = findMultiColorInRegionFuzzy
      L1_2 = 16260891
      L2_2 = "12|0|0xf8201c,19|6|0xf81c18,16|6|0xf81c18,15|7|0xf81e1b,9|1|0xf81c18,5|-6|0xf81c18,-2|9|0xf83633,-1|3|0xf86b67,5|-5|0xf81c18"
      L3_2 = 90
      L4_2 = 180
      L5_2 = 647
      L6_2 = 817
      L7_2 = 942
      L0_2, L1_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
      if 0 < L0_2 then
        L2_2 = randomClick
        L3_2 = 2
        L4_2 = 500
        L5_2 = L0_2 + 50
        L6_2 = L1_2 + 50
        L2_2(L3_2, L4_2, L5_2, L6_2)
      end
      L2_2 = tonumber
      L3_2 = _ENV["计算宠物忠诚"]
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L3_2()
      L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
      L3_2 = printLog
      L4_2 = "宠物忠诚剩余："
      L5_2 = L2_2
      L4_2 = L4_2 .. L5_2
      L3_2(L4_2)
      if 76 <= L2_2 then
        L3_2 = randomClick
        L4_2 = 2
        L5_2 = 500
        L6_2 = 1706
        L7_2 = 73
        L3_2(L4_2, L5_2, L6_2, L7_2)
        return
      else
        L3_2 = randomClick
        L4_2 = 2
        L5_2 = 500
        L6_2 = 1706
        L7_2 = 73
        L3_2(L4_2, L5_2, L6_2, L7_2)
        L3_2 = _ENV["_回复"]
        L3_2 = L3_2["宝宝巫医"]
        L4_2 = true
        return L3_2(L4_2)
      end
    else
      L0_2 = randomClick
      L1_2 = 2
      L2_2 = 300
      L3_2 = 1509
      L4_2 = 52
      L5_2 = 30
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
    end
  end
end

_ENV["宠物忠诚玲珑"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = findFeat
  L1_2 = color
  L1_2 = L1_2.dialogClose
  L0_2 = L0_2(L1_2)
  if 0 < L0_2 then
    L0_2 = findAndTap
    L1_2 = color
    L1_2 = L1_2.btn
    L1_2 = L1_2.veterinary
    L2_2 = "singleTap"
    L3_2 = 30
    L0_2 = L0_2(L1_2, L2_2, L3_2)
    if L0_2 then
      L0_2 = mSleep
      L1_2 = math
      L1_2 = L1_2.random
      L2_2 = 450
      L3_2 = 550
      L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
      L0_2(L1_2, L2_2, L3_2, L4_2)
    else
      L0_2 = findAndTap
      L1_2 = color
      L1_2 = L1_2.btn
      L1_2 = L1_2.joinDungeon
      L2_2 = "singleTap"
      L3_2 = 30
      L0_2 = L0_2(L1_2, L2_2, L3_2)
      if L0_2 then
        L0_2 = mSleep
        L1_2 = math
        L1_2 = L1_2.random
        L2_2 = 450
        L3_2 = 550
        L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
        L0_2(L1_2, L2_2, L3_2, L4_2)
      else
        L0_2 = cleanupINF
        L0_2()
      end
    end
  end
end

dialogProcess = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = findAndTap
  L1_2 = color
  L1_2 = L1_2.btn
  L1_2 = L1_2.teamInvite
  L2_2 = "singleTap"
  L3_2 = 30
  L0_2 = L0_2(L1_2, L2_2, L3_2)
  if L0_2 then
    L0_2 = mSleep
    L1_2 = math
    L1_2 = L1_2.random
    L2_2 = 450
    L3_2 = 550
    L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2)
  end
end

teamInvite = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = findFeat
  L1_2 = color
  L1_2 = L1_2.btn
  L1_2 = L1_2.tipsMove
  L0_2, L1_2 = L0_2(L1_2)
  if 0 < L0_2 then
    L2_2 = {}
    L3_2 = L0_2
    L4_2 = L1_2
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    tipsPos = L2_2
    L2_2 = {}
    L3_2 = 390
    L4_2 = 30
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    movePos = L2_2
    L2_2 = {}
    L3_2 = tipsPos
    L4_2 = movePos
    L2_2[1] = L3_2
    L2_2[2] = L4_2
    slideTips = L2_2
    L2_2 = slide
    L3_2 = slideTips
    L2_2(L3_2)
    L2_2 = mSleep
    L3_2 = math
    L3_2 = L3_2.random
    L4_2 = 450
    L5_2 = 550
    L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2)
    L2_2(L3_2, L4_2, L5_2, L6_2)
  end
end

moveTips = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = findAndTap
  L1_2 = color
  L1_2 = L1_2.btn
  L1_2 = L1_2.shanheReady
  L2_2 = "singleTap"
  L3_2 = 30
  L0_2 = L0_2(L1_2, L2_2, L3_2)
  if L0_2 then
    L0_2 = mSleep
    L1_2 = math
    L1_2 = L1_2.random
    L2_2 = 450
    L3_2 = 550
    L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
    L0_2(L1_2, L2_2, L3_2, L4_2)
  end
end

shanheReady = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  while true do
    L0_2 = dialogProcess
    L0_2()
    L0_2 = teamInvite
    L0_2()
    L0_2 = shanheReady
    L0_2()
    L0_2 = moveTips
    L0_2()
    L0_2 = fightSystem
    L0_2()
  end
end

startSlacking = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  tradingMap = ""
  tradingMerchant = ""
  chequeBalance = 0
  L0_2 = {}
  priceList = L0_2
  L0_2 = {}
  myBag = L0_2
end

tradingInit = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = getNetTime
  L0_2 = L0_2()
  L1_2 = os
  L1_2 = L1_2.date
  L2_2 = "%M"
  L3_2 = L0_2
  L1_2 = L1_2(L2_2, L3_2)
  L2_2 = tonumber
  L3_2 = L1_2
  L2_2 = L2_2(L3_2)
  L2_2 = L2_2 % 10
  L2_2 = 10 - L2_2
  if 1 < L2_2 then
    L2_2 = false
    return L2_2
  else
    L2_2 = true
    return L2_2
  end
end

refreshInaMinute = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L0_2 = scrnLog
  L1_2 = "检查背包商品"
  L0_2(L1_2)
  L0_2 = {}
  L1_2 = 900
  L2_2 = 260
  L3_2 = 1570
  L4_2 = 796
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L1_2 = _ENV["_功能"]
  L1_2 = L1_2["背包"]
  L2_2 = "open"
  L1_2(L2_2)
  L1_2 = pairs
  L2_2 = color
  L2_2 = L2_2.goods
  L1_2, L2_2, L3_2 = L1_2(L2_2)
  for L4_2, L5_2 in L1_2, L2_2, L3_2 do
    L6_2 = pairs
    L7_2 = L5_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L11_2 = {}
      L12_2 = L10_2[1]
      L13_2 = L10_2[2]
      L14_2 = L10_2[3]
      L15_2 = L0_2[1]
      L16_2 = L0_2[2]
      L17_2 = L0_2[3]
      L18_2 = L0_2[4]
      L11_2[1] = L12_2
      L11_2[2] = L13_2
      L11_2[3] = L14_2
      L11_2[4] = L15_2
      L11_2[5] = L16_2
      L11_2[6] = L17_2
      L11_2[7] = L18_2
      L12_2 = findFeat
      L13_2 = L11_2
      L12_2 = L12_2(L13_2)
      if 0 < L12_2 then
        L12_2 = ""
        if L4_2 == "DF" then
          L12_2 = "BJLZ"
        elseif L4_2 == "ALG" or L4_2 == "CAC" then
          L12_2 = "CSC"
        elseif L4_2 == "CSC" then
          L13_2 = _ENV["北俱地府"]
          if L13_2 then
            L12_2 = "CAC"
          else
            L12_2 = "ALG"
          end
        elseif L4_2 == "BJLZ" then
          L12_2 = "DF"
        end
        L13_2 = nLog
        L14_2 = L9_2
        L13_2(L14_2)
        L13_2 = myBag
        L14_2 = {}
        L14_2.from = L4_2
        L14_2.to = L12_2
        L13_2[L9_2] = L14_2
      end
    end
  end
  L1_2 = closeAllWnd
  L1_2()
  L1_2 = closeScrnLog
  L2_2 = "检查背包商品"
  L1_2(L2_2)
end

checkMyBag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = findFeat
    L1_2 = color
    L1_2 = L1_2.merchantTask
    L0_2 = L0_2(L1_2)
    if 0 < L0_2 then
      L0_2 = _cmp_tb
      L1_2 = Color
      L1_2 = L1_2["初始化"]
      L1_2 = L1_2["已展开任务栏"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        needDodge = true
        L0_2 = true
        return L0_2
    end
    else
      L0_2 = _find_tb
      L1_2 = Color
      L1_2 = L1_2["通用"]
      L1_2 = L1_2["无用任务删除"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find_tb
        L1_2 = Color
        L1_2 = L1_2["起号"]
        L1_2 = L1_2["绘卷"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _print
          L1_2 = "删除任务"
          L0_2(L1_2)
          L0_2 = _ENV["通用功能"]
          L0_2 = L0_2["任务栏清理一次"]
          L0_2()
      end
      else
        L0_2 = findFeat
        L1_2 = color
        L1_2 = L1_2.merchantTask
        L0_2 = L0_2(L1_2)
        if L0_2 < 0 then
          L0_2 = _cmp_tb
          L1_2 = Color
          L1_2 = L1_2["初始化"]
          L1_2 = L1_2["已展开任务栏"]
          L0_2 = L0_2(L1_2)
          if L0_2 then
            L0_2 = false
            return L0_2
        end
        else
          L0_2 = _cmp_tb
          L1_2 = Color
          L1_2 = L1_2["初始化"]
          L1_2 = L1_2["已展开任务栏"]
          L0_2 = L0_2(L1_2)
          if L0_2 == false then
            L0_2 = findFeat
            L1_2 = color
            L1_2 = L1_2.VERINF
            L1_2 = L1_2.VERmoveWord
            L0_2 = L0_2(L1_2)
            if 0 < L0_2 then
              L0_2 = _ENV["_验证"]
              L0_2 = L0_2["漂浮2"]
              L0_2()
            else
              L0_2 = findFeat
              L1_2 = color
              L1_2 = L1_2.VERINF
              L1_2 = L1_2.VERidiom
              L0_2 = L0_2(L1_2)
              if 0 < L0_2 then
                L0_2 = _ENV["_验证"]
                L0_2 = L0_2["成语"]
                L0_2 = L0_2["验证"]
                L0_2()
              else
                L0_2 = _cmp_tb
                L1_2 = Color
                L1_2 = L1_2["初始化"]
                L1_2 = L1_2["展开任务栏"]
                L0_2(L1_2)
                L0_2 = mSleep
                L1_2 = math
                L1_2 = L1_2.random
                L2_2 = 450
                L3_2 = 550
                L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2, L3_2)
                L0_2(L1_2, L2_2, L3_2, L4_2)
              end
            end
            L0_2 = cleanupINF
            L0_2()
          end
        end
      end
    end
  end
end

checkMerchantTask = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = tsOcrText
  L1_2 = blackPrice
  L2_2 = 1476
  L3_2 = 868
  L4_2 = 1658
  L5_2 = 906
  L6_2 = "292B2E , 282A2D"
  L7_2 = 85
  L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  if L0_2 ~= "" then
    L1_2 = tonumber
    L2_2 = L0_2
    L1_2 = L1_2(L2_2)
    chequeBalance = L1_2
    L1_2 = true
    return L1_2
  end
  L1_2 = tsOcrText
  L2_2 = bluePrice
  L3_2 = 1476
  L4_2 = 868
  L5_2 = 1658
  L6_2 = 906
  L7_2 = "191AFA , 181909"
  L8_2 = 85
  L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  if L1_2 ~= "" then
    L2_2 = tonumber
    L3_2 = L1_2
    L2_2 = L2_2(L3_2)
    chequeBalance = L2_2
    L2_2 = true
    return L2_2
  end
  L2_2 = tsOcrText
  L3_2 = bluePrice
  L4_2 = 1476
  L5_2 = 868
  L6_2 = 1658
  L7_2 = 906
  L8_2 = "249D29 , 230928"
  L9_2 = 85
  L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  if L2_2 ~= "" then
    L3_2 = tonumber
    L4_2 = L2_2
    L3_2 = L3_2(L4_2)
    chequeBalance = L3_2
    L3_2 = true
    return L3_2
  end
  L3_2 = false
  return L3_2
end

checkBalance = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L2_2 = ""
  L3_2 = ""
  if A1_2 == "A" then
    L2_2 = "商人"
  elseif A1_2 == "B" then
    L2_2 = "货商"
  end
  if A0_2 == "CAC" then
    L3_2 = "长安"
  elseif A0_2 == "DF" then
    L3_2 = "地府"
  elseif A0_2 == "BJLZ" then
    L3_2 = "北俱"
  elseif A0_2 == "ALG" then
    L3_2 = "傲来"
  elseif A0_2 == "CSC" then
    L3_2 = "长寿"
  end
  while true do
    L4_2 = scrnLog
    L5_2 = "前往"
    L6_2 = L3_2
    L7_2 = L2_2
    L5_2 = L5_2 .. L6_2 .. L7_2
    L4_2(L5_2)
    L4_2 = findFeat
    L5_2 = color
    L5_2 = L5_2.btn
    L5_2 = L5_2.buyIn
    L4_2 = L4_2(L5_2)
    if 0 < L4_2 then
      L4_2 = _find_tb
      L5_2 = Color
      L5_2 = L5_2["跑商"]
      L5_2 = L5_2["横幅等待"]
      L4_2 = L4_2(L5_2)
      if L4_2 == false then
        L4_2 = closeScrnLog
        L5_2 = "前往"
        L6_2 = L3_2
        L7_2 = L2_2
        L5_2 = L5_2 .. L6_2 .. L7_2
        L4_2(L5_2)
        tradingMap = A0_2
        tradingMerchant = A1_2
        L4_2 = checkBalance
        L4_2()
        L4_2 = true
        return L4_2
    end
    else
      L4_2 = findFeat
      L5_2 = color
      L5_2 = L5_2.btn
      L5_2 = L5_2.buyIn
      L4_2 = L4_2(L5_2)
      if 0 < L4_2 then
        L4_2 = _find_tb
        L5_2 = Color
        L5_2 = L5_2["跑商"]
        L5_2 = L5_2["横幅等待"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _Sleep
          L5_2 = 300
          L6_2 = 500
          L4_2(L5_2, L6_2)
      end
      else
        L4_2 = findFeat
        L5_2 = color
        L5_2 = L5_2.merchant
        L5_2 = L5_2[A1_2]
        L4_2, L5_2 = L4_2(L5_2)
        L6_2 = findFeat
        L7_2 = color
        L7_2 = L7_2[A0_2]
        L7_2 = L7_2[A1_2]
        L6_2, L7_2 = L6_2(L7_2)
        L8_2 = getCurMap
        L8_2 = L8_2()
        if 0 < L4_2 and L8_2 == A0_2 then
          if A0_2 == "ALG" or A0_2 == "CAC" or A0_2 == "CSC" then
            L9_2 = _ENV["UI_跑商_屏蔽摆摊"]
            if L9_2 then
              L9_2 = _ENV["跑商屏蔽"]
              L9_2()
            else
              L9_2 = math
              L9_2 = L9_2.random
              L10_2 = 1
              L11_2 = 10
              L9_2 = L9_2(L10_2, L11_2)
              if 9 < L9_2 then
                L10_2 = _ENV["跑商屏蔽"]
                L10_2()
              end
            end
          end
          L9_2 = {}
          L10_2 = L4_2 - 15
          L11_2 = L5_2 - 70
          L9_2[1] = L10_2
          L9_2[2] = L11_2
          L10_2 = singleTap
          L11_2 = L9_2
          L12_2 = 7
          L10_2(L11_2, L12_2)
          L10_2 = mSleep
          L11_2 = 250
          L10_2(L11_2)
          L10_2 = mSleep
          L11_2 = _ENV["卡顿掉帧"]
          L11_2 = L11_2 / 2
          L10_2(L11_2)
          L10_2 = _find_tb
          L11_2 = Color
          L11_2 = L11_2["出现重叠"]
          L10_2, L11_2, L12_2 = L10_2(L11_2)
          if L10_2 then
            L13_2 = L11_2 - 20
            L14_2 = L12_2 + 20
            L15_2 = L11_2 + 100
            L16_2 = L12_2 + 540
            L17_2 = _find_tb
            L18_2 = Color
            L18_2 = L18_2["跑商"]
            L18_2 = L18_2["商人重叠"]
            L19_2 = {}
            L20_2 = 0
            L21_2 = 0
            L19_2[1] = L20_2
            L19_2[2] = L21_2
            L20_2 = {}
            L21_2 = L13_2
            L22_2 = L14_2
            L23_2 = L15_2
            L24_2 = L16_2
            L20_2[1] = L21_2
            L20_2[2] = L22_2
            L20_2[3] = L23_2
            L20_2[4] = L24_2
            L17_2 = L17_2(L18_2, L19_2, L20_2)
            if L17_2 then
              L17_2 = _Sleep
              L18_2 = 100
              L19_2 = 200
              L17_2(L18_2, L19_2)
            end
          end
          L13_2 = _find_cx
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["我要交易"]
          L15_2 = {}
          L16_2 = 70
          L17_2 = 50
          L15_2[1] = L16_2
          L15_2[2] = L17_2
          L16_2 = {}
          L17_2 = math
          L17_2 = L17_2.random
          L18_2 = 10
          L19_2 = 250
          L17_2 = L17_2(L18_2, L19_2)
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 1
          L20_2 = 10
          L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2 = L18_2(L19_2, L20_2)
          L16_2[1] = L17_2
          L16_2[2] = L18_2
          L16_2[3] = L19_2
          L16_2[4] = L20_2
          L16_2[5] = L21_2
          L16_2[6] = L22_2
          L16_2[7] = L23_2
          L16_2[8] = L24_2
          L16_2[9] = L25_2
          L13_2 = L13_2(L14_2, L15_2, L16_2)
          if L13_2 then
          end
          L13_2 = _find_cx
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["跑商交易界面"]
          L15_2 = {}
          L16_2 = 70
          L17_2 = 50
          L15_2[1] = L16_2
          L15_2[2] = L17_2
          L13_2 = L13_2(L14_2, L15_2)
          if L13_2 then
          end
          L13_2 = _find
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["没有业务往来"]
          L13_2 = L13_2(L14_2)
          if L13_2 then
            L13_2 = _ENV["_游戏"]
            L13_2 = L13_2["退出到登录界面"]
            L13_2()
            L13_2 = dialog
            L14_2 = "商人掉了"
            L15_2 = time
            L13_2(L14_2, L15_2)
            L13_2 = lua_exit
            L13_2()
          end
        elseif 0 < L6_2 and L8_2 == A0_2 then
          L9_2 = scrnLog
          L10_2 = "寻找商人"
          L9_2(L10_2)
          L9_2 = nil
          if A0_2 == "BJLZ" then
            if A1_2 == "A" then
              L10_2 = {}
              L11_2 = L6_2 - 399
              L12_2 = L7_2 - 73
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            else
              L10_2 = {}
              L11_2 = L6_2 - 169
              L12_2 = L7_2 + 62
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            end
          elseif A0_2 == "DF" then
            if A1_2 == "A" then
              L10_2 = {}
              L11_2 = L6_2 + 73
              L12_2 = L7_2 + 164
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            else
              L10_2 = {}
              L11_2 = L6_2 - 238
              L12_2 = L7_2 + 154
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            end
          elseif A0_2 == "ALG" then
            if A1_2 == "A" then
              L10_2 = {}
              L11_2 = L6_2 - 71
              L12_2 = L7_2 - 306
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            else
              L10_2 = {}
              L11_2 = L6_2 - 60
              L12_2 = L7_2 - 116
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            end
          elseif A0_2 == "CSC" then
            if A1_2 == "A" then
              L10_2 = {}
              L11_2 = L6_2 + 216
              L12_2 = L7_2 - 254
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            else
              L10_2 = {}
              L11_2 = L6_2 + 440
              L12_2 = L7_2 - 83
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            end
          elseif A0_2 == "CAC" then
            if A1_2 == "A" then
              L10_2 = {}
              L11_2 = L6_2 + 32
              L12_2 = L7_2 - 232
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            else
              L10_2 = {}
              L11_2 = L6_2 - 331
              L12_2 = L7_2 - 268
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L9_2 = L10_2
            end
          end
          L10_2 = singleTap
          L11_2 = L9_2
          L12_2 = 5
          L10_2(L11_2, L12_2)
          L10_2 = mSleep
          L11_2 = 250
          L10_2(L11_2)
          L10_2 = mSleep
          L11_2 = _ENV["卡顿掉帧"]
          L11_2 = L11_2 / 2
          L10_2(L11_2)
          L10_2 = _find_tb
          L11_2 = Color
          L11_2 = L11_2["出现重叠"]
          L10_2, L11_2, L12_2 = L10_2(L11_2)
          if L10_2 then
            L13_2 = L11_2 - 20
            L14_2 = L12_2 + 20
            L15_2 = L11_2 + 100
            L16_2 = L12_2 + 540
            L17_2 = _find_tb
            L18_2 = Color
            L18_2 = L18_2["跑商"]
            L18_2 = L18_2["商人重叠"]
            L19_2 = {}
            L20_2 = 0
            L21_2 = 0
            L19_2[1] = L20_2
            L19_2[2] = L21_2
            L20_2 = {}
            L21_2 = L13_2
            L22_2 = L14_2
            L23_2 = L15_2
            L24_2 = L16_2
            L20_2[1] = L21_2
            L20_2[2] = L22_2
            L20_2[3] = L23_2
            L20_2[4] = L24_2
            L17_2 = L17_2(L18_2, L19_2, L20_2)
            if L17_2 then
              L17_2 = _Sleep
              L18_2 = 100
              L19_2 = 200
              L17_2(L18_2, L19_2)
            end
          end
          L13_2 = _find_cx
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["我要交易"]
          L15_2 = {}
          L16_2 = 70
          L17_2 = 50
          L15_2[1] = L16_2
          L15_2[2] = L17_2
          L16_2 = {}
          L17_2 = math
          L17_2 = L17_2.random
          L18_2 = 10
          L19_2 = 250
          L17_2 = L17_2(L18_2, L19_2)
          L18_2 = math
          L18_2 = L18_2.random
          L19_2 = 1
          L20_2 = 10
          L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2 = L18_2(L19_2, L20_2)
          L16_2[1] = L17_2
          L16_2[2] = L18_2
          L16_2[3] = L19_2
          L16_2[4] = L20_2
          L16_2[5] = L21_2
          L16_2[6] = L22_2
          L16_2[7] = L23_2
          L16_2[8] = L24_2
          L16_2[9] = L25_2
          L13_2 = L13_2(L14_2, L15_2, L16_2)
          if L13_2 then
          end
          L13_2 = _find_cx
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["跑商交易界面"]
          L15_2 = {}
          L16_2 = 70
          L17_2 = 50
          L15_2[1] = L16_2
          L15_2[2] = L17_2
          L13_2 = L13_2(L14_2, L15_2)
          if L13_2 then
          end
          L13_2 = _find
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["没有业务往来"]
          L13_2 = L13_2(L14_2)
          if L13_2 then
            L13_2 = _ENV["_游戏"]
            L13_2 = L13_2["退出到登录界面"]
            L13_2()
            L13_2 = dialog
            L14_2 = "商人掉了"
            L15_2 = time
            L13_2(L14_2, L15_2)
            L13_2 = lua_exit
            L13_2()
          end
          L13_2 = _find
          L14_2 = Color
          L14_2 = L14_2["跑商"]
          L14_2 = L14_2["商人红"]
          L13_2 = L13_2(L14_2)
          if L13_2 then
            L13_2 = _ENV["通用功能"]
            L13_2 = L13_2["关闭"]
            L13_2()
            L13_2 = {}
            L14_2 = 950
            L15_2 = 545
            L13_2[1] = L14_2
            L13_2[2] = L15_2
            L14_2 = singleTap
            L15_2 = L13_2
            L16_2 = 30
            L14_2(L15_2, L16_2)
            L14_2 = mSleep
            L15_2 = math
            L15_2 = L15_2.random
            L16_2 = 300
            L17_2 = 550
            L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2 = L15_2(L16_2, L17_2)
            L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
            L14_2 = goTo
            L15_2 = constPos
            L15_2 = L15_2.merchantPos
            L15_2 = L15_2[A1_2]
            L15_2 = L15_2[A0_2]
            L16_2 = A0_2
            L14_2(L15_2, L16_2)
          end
        else
          L9_2 = math
          L9_2 = L9_2.random
          L10_2 = 1
          L11_2 = 10
          L9_2 = L9_2(L10_2, L11_2)
          if 8 < L9_2 then
            L10_2 = {}
            L11_2 = 950
            L12_2 = 545
            L10_2[1] = L11_2
            L10_2[2] = L12_2
            L11_2 = singleTap
            L12_2 = L10_2
            L13_2 = 2
            L11_2(L12_2, L13_2)
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 100
            L14_2 = 150
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
          end
          if A0_2 == "CSC" then
            L10_2 = _find
            L11_2 = Color
            L11_2 = L11_2["跑商"]
            L11_2 = L11_2["误入cs杂货店"]
            L10_2 = L10_2(L11_2)
            if L10_2 then
              L10_2 = tap
              L11_2 = 592
              L12_2 = 874
              L10_2(L11_2, L12_2)
              L10_2 = mSleep
              L11_2 = 3000
              L10_2(L11_2)
            end
          end
          L10_2 = goTo
          L11_2 = constPos
          L11_2 = L11_2.merchantPos
          L11_2 = L11_2[A1_2]
          L11_2 = L11_2[A0_2]
          L12_2 = A0_2
          L10_2(L11_2, L12_2)
          L10_2 = _ENV["moveJudge押镖"]
          L11_2 = 220
          L10_2(L11_2)
          if A0_2 == "ALG" or A0_2 == "CAC" or A0_2 == "CSC" then
            L10_2 = _ENV["UI_跑商_屏蔽摆摊"]
            if L10_2 then
              L10_2 = _ENV["跑商屏蔽"]
              L10_2()
            else
              L10_2 = math
              L10_2 = L10_2.random
              L11_2 = 1
              L12_2 = 10
              L10_2 = L10_2(L11_2, L12_2)
              if 9 < L10_2 then
                L11_2 = _ENV["跑商屏蔽"]
                L11_2()
              end
            end
          end
        end
      end
    end
  end
end

openTrading = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  L0_2 = scrnLog
  L1_2 = "查看商品价格"
  L0_2(L1_2)
  L0_2 = {}
  L1_2 = 251
  L2_2 = 180
  L3_2 = 915
  L4_2 = 315
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L1_2 = {}
  L2_2 = 408
  L3_2 = 366
  L4_2 = 506
  L5_2 = 464
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L2_2 = tradingMap
  if L2_2 ~= "DF" then
    L2_2 = tradingMap
    if L2_2 ~= "ALG" then
      L2_2 = tradingMap
      if L2_2 ~= "CSC" then
        L2_2 = tradingMap
        if L2_2 == "BJLZ" then
          L2_2 = _ENV["北俱地府"]
          if L2_2 then
            -- 替换 goto lbl_40，直接执行对应逻辑
            L2_2 = nLog
            L3_2 = "当前地图："
            L4_2 = tradingMap
            L3_2 = L3_2 .. L4_2
            L2_2(L3_2)
            return
          end
        end
        L2_2 = tradingMap
        if L2_2 == "CAC" then
          L2_2 = _ENV["北俱地府"]
          if L2_2 then
            -- 替换 goto lbl_40，直接执行对应逻辑
            L2_2 = nLog
            L3_2 = "当前地图："
            L4_2 = tradingMap
            L3_2 = L3_2 .. L4_2
            L2_2(L3_2)
            return
          end
        end
        L2_2 = _ENV["UI_跑商模式"]
        if L2_2 == "联动跑商" then
          -- 继续执行后续逻辑
        else
          -- 替换 goto lbl_214，直接跳转到对应逻辑
          L2_2 = nLog
          L3_2 = "check sell1"
          L2_2(L3_2)
          return
        end
      end
    end
  end
  -- 移除 lbl_40 标签，这部分逻辑已经在前面处理过了
  L2_2 = nLog
  L3_2 = "当前地图："
  L4_2 = tradingMap
  L3_2 = L3_2 .. L4_2
  L2_2(L3_2)
  L2_2 = pairs
  L3_2 = color
  L3_2 = L3_2.goods
  L4_2 = tradingMap
  L3_2 = L3_2[L4_2]
  L2_2, L3_2, L4_2 = L2_2(L3_2)
  for L5_2, L6_2 in L2_2, L3_2, L4_2 do
    L7_2 = _ENV["UI_抢货价钱"]
    if L7_2 == "抢货不看价钱" then
      L7_2 = priceList
      L7_2 = L7_2[L5_2]
      if L7_2 == nil then
        L7_2 = priceList
        L8_2 = {}
        L7_2[L5_2] = L8_2
      end
      L7_2 = priceList
      L7_2 = L7_2[L5_2]
      L8_2 = tradingMerchant
      L9_2 = proposedPrice
      L9_2 = L9_2[L5_2]
      L9_2 = L9_2 - 100
      L7_2[L8_2] = L9_2
    else
      L7_2 = {}
      L8_2 = L6_2[1]
      L9_2 = L6_2[2]
      L10_2 = L6_2[3]
      L11_2 = L0_2[1]
      L12_2 = L0_2[2]
      L13_2 = L0_2[3]
      L14_2 = L0_2[4]
      L7_2[1] = L8_2
      L7_2[2] = L9_2
      L7_2[3] = L10_2
      L7_2[4] = L11_2
      L7_2[5] = L12_2
      L7_2[6] = L13_2
      L7_2[7] = L14_2
      L8_2 = {}
      L9_2 = L6_2[1]
      L10_2 = L6_2[2]
      L11_2 = L6_2[3]
      L12_2 = L1_2[1]
      L13_2 = L1_2[2]
      L14_2 = L1_2[3]
      L15_2 = L1_2[4]
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L8_2[3] = L11_2
      L8_2[4] = L12_2
      L8_2[5] = L13_2
      L8_2[6] = L14_2
      L8_2[7] = L15_2
      L9_2 = findAndTap
      L10_2 = L7_2
      L11_2 = "singleTap"
      L12_2 = 20
      L9_2 = L9_2(L10_2, L11_2, L12_2)
      if L9_2 then
        L9_2 = nLog
        L10_2 = "选择物品"
        L9_2(L10_2)
        repeat
          L9_2 = 1
          L10_2 = 20
          L11_2 = 1
          for L12_2 = L9_2, L10_2, L11_2 do
            L13_2 = mSleep
            L14_2 = math
            L14_2 = L14_2.random
            L15_2 = 150
            L16_2 = 160
            L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
            L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
            L13_2 = mSleep
            L14_2 = _ENV["卡顿掉帧"]
            L14_2 = L14_2 / 5
            L13_2(L14_2)
            L13_2 = findFeat
            L14_2 = L8_2
            L13_2 = L13_2(L14_2)
            if 0 < L13_2 then
              break
            end
            L13_2 = findAndTap
            L14_2 = L7_2
            L15_2 = "singleTap"
            L16_2 = 20
            L13_2(L14_2, L15_2, L16_2)
            L13_2 = mSleep
            L14_2 = math
            L14_2 = L14_2.random
            L15_2 = 300
            L16_2 = 350
            L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L14_2(L15_2, L16_2)
            L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
            L13_2 = mSleep
            L14_2 = _ENV["卡顿掉帧"]
            L14_2 = L14_2 / 5
            L13_2(L14_2)
          end
          L9_2 = findFeat
          L10_2 = L8_2
          L9_2 = L9_2(L10_2)
          if 0 < L9_2 then
            -- 继续执行后续逻辑
          else
            -- 替换 goto lbl_186，直接执行对应逻辑
            L9_2 = priceList
            L9_2 = L9_2[L5_2]
            if L9_2 == nil then
              L9_2 = priceList
              L10_2 = {}
              L9_2[L5_2] = L10_2
            end
            L9_2 = priceList
            L9_2 = L9_2[L5_2]
            L10_2 = L6_2
            L9_2[L10_2] = 999999
            return
          end
          L9_2 = nLog
          L10_2 = L5_2
          L9_2(L10_2)
          L9_2 = tsOcrText
          L10_2 = blackPrice
          L11_2 = 353
          L12_2 = 806
          L13_2 = 537
          L14_2 = 847
          L15_2 = "292B2E , 282A2D"
          L16_2 = 85
          L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          if L9_2 == "" then
            L10_2 = tsOcrText
            L11_2 = bluePrice
            L12_2 = 353
            L13_2 = 806
            L14_2 = 537
            L15_2 = 847
            L16_2 = "191AFA , 181909"
            L17_2 = 85
            L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            L9_2 = L10_2
          end
        until L9_2 ~= ""
        L10_2 = nLog
        L11_2 = "进购价钱:"
        L12_2 = L9_2
        L11_2 = L11_2 .. L12_2
        L10_2(L11_2)
        L10_2 = priceList
        L10_2 = L10_2[L5_2]
        if L10_2 == nil then
          L10_2 = priceList
          L11_2 = {}
          L10_2[L5_2] = L11_2
        end
        L10_2 = priceList
        L10_2 = L10_2[L5_2]
        L11_2 = tradingMerchant
        L12_2 = tonumber
        L13_2 = L9_2
        L12_2 = L12_2(L13_2)
        L10_2[L11_2] = L12_2
        -- 替换 goto lbl_212，直接跳到函数结束
      else
        -- 移除 lbl_186 标签，这部分逻辑已经在前面处理过了
        L9_2 = priceList
        L9_2 = L9_2[L5_2]
        if L9_2 == nil then
          L9_2 = priceList
          L10_2 = {}
          L9_2[L5_2] = L10_2
        end
        L9_2 = priceList
        L9_2 = L9_2[L5_2]
        L10_2 = tradingMerchant
        L9_2[L10_2] = 999999
        L9_2 = nLog
        L10_2 = "ver failed"
        L9_2(L10_2)
      else
        L9_2 = priceList
        L9_2 = L9_2[L5_2]
        if L9_2 == nil then
          L9_2 = priceList
          L10_2 = {}
          L9_2[L5_2] = L10_2
        end
        L9_2 = priceList
        L9_2 = L9_2[L5_2]
        L10_2 = tradingMerchant
        L9_2[L10_2] = 999999
      end
    end
    -- 移除 lbl_212 标签
  end
  -- 移除 lbl_214 标签，这部分逻辑已经在前面处理过了
  L2_2 = nLog
  L3_2 = "check sell1"
  L2_2(L3_2)
  L2_2 = {}
  L3_2 = 999
  L4_2 = 184
  L5_2 = 1666
  L6_2 = 718
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L3_2 = {}
  L4_2 = 408
  L5_2 = 366
  L6_2 = 506
  L7_2 = 464
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L4_2 = pairs
  L5_2 = myBag
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = nLog
    L10_2 = "卖出选择"
    L9_2(L10_2)
    L9_2 = myBag
    L9_2 = L9_2[L7_2]
    L9_2 = L9_2.to
    L10_2 = tradingMap
    if L9_2 == L10_2 then
      L9_2 = color
      L9_2 = L9_2.goods
      L10_2 = myBag
      L10_2 = L10_2[L7_2]
      L10_2 = L10_2.from
      L9_2 = L9_2[L10_2]
      L9_2 = L9_2[L7_2]
      L10_2 = {}
      L11_2 = L9_2[1]
      L12_2 = L9_2[2]
      L13_2 = L9_2[3]
      L14_2 = L3_2[1]
      L15_2 = L3_2[2]
      L16_2 = L3_2[3]
      L17_2 = L3_2[4]
      L10_2[1] = L11_2
      L10_2[2] = L12_2
      L10_2[3] = L13_2
      L10_2[4] = L14_2
      L10_2[5] = L15_2
      L10_2[6] = L16_2
      L10_2[7] = L17_2
      L11_2 = {}
      L12_2 = L9_2[1]
      L13_2 = L9_2[2]
      L14_2 = L9_2[3]
      L15_2 = L2_2[1]
      L16_2 = L2_2[2]
      L17_2 = L2_2[3]
      L18_2 = L2_2[4]
      L11_2[1] = L12_2
      L11_2[2] = L13_2
      L11_2[3] = L14_2
      L11_2[4] = L15_2
      L11_2[5] = L16_2
      L11_2[6] = L17_2
      L11_2[7] = L18_2
      L12_2 = findAndTap
      L13_2 = L11_2
      L14_2 = "singleTap"
      L15_2 = 20
      L12_2 = L12_2(L13_2, L14_2, L15_2)
      if L12_2 then
        repeat
          L12_2 = 1
          L13_2 = 20
          L14_2 = 1
          for L15_2 = L12_2, L13_2, L14_2 do
            L16_2 = mSleep
            L17_2 = math
            L17_2 = L17_2.random
            L18_2 = 200
            L19_2 = 250
            L17_2, L18_2, L19_2, L20_2, L21_2 = L17_2(L18_2, L19_2)
            L16_2(L17_2, L18_2, L19_2, L20_2, L21_2)
            L16_2 = mSleep
            L17_2 = _ENV["卡顿掉帧"]
            L17_2 = L17_2 / 5
            L16_2(L17_2)
            L16_2 = findFeat
            L17_2 = L10_2
            L16_2 = L16_2(L17_2)
            if 0 < L16_2 then
              break
            end
            L16_2 = findAndTap
            L17_2 = L11_2
            L18_2 = "singleTap"
            L19_2 = 20
            L16_2(L17_2, L18_2, L19_2)
            L16_2 = mSleep
            L17_2 = math
            L17_2 = L17_2.random
            L18_2 = 300
            L19_2 = 350
            L17_2, L18_2, L19_2, L20_2, L21_2 = L17_2(L18_2, L19_2)
            L16_2(L17_2, L18_2, L19_2, L20_2, L21_2)
            L16_2 = mSleep
            L17_2 = _ENV["卡顿掉帧"]
            L17_2 = L17_2 / 5
            L16_2(L17_2)
          end
          L12_2 = tsOcrText
          L13_2 = blackPrice
          L14_2 = 1125
          L15_2 = 804
          L16_2 = 1294
          L17_2 = 841
          L18_2 = "292B2E , 282A2D"
          L19_2 = 85
          L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
          if L12_2 == "" then
            L13_2 = tsOcrText
            L14_2 = bluePrice
            L15_2 = 1125
            L16_2 = 804
            L17_2 = 1294
            L18_2 = 841
            L19_2 = "191AFA , 181909"
            L20_2 = 85
            L13_2 = L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L12_2 = L13_2
          end
        until L12_2 ~= ""
        L13_2 = nLog
        L14_2 = "卖出价钱:"
        L15_2 = L12_2
        L14_2 = L14_2 .. L15_2
        L13_2(L14_2)
        L13_2 = myBag
        L13_2 = L13_2[L7_2]
        L14_2 = tradingMerchant
        L15_2 = tonumber
        L16_2 = L12_2
        L15_2 = L15_2(L16_2)
        L13_2[L14_2] = L15_2
      else
        L12_2 = nLog
        L13_2 = "没有商品"
        L14_2 = L7_2
        L13_2 = L13_2 .. L14_2
        L12_2(L13_2)
        L12_2 = myBag
        L12_2 = L12_2[L7_2]
        L13_2 = tradingMerchant
        L12_2[L13_2] = 1
      end
      L12_2 = nLog
      L13_2 = L7_2
      L14_2 = tradingMerchant
      L15_2 = myBag
      L15_2 = L15_2[L7_2]
      L16_2 = tradingMerchant
      L15_2 = L15_2[L16_2]
      L13_2 = L13_2 .. L14_2 .. L15_2
      L12_2(L13_2)
    else
      L9_2 = nLog
      L10_2 = "没有商品"
      L11_2 = L7_2
      L10_2 = L10_2 .. L11_2
      L9_2(L10_2)
    end
  end
  L4_2 = closeScrnLog
  L5_2 = "查看商品价格"
  L4_2(L5_2)
  L4_2 = nLog
  L5_2 = "查看-priceList="
  L4_2(L5_2)
  L4_2 = nLog
  L5_2 = json
  L5_2 = L5_2.encode
  L6_2 = priceList
  L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2 = L5_2(L6_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
end

checkPrice = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = pairs
  L1_2 = priceList
  L0_2, L1_2, L2_2 = L0_2(L1_2)
  for L3_2, L4_2 in L0_2, L1_2, L2_2 do
    L5_2 = L4_2.A
    L6_2 = L4_2.B
    if L5_2 <= L6_2 then
      L5_2 = L4_2.A
      L6_2 = proposedPrice
      L6_2 = L6_2[L3_2]
      if L5_2 < L6_2 then
        L5_2 = priceList
        L5_2 = L5_2[L3_2]
        L5_2.theRightOne = "A"
    end
    else
      L5_2 = L4_2.B
      L6_2 = L4_2.A
      if L5_2 <= L6_2 then
        L5_2 = L4_2.B
        L6_2 = proposedPrice
        L6_2 = L6_2[L3_2]
        if L5_2 < L6_2 then
          L5_2 = priceList
          L5_2 = L5_2[L3_2]
          L5_2.theRightOne = "B"
      end
      else
        L5_2 = priceList
        L5_2 = L5_2[L3_2]
        L5_2.theRightOne = "none"
      end
    end
  end
  L0_2 = pairs
  L1_2 = myBag
  L0_2, L1_2, L2_2 = L0_2(L1_2)
  for L3_2, L4_2 in L0_2, L1_2, L2_2 do
    L5_2 = L4_2.A
    L6_2 = L4_2.B
    if L5_2 >= L6_2 then
      L5_2 = myBag
      L5_2 = L5_2[L3_2]
      L5_2.theRightOne = "A"
    else
      L5_2 = L4_2.B
      L6_2 = L4_2.A
      if L5_2 >= L6_2 then
        L5_2 = myBag
        L5_2 = L5_2[L3_2]
        L5_2.theRightOne = "B"
      end
    end
  end
  L0_2 = nLog
  L1_2 = "比价-priceList="
  L2_2 = json
  L2_2 = L2_2.encode
  L3_2 = priceList
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2 .. L2_2
  L0_2(L1_2)
  L0_2 = _ENV["UI_跑商模式"]
  if L0_2 == "联动跑商" then
    L0_2 = nLog
    L1_2 = "发送信息="
    L2_2 = json
    L2_2 = L2_2.encode
    L3_2 = priceList
    L2_2 = L2_2(L3_2)
    L1_2 = L1_2 .. L2_2
    L0_2(L1_2)
    L0_2 = _ENV["青松_对象操作"]
    L1_2 = "保存对象"
    L2_2 = _ENV["_联动_服务器名"]
    L3_2 = tradingMap
    L4_2 = os
    L4_2 = L4_2.date
    L5_2 = "%M"
    L6_2 = os
    L6_2 = L6_2.time
    L6_2, L7_2, L8_2 = L6_2()
    L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2)
    L5_2 = ";"
    L6_2 = json
    L6_2 = L6_2.encode
    L7_2 = priceList
    L6_2 = L6_2(L7_2)
    L4_2 = L4_2 .. L5_2 .. L6_2
    L0_2(L1_2, L2_2, L3_2, L4_2)
  end
  L0_2 = nLog
  L1_2 = "比价-myBag="
  L2_2 = json
  L2_2 = L2_2.encode
  L3_2 = myBag
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2 .. L2_2
  L0_2(L1_2)
end

comparePrices = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2
  L0_2 = {}
  L1_2 = 251
  L2_2 = 180
  L3_2 = 915
  L4_2 = 315
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L1_2 = {}
  L2_2 = 408
  L3_2 = 366
  L4_2 = 506
  L5_2 = 464
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L2_2 = {}
  L3_2 = 0
  L4_2 = chequeBalance
  L5_2 = _ENV["交票金额"]
  if L4_2 >= L5_2 then
    L4_2 = false
    return L4_2
  else
    L4_2 = tradingMap
    if L4_2 == "BJLZ" or L4_2 == "CAC" then
      L4_2 = _ENV["北俱地府"]
      if L4_2 == false then
        L4_2 = false
        return L4_2
      end
    end
  end
  -- 移除 lbl_33 标签
  L4_2 = scrnLog
  L5_2 = "购买商品"
  L4_2(L5_2)
  L4_2 = pairs
  L5_2 = priceList
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = L8_2.theRightOne
    if L9_2 == "A" or L9_2 == "B" then
      -- 继续执行购买逻辑
      L9_2 = nLog
      L10_2 = "购买："
      L11_2 = L5_2
      L12_2 = " 商人："
      L13_2 = L6_2
      L14_2 = " 数量："
      L15_2 = L8_2.amount
      L16_2 = " 单价："
      L17_2 = L7_2[L6_2]
      L10_2 = L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2
      L9_2(L10_2)
      L9_2 = L8_2.amount
      L10_2 = L7_2[L6_2]
      L9_2 = L9_2 * L10_2
      L10_2 = _ENV["购买商品"]
      L11_2 = L5_2
      L12_2 = L8_2.amount
      L13_2 = L6_2
      L10_2 = L10_2(L11_2, L12_2, L13_2)
      if L10_2 == false then
        L10_2 = false
        return L10_2
      end
      L10_2 = priceList
      L10_2 = L10_2[L5_2]
      L11_2 = L6_2
      L12_2 = L7_2[L6_2]
      L10_2[L11_2] = L12_2
      L10_2 = _ENV["账户余额"]
      L11_2 = L10_2.balance
      L11_2 = L11_2 - L9_2
      L10_2.balance = L11_2
      L10_2 = _ENV["保存账户余额"]
      L11_2 = _ENV["账户余额"]
      L10_2(L11_2)
    end
    L9_2 = nLog
    L10_2 = "购买方位"
    L11_2 = L8_2.theRightOne
    L10_2 = L10_2 .. L11_2
    L9_2(L10_2)
    L9_2 = L8_2.theRightOne
    L10_2 = math
    L10_2 = L10_2.floor
    L11_2 = chequeBalance
    L12_2 = L8_2[L9_2]
    L11_2 = L11_2 / L12_2
    L10_2 = L10_2(L11_2)
    L11_2 = ""
    if 19 <= L10_2 then
      L10_2 = 19
    end
    if L7_2 == "money" or L7_2 == "shoushi" or L7_2 == "baozhu" then
      L11_2 = "BJLZ"
    elseif L7_2 == "salt" or L7_2 == "wine" or L7_2 == "hat" then
      L11_2 = "CSC"
    elseif L7_2 == "dengyou" or L7_2 == "lingdang" or L7_2 == "renshen" then
      L11_2 = "DF"
    elseif L7_2 == "fozhu" or L7_2 == "dao" or L7_2 == "shanzi" then
      L11_2 = "CSC"
    elseif L7_2 == "antler" or L7_2 == "amulet" or L7_2 == "mianfen" then
      L12_2 = chequeBalance
      L13_2 = _ENV["回城金额"]
      if L12_2 > L13_2 or _ENV["北俱地府"] then
        L11_2 = "CAC"
      else
        L11_2 = "ALG"
      end
    end
    -- 移除 lbl_109 和 lbl_110 标签
    L12_2 = table
    L12_2 = L12_2.insert
    L13_2 = L2_2
    L14_2 = {}
    L14_2.goods = L7_2
    L14_2.merchant = L9_2
    L14_2.to = L11_2
    L15_2 = L8_2[L9_2]
    L14_2.price = L15_2
    L14_2.quantity = L10_2
    L15_2 = chequeBalance
    L16_2 = L8_2[L9_2]
    L16_2 = L16_2 * L10_2
    L15_2 = L15_2 - L16_2
    L14_2.balance = L15_2
    L12_2(L13_2, L14_2)
    -- 移除 lbl_126 标签
  end
  L4_2 = table
  L4_2 = L4_2.sort
  L5_2 = L2_2
  
  function L6_2(A0_3, A1_3)
    local L2_3, L3_3, L4_3
    L2_3 = A0_3.quantity
    L3_3 = A1_3.quantity
    if L2_3 > L3_3 then
      L2_3 = true
      return L2_3
    else
      L2_3 = A0_3.quantity
      L3_3 = A1_3.quantity
      if L2_3 == L3_3 then
        L2_3 = A0_3.balance
        L3_3 = A1_3.balance
        if L2_3 < L3_3 then
          L2_3 = true
          return L2_3
        else
          L2_3 = A0_3.balance
          L3_3 = A1_3.balance
          if L2_3 == L3_3 then
            L2_3 = A0_3.goods
            L3_3 = A1_3.goods
            L2_3 = L2_3 < L3_3
            return L2_3
          end
        end
      end
    end
    L2_3 = false
    return L2_3
  end
  
  L4_2(L5_2, L6_2)
  L4_2 = nLog
  L5_2 = "排序前="
  L6_2 = json
  L6_2 = L6_2.encode
  L7_2 = L2_2
  L6_2 = L6_2(L7_2)
  L5_2 = L5_2 .. L6_2
  L4_2(L5_2)
  L4_2 = nLog
  L5_2 = "排序后="
  L6_2 = json
  L6_2 = L6_2.encode
  L7_2 = L2_2
  L6_2 = L6_2(L7_2)
  L5_2 = L5_2 .. L6_2
  L4_2(L5_2)
  L4_2 = _ENV["UI_合适都买"]
  if L4_2 then
    L4_2 = _ENV["UI_跑商模式"]
    if L4_2 ~= "抢货模式" then
      L4_2 = pairs
      L5_2 = priceList
      L4_2, L5_2, L6_2 = L4_2(L5_2)
      for L7_2, L8_2 in L4_2, L5_2, L6_2 do
        L9_2 = L8_2.theRightOne
        if (L9_2 == "A" and L7_2 ~= "salt") or (L9_2 == "B" and L7_2 ~= "salt") then
          -- 继续执行销售逻辑
          L9_2 = nLog
          L10_2 = "销售："
          L11_2 = L7_2
          L12_2 = " 商人："
          L13_2 = L6_2
          L14_2 = " 数量："
          L15_2 = L8_2.amount
          L16_2 = " 单价："
          L17_2 = L5_2[L6_2]
          L10_2 = L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2
          L9_2(L10_2)
          L9_2 = L8_2.amount
          L10_2 = L5_2[L6_2]
          L9_2 = L9_2 * L10_2
          L10_2 = _ENV["销售商品"]
          L11_2 = L7_2
          L12_2 = L8_2.amount
          L13_2 = L6_2
          L10_2 = L10_2(L11_2, L12_2, L13_2)
          if L10_2 == false then
            L10_2 = false
            return L10_2
          end
          L10_2 = priceList
          L10_2 = L10_2[L7_2]
          L11_2 = L6_2
          L12_2 = L5_2[L6_2]
          L10_2[L11_2] = L12_2
          L10_2 = _ENV["账户余额"]
          L11_2 = L10_2.balance
          L11_2 = L11_2 + L9_2
          L10_2.balance = L11_2
          L10_2 = _ENV["保存账户余额"]
          L11_2 = _ENV["账户余额"]
          L10_2(L11_2)
        end
        L9_2 = nLog
        L10_2 = "购买方位"
        L11_2 = L8_2.theRightOne
        L10_2 = L10_2 .. L11_2
        L9_2(L10_2)
        L9_2 = L8_2.theRightOne
        L10_2 = L8_2.theRightOne
        if L10_2 == "A" then
          L9_2 = "B"
        else
          L10_2 = L8_2.theRightOne
          if L10_2 == "B" then
            L9_2 = "A"
          end
        end
        L10_2 = math
        L10_2 = L10_2.floor
        L11_2 = chequeBalance
        L12_2 = L8_2[L9_2]
        L11_2 = L11_2 / L12_2
        L10_2 = L10_2(L11_2)
        L11_2 = ""
        if 19 <= L10_2 then
          L10_2 = 19
        end
        if L7_2 == "money" or L7_2 == "shoushi" or L7_2 == "baozhu" then
          L11_2 = "BJLZ"
        elseif L7_2 == "salt" or L7_2 == "wine" or L7_2 == "hat" then
          L11_2 = "CSC"
        elseif L7_2 == "dengyou" or L7_2 == "lingdang" or L7_2 == "renshen" then
          L11_2 = "DF"
        elseif L7_2 == "fozhu" or L7_2 == "dao" or L7_2 == "shanzi" then
          L11_2 = "CSC"
        elseif L7_2 == "antler" or L7_2 == "amulet" or L7_2 == "mianfen" then
          L12_2 = chequeBalance
          L13_2 = _ENV["回城金额"]
          if L12_2 > L13_2 or _ENV["北俱地府"] then
            L11_2 = "CAC"
          else
            L11_2 = "ALG"
          end
        end
        -- 移除 lbl_241 和 lbl_242 标签
        L12_2 = table
        L12_2 = L12_2.insert
        L13_2 = L2_2
        L14_2 = {}
        L14_2.goods = L7_2
        L14_2.merchant = L9_2
        L14_2.to = L11_2
        L15_2 = L8_2[L9_2]
        L14_2.price = L15_2
        L14_2.quantity = L10_2
        L15_2 = chequeBalance
        L16_2 = L8_2[L9_2]
        L16_2 = L16_2 * L10_2
        L15_2 = L15_2 - L16_2
        L14_2.balance = L15_2
        L12_2(L13_2, L14_2)
        -- 移除 lbl_258 标签
      end
      L4_2 = nLog
      L5_2 = "二次排序后="
      L6_2 = json
      L6_2 = L6_2.encode
      L7_2 = L2_2
      L6_2 = L6_2(L7_2)
      L5_2 = L5_2 .. L6_2
      L4_2(L5_2)
    end
  end
  L4_2 = pairs
  L5_2 = L2_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = L8_2.goods
    if L9_2 == "salt" then
      L9_2 = table
      L9_2 = L9_2.remove
      L10_2 = L2_2
      L11_2 = L7_2
      L9_2(L10_2, L11_2)
      L9_2 = table
      L9_2 = L9_2.insert
      L10_2 = L2_2
      L11_2 = 1
      L12_2 = L8_2
      L9_2(L10_2, L11_2, L12_2)
      break
    end
  end
  L4_2 = {}
  priceList = L4_2
  _ENV["购买成功"] = true
  L4_2 = nLog
  L5_2 = "开始购买流程"
  L4_2(L5_2)
  L4_2 = pairs
  L5_2 = L2_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = nLog
    L10_2 = "钱数="
    L11_2 = chequeBalance
    L10_2 = L10_2 .. L11_2
    L9_2(L10_2)
    L9_2 = nLog
    L10_2 = "goodsAmount="
    L11_2 = L3_2
    L10_2 = L10_2 .. L11_2
    L9_2(L10_2)
    if 19 <= L3_2 then
      L9_2 = nLog
      L10_2 = "身上商品已满"
      L9_2(L10_2)
      break
    elseif L3_2 < 19 then
      L9_2 = chequeBalance
      L10_2 = L8_2.price
      if L9_2 > L10_2 then
        L9_2 = nLog
        L10_2 = "符合条件购买商品"
        L9_2(L10_2)
        L9_2 = tradingMerchant
        L10_2 = L8_2.merchant
        if L9_2 == L10_2 then
          L9_2 = ""
          L10_2 = tradingMerchant
          if L10_2 == "A" then
            L9_2 = "商人"
          else
            L10_2 = tradingMerchant
            if L10_2 == "B" then
              L9_2 = "货商"
            end
          end
          L10_2 = scrnLog
          L11_2 = "买入:"
          L12_2 = L9_2
          L13_2 = "价格更优"
          L11_2 = L11_2 .. L12_2 .. L13_2
          L10_2(L11_2)
          L10_2 = {}
          L11_2 = color
          L11_2 = L11_2.goods
          L12_2 = tradingMap
          L11_2 = L11_2[L12_2]
          L12_2 = L8_2.goods
          L11_2 = L11_2[L12_2]
          L11_2 = L11_2[1]
          L12_2 = color
          L12_2 = L12_2.goods
          L13_2 = tradingMap
          L12_2 = L12_2[L13_2]
          L13_2 = L8_2.goods
          L12_2 = L12_2[L13_2]
          L12_2 = L12_2[2]
          L13_2 = color
          L13_2 = L13_2.goods
          L14_2 = tradingMap
          L13_2 = L13_2[L14_2]
          L14_2 = L8_2.goods
          L13_2 = L13_2[L14_2]
          L13_2 = L13_2[3]
          L14_2 = L0_2[1]
          L15_2 = L0_2[2]
          L16_2 = L0_2[3]
          L17_2 = L0_2[4]
          L10_2[1] = L11_2
          L10_2[2] = L12_2
          L10_2[3] = L13_2
          L10_2[4] = L14_2
          L10_2[5] = L15_2
          L10_2[6] = L16_2
          L10_2[7] = L17_2
          repeat
            L11_2 = findAndTap
            L12_2 = L10_2
            L13_2 = "singleTap"
            L14_2 = 20
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            if L11_2 then
              -- 继续执行购买逻辑
              L11_2 = mSleep
              L12_2 = math
              L12_2 = L12_2.random
              L13_2 = 1000
              L14_2 = 2000
              L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L12_2(L13_2, L14_2)
              L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
              L11_2 = _ENV["购买商品"]
              L12_2 = L8_2.goods
              L13_2 = L9_2
              L14_2 = L8_2.merchant
              L11_2 = L11_2(L12_2, L13_2, L14_2)
              if L11_2 == false then
                L11_2 = false
                return L11_2
              end
              L11_2 = priceList
              L11_2 = L11_2[L8_2.goods]
              L12_2 = L8_2.merchant
              L13_2 = L8_2.price
              L11_2[L12_2] = L13_2
              L11_2 = _ENV["账户余额"]
              L12_2 = L11_2.balance
              L13_2 = L9_2
              L14_2 = L8_2.price
              L13_2 = L13_2 * L14_2
              L12_2 = L12_2 - L13_2
              L11_2.balance = L12_2
              L11_2 = _ENV["保存账户余额"]
              L12_2 = _ENV["账户余额"]
              L11_2(L12_2)
              L11_2 = nLog
              L12_2 = "购买："
              L13_2 = L8_2.goods
              L14_2 = " 商人："
              L15_2 = L8_2.merchant
              L16_2 = " 数量："
              L17_2 = L9_2
              L18_2 = " 单价："
              L19_2 = L8_2.price
              L20_2 = " 价格更优"
              L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2 .. L20_2
              L11_2(L12_2)
            end
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 300
            L14_2 = 350
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L11_2 = findAndTap
            L12_2 = color
            L12_2 = L12_2.btn
            L12_2 = L12_2.buyMax
            L13_2 = "singleTap"
            L14_2 = 15
            L11_2(L12_2, L13_2, L14_2)
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 400
            L14_2 = 450
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L11_2 = tonumber
            L12_2 = tsOcrText
            L13_2 = blackPrice
            L14_2 = 718
            L15_2 = 810
            L16_2 = 790
            L17_2 = 843
            L18_2 = "292B2E , 282A2D"
            L19_2 = 85
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
            L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
          until L11_2 ~= "" and L11_2 ~= nil
          L12_2 = findAndTap
          L13_2 = color
          L13_2 = L13_2.btn
          L13_2 = L13_2.buyIn
          L14_2 = "singleTap"
          L15_2 = 15
          L12_2(L13_2, L14_2, L15_2)
          L12_2 = _find_cx
          L13_2 = Color
          L13_2 = L13_2["通用"]
          L13_2 = L13_2["出现对话框"]
          L14_2 = {}
          L15_2 = 70
          L16_2 = 40
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L12_2 = L12_2(L13_2, L14_2)
          if L12_2 then
            L12_2 = 1
            L13_2 = 10
            L14_2 = 1
            for L15_2 = L12_2, L13_2, L14_2 do
              L16_2 = _cmp
              L17_2 = Color
              L17_2 = L17_2["跑商"]
              L17_2 = L17_2["购买成功"]
              L16_2 = L16_2(L17_2)
              if L16_2 then
                _ENV["购买成功"] = true
                break
              end
              L16_2 = _cmp_tb
              L17_2 = Color
              L17_2 = L17_2["跑商"]
              L17_2 = L17_2["购买失败"]
              L16_2 = L16_2(L17_2)
              if L16_2 then
                _ENV["购买成功"] = false
                L16_2 = closeAllDialog
                L16_2()
                break
              end
            end
          end
          L12_2 = _ENV["购买成功"]
          if L12_2 then
            L12_2 = myBag
            L13_2 = L8_2.goods
            L14_2 = {}
            L15_2 = tradingMap
            L14_2.from = L15_2
            L15_2 = L8_2.to
            L14_2.to = L15_2
            L12_2[L13_2] = L14_2
            L3_2 = L3_2 + L11_2
            L12_2 = closeAllDialog
            L12_2()
            L12_2 = checkBalance
            L12_2()
            L12_2 = nLog
            L13_2 = "[DATE]"
            L14_2 = "balance:"
            L15_2 = chequeBalance
            L13_2 = L13_2 .. L14_2 .. L15_2
            L12_2(L13_2)
            L12_2 = closeScrnLog
            L13_2 = "买入:"
            L14_2 = L9_2
            L15_2 = "价格更优"
            L13_2 = L13_2 .. L14_2 .. L15_2
            L12_2(L13_2)
          end
          -- 移除 lbl_486 标签
        else
          L9_2 = math
          L9_2 = L9_2.floor
          L10_2 = chequeBalance
          L11_2 = L8_2.price
          L10_2 = L10_2 / L11_2
          L9_2 = L9_2(L10_2)
          if L9_2 < 3 then
            break
          end
          L9_2 = closeAllWnd
          L9_2()
          L9_2 = nLog
          L10_2 = "去隔壁买"
          L9_2(L10_2)
          L9_2 = openTrading
          L10_2 = tradingMap
          L11_2 = L8_2.merchant
          L9_2(L10_2, L11_2)
          L9_2 = ""
          L10_2 = tradingMerchant
          if L10_2 == "A" then
            L9_2 = "商人"
          else
            L10_2 = tradingMerchant
            if L10_2 == "B" then
              L9_2 = "货商"
            end
          end
          L10_2 = scrnLog
          L11_2 = "买入:"
          L12_2 = L9_2
          L13_2 = "价格更优"
          L11_2 = L11_2 .. L12_2 .. L13_2
          L10_2(L11_2)
          L10_2 = {}
          L11_2 = color
          L11_2 = L11_2.goods
          L12_2 = tradingMap
          L11_2 = L11_2[L12_2]
          L12_2 = L8_2.goods
          L11_2 = L11_2[L12_2]
          L11_2 = L11_2[1]
          L12_2 = color
          L12_2 = L12_2.goods
          L13_2 = tradingMap
          L12_2 = L12_2[L13_2]
          L13_2 = L8_2.goods
          L12_2 = L12_2[L13_2]
          L12_2 = L12_2[2]
          L13_2 = color
          L13_2 = L13_2.goods
          L14_2 = tradingMap
          L13_2 = L13_2[L14_2]
          L14_2 = L8_2.goods
          L13_2 = L13_2[L14_2]
          L13_2 = L13_2[3]
          L14_2 = L0_2[1]
          L15_2 = L0_2[2]
          L16_2 = L0_2[3]
          L17_2 = L0_2[4]
          L10_2[1] = L11_2
          L10_2[2] = L12_2
          L10_2[3] = L13_2
          L10_2[4] = L14_2
          L10_2[5] = L15_2
          L10_2[6] = L16_2
          L10_2[7] = L17_2
          repeat
            L11_2 = findAndTap
            L12_2 = L10_2
            L13_2 = "singleTap"
            L14_2 = 20
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            if L11_2 then
              -- 继续执行销售逻辑
            else
              -- 替换 goto lbl_664，跳出当前循环
              break
            end
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 300
            L14_2 = 350
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L11_2 = findAndTap
            L12_2 = color
            L12_2 = L12_2.btn
            L12_2 = L12_2.buyMax
            L13_2 = "singleTap"
            L14_2 = 15
            L11_2(L12_2, L13_2, L14_2)
            L11_2 = mSleep
            L12_2 = math
            L12_2 = L12_2.random
            L13_2 = 400
            L14_2 = 450
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2)
            L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
            L11_2 = tonumber
            L12_2 = tsOcrText
            L13_2 = blackPrice
            L14_2 = 718
            L15_2 = 810
            L16_2 = 790
            L17_2 = 843
            L18_2 = "292B2E , 282A2D"
            L19_2 = 85
            L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
            L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
          until L11_2 ~= "" and L11_2 ~= nil
          L12_2 = findAndTap
          L13_2 = color
          L13_2 = L13_2.btn
          L13_2 = L13_2.buyIn
          L14_2 = "singleTap"
          L15_2 = 20
          L12_2(L13_2, L14_2, L15_2)
          L12_2 = _find_cx
          L13_2 = Color
          L13_2 = L13_2["通用"]
          L13_2 = L13_2["出现对话框"]
          L14_2 = {}
          L15_2 = 70
          L16_2 = 40
          L14_2[1] = L15_2
          L14_2[2] = L16_2
          L12_2 = L12_2(L13_2, L14_2)
          if L12_2 then
            L12_2 = 1
            L13_2 = 10
            L14_2 = 1
            for L15_2 = L12_2, L13_2, L14_2 do
              L16_2 = _cmp
              L17_2 = Color
              L17_2 = L17_2["跑商"]
              L17_2 = L17_2["购买成功"]
              L16_2 = L16_2(L17_2)
              if L16_2 then
                _ENV["购买成功"] = true
                break
              end
              L16_2 = _cmp_tb
              L17_2 = Color
              L17_2 = L17_2["跑商"]
              L17_2 = L17_2["购买失败"]
              L16_2 = L16_2(L17_2)
              if L16_2 then
                _ENV["购买成功"] = false
                L16_2 = closeAllDialog
                L16_2()
                break
              end
              L16_2 = mSleep
              L17_2 = 20
              L16_2(L17_2)
            end
          end
          L12_2 = _ENV["购买成功"]
          if L12_2 then
            L12_2 = myBag
            L13_2 = L8_2.goods
            L14_2 = {}
            L15_2 = tradingMap
            L14_2.from = L15_2
            L15_2 = L8_2.to
            L14_2.to = L15_2
            L12_2[L13_2] = L14_2
            L3_2 = L3_2 + L11_2
            L12_2 = closeAllDialog
            L12_2()
            L12_2 = checkBalance
            L12_2()
            L12_2 = nLog
            L13_2 = "[DATE]"
            L14_2 = "balance:"
            L15_2 = chequeBalance
            L13_2 = L13_2 .. L14_2 .. L15_2
            L12_2(L13_2)
            L12_2 = closeScrnLog
            L13_2 = "买入:"
            L14_2 = L9_2
            L15_2 = "价格更优"
            L13_2 = L13_2 .. L14_2 .. L15_2
            L12_2(L13_2)
          end
        end
      end
    end
    -- 移除 lbl_664 标签
  end
  L4_2 = cleanupINF
  L4_2()
  L4_2 = nLog
  L5_2 = "购买后-myBag="
  L4_2(L5_2)
  L4_2 = nLog
  L5_2 = json
  L5_2 = L5_2.encode
  L6_2 = myBag
  L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2 = L5_2(L6_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
  L4_2 = closeScrnLog
  L5_2 = "购买商品"
  L4_2(L5_2)
end

buyGoods = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L0_2 = 0
  L1_2 = {}
  L2_2 = 999
  L3_2 = 184
  L4_2 = 1666
  L5_2 = 718
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L2_2 = pairs
  L3_2 = myBag
  L2_2, L3_2, L4_2 = L2_2(L3_2)
  for L5_2, L6_2 in L2_2, L3_2, L4_2 do
    L0_2 = L0_2 + 1
  end
  if 0 < L0_2 then
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L7_2 = L6_2.theRightOne
      L8_2 = tradingMerchant
      if L7_2 == L8_2 then
        L7_2 = ""
        L8_2 = tradingMerchant
        if L8_2 == "A" then
          L7_2 = "商人"
        else
          L8_2 = tradingMerchant
          if L8_2 == "B" then
            L7_2 = "货商"
          end
        end
        L8_2 = scrnLog
        L9_2 = "卖出:"
        L10_2 = L7_2
        L11_2 = "价格更优"
        L9_2 = L9_2 .. L10_2 .. L11_2
        L8_2(L9_2)
        L8_2 = color
        L8_2 = L8_2.goods
        L9_2 = myBag
        L9_2 = L9_2[L5_2]
        L9_2 = L9_2.from
        L8_2 = L8_2[L9_2]
        L8_2 = L8_2[L5_2]
        L9_2 = {}
        L10_2 = L8_2[1]
        L11_2 = L8_2[2]
        L12_2 = L8_2[3]
        L13_2 = L1_2[1]
        L14_2 = L1_2[2]
        L15_2 = L1_2[3]
        L16_2 = L1_2[4]
        L9_2[1] = L10_2
        L9_2[2] = L11_2
        L9_2[3] = L12_2
        L9_2[4] = L13_2
        L9_2[5] = L14_2
        L9_2[6] = L15_2
        L9_2[7] = L16_2
        L10_2 = findAndTap
        L11_2 = L9_2
        L12_2 = "singleTap"
        L13_2 = 20
        L10_2 = L10_2(L11_2, L12_2, L13_2)
        if L10_2 then
          -- 继续执行销售逻辑
        else
          -- 替换 goto lbl_231，跳出当前循环
          break
        end
        L10_2 = 1
        L11_2 = 10
        L12_2 = 1
        for L13_2 = L10_2, L11_2, L12_2 do
          L14_2 = _find
          L15_2 = Color
          L15_2 = L15_2["跑商"]
          L15_2 = L15_2["最大"]
          L14_2 = L14_2(L15_2)
          if L14_2 then
            L14_2 = mSleep
            L15_2 = math
            L15_2 = L15_2.random
            L16_2 = 250
            L17_2 = 350
            L15_2, L16_2, L17_2, L18_2 = L15_2(L16_2, L17_2)
            L14_2(L15_2, L16_2, L17_2, L18_2)
          end
          L14_2 = _find
          L15_2 = Color
          L15_2 = L15_2["跑商"]
          L15_2 = L15_2["卖出"]
          L14_2 = L14_2(L15_2)
          if L14_2 then
            L14_2 = mSleep
            L15_2 = math
            L15_2 = L15_2.random
            L16_2 = 550
            L17_2 = 750
            L15_2, L16_2, L17_2, L18_2 = L15_2(L16_2, L17_2)
            L14_2(L15_2, L16_2, L17_2, L18_2)
          end
          L14_2 = findAndTap
          L15_2 = L9_2
          L16_2 = "singleTap"
          L17_2 = 20
          L14_2 = L14_2(L15_2, L16_2, L17_2)
          if L14_2 == false then
            break
          end
          L14_2 = mSleep
          L15_2 = 400
          L14_2(L15_2)
        end
        L10_2 = mSleep
        L11_2 = math
        L11_2 = L11_2.random
        L12_2 = 850
        L13_2 = 1250
        L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L11_2(L12_2, L13_2)
        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
        L10_2 = checkBalance
        L10_2()
        L10_2 = nLog
        L11_2 = "[DATE]"
        L12_2 = "balance:"
        L13_2 = chequeBalance
        L11_2 = L11_2 .. L12_2 .. L13_2
        L10_2(L11_2)
        L10_2 = closeScrnLog
        L11_2 = "卖出:"
        L12_2 = L7_2
        L13_2 = "价格更优"
        L11_2 = L11_2 .. L12_2 .. L13_2
        L10_2(L11_2)
      else
        L7_2 = closeAllWnd
        L7_2()
        L7_2 = openTrading
        L8_2 = tradingMap
        L9_2 = L6_2.theRightOne
        L7_2(L8_2, L9_2)
        L7_2 = ""
        L8_2 = tradingMerchant
        if L8_2 == "A" then
          L7_2 = "商人"
        else
          L8_2 = tradingMerchant
          if L8_2 == "B" then
            L7_2 = "货商"
          end
        end
        L8_2 = scrnLog
        L9_2 = "卖出:"
        L10_2 = L7_2
        L11_2 = "价格更优"
        L9_2 = L9_2 .. L10_2 .. L11_2
        L8_2(L9_2)
        L8_2 = color
        L8_2 = L8_2.goods
        L9_2 = myBag
        L9_2 = L9_2[L5_2]
        L9_2 = L9_2.from
        L8_2 = L8_2[L9_2]
        L8_2 = L8_2[L5_2]
        L9_2 = {}
        L10_2 = L8_2[1]
        L11_2 = L8_2[2]
        L12_2 = L8_2[3]
        L13_2 = L1_2[1]
        L14_2 = L1_2[2]
        L15_2 = L1_2[3]
        L16_2 = L1_2[4]
        L9_2[1] = L10_2
        L9_2[2] = L11_2
        L9_2[3] = L12_2
        L9_2[4] = L13_2
        L9_2[5] = L14_2
        L9_2[6] = L15_2
        L9_2[7] = L16_2
        L10_2 = findAndTap
        L11_2 = L9_2
        L12_2 = "singleTap"
        L13_2 = 20
        L10_2 = L10_2(L11_2, L12_2, L13_2)
        if L10_2 then
          L10_2 = 1
          L11_2 = 10
          L12_2 = 1
          for L13_2 = L10_2, L11_2, L12_2 do
            L14_2 = _find
            L15_2 = Color
            L15_2 = L15_2["跑商"]
            L15_2 = L15_2["最大"]
            L14_2 = L14_2(L15_2)
            if L14_2 then
              L14_2 = mSleep
              L15_2 = math
              L15_2 = L15_2.random
              L16_2 = 250
              L17_2 = 350
              L15_2, L16_2, L17_2, L18_2 = L15_2(L16_2, L17_2)
              L14_2(L15_2, L16_2, L17_2, L18_2)
            end
            L14_2 = _find
            L15_2 = Color
            L15_2 = L15_2["跑商"]
            L15_2 = L15_2["卖出"]
            L14_2 = L14_2(L15_2)
            if L14_2 then
              L14_2 = mSleep
              L15_2 = math
              L15_2 = L15_2.random
              L16_2 = 550
              L17_2 = 750
              L15_2, L16_2, L17_2, L18_2 = L15_2(L16_2, L17_2)
              L14_2(L15_2, L16_2, L17_2, L18_2)
            end
            L14_2 = findAndTap
            L15_2 = L9_2
            L16_2 = "singleTap"
            L17_2 = 20
            L14_2 = L14_2(L15_2, L16_2, L17_2)
            if L14_2 == false then
              break
            end
            L14_2 = mSleep
            L15_2 = 400
            L14_2(L15_2)
          end
          L10_2 = checkBalance
          L10_2()
          L10_2 = nLog
          L11_2 = "[DATE]"
          L12_2 = "balance:"
          L13_2 = chequeBalance
          L11_2 = L11_2 .. L12_2 .. L13_2
          L10_2(L11_2)
          L10_2 = closeScrnLog
          L11_2 = "卖出:"
          L12_2 = L7_2
          L13_2 = "价格更优"
          L11_2 = L11_2 .. L12_2 .. L13_2
          L10_2(L11_2)
        end
      end
      -- 移除 lbl_231 标签
    end
    L2_2 = tradingMap
    if L2_2 == "BJLZ" or L2_2 == "CAC" then
      L2_2 = _ENV["北俱地府"]
      if L2_2 == false then
        L2_2 = closeAllWnd
        L2_2()
      end
    end
    -- 移除 lbl_244 标签
    L2_2 = {}
    myBag = L2_2
  end
end

sellGoods = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  while true do
    L0_2 = scrnLog
    L1_2 = "前往白虎堂"
    L0_2(L1_2)
    L0_2 = getCurMap
    L0_2 = L0_2()
    L1_2 = findFeat
    L2_2 = color
    L2_2 = L2_2.npcPos
    L2_2 = L2_2.exchequerDirector
    L1_2, L2_2 = L1_2(L2_2)
    L3_2 = findFeat
    L4_2 = color
    L4_2 = L4_2.npcPos
    L4_2 = L4_2["白虎堂红"]
    L3_2, L4_2 = L3_2(L4_2)
    if L0_2 == "WMC" and 0 < L1_2 then
      L5_2 = _ENV["UI_1级帮"]
      if L5_2 then
        L5_2 = {}
        L6_2 = 617
        L7_2 = 177
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 30
        L6_2(L7_2, L8_2)
      else
        L5_2 = {}
        L6_2 = L1_2 + 190
        L7_2 = L2_2
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 20
        L6_2(L7_2, L8_2)
      end
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 1000
      L8_2 = 1400
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L5_2 = _ENV["moveJudge押镖"]
      L5_2()
      L5_2 = closeScrnLog
      L6_2 = "前往白虎堂"
      L5_2(L6_2)
      L5_2 = true
      return L5_2
    elseif L0_2 == "WMC" and L1_2 < 0 and L3_2 < 0 then
      L5_2 = {}
      L6_2 = 617
      L7_2 = 177
      L5_2[1] = L6_2
      L5_2[2] = L7_2
      L6_2 = singleTap
      L7_2 = L5_2
      L8_2 = 30
      L6_2(L7_2, L8_2)
      L6_2 = mSleep
      L7_2 = 1000
      L6_2(L7_2)
      L6_2 = _ENV["moveJudge押镖"]
      L6_2()
    elseif L0_2 == "WMC" and 0 < L3_2 then
      L5_2 = _ENV["UI_1级帮"]
      if L5_2 then
        L5_2 = {}
        L6_2 = 617
        L7_2 = 177
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 30
        L6_2(L7_2, L8_2)
      else
        L5_2 = {}
        L6_2 = L3_2 + 180
        L7_2 = L4_2
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L6_2 = singleTap
        L7_2 = L5_2
        L8_2 = 20
        L6_2(L7_2, L8_2)
      end
      L5_2 = mSleep
      L6_2 = math
      L6_2 = L6_2.random
      L7_2 = 1000
      L8_2 = 1400
      L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2 = L6_2(L7_2, L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L5_2 = _ENV["moveJudge押镖"]
      L5_2()
      L5_2 = closeScrnLog
      L6_2 = "前往白虎堂"
      L5_2(L6_2)
      L5_2 = true
      return L5_2
    elseif L0_2 == "" then
      L5_2 = fightSystem
      L5_2()
    elseif L0_2 == "BP" then
      L5_2 = _ENV["UI_车夫寻找方式"]
      if L5_2 == "自动识别" then
        L5_2 = mSleep
        L6_2 = 300
        L5_2(L6_2)
        L5_2 = tsFindText
        L6_2 = _ENV["车夫坐标"]
        L7_2 = "车夫"
        L8_2 = 100
        L9_2 = 100
        L10_2 = 1820
        L11_2 = 980
        L12_2 = "D5C636 , 17170D # E62C19 , 192C19"
        L13_2 = 90
        L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L2_2 = L6_2
        L1_2 = L5_2
        L5_2 = _tap
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = -10
        L8_2 = 2
        L6_2 = L6_2(L7_2, L8_2)
        L6_2 = L1_2 + L6_2
        L7_2 = math
        L7_2 = L7_2.random
        L8_2 = -45
        L9_2 = -5
        L7_2 = L7_2(L8_2, L9_2)
        L7_2 = L2_2 + L7_2
        L5_2(L6_2, L7_2)
        L5_2 = _find_cx
        L6_2 = Color
        L6_2 = L6_2["跑商"]
        L6_2 = L6_2["进入金库"]
        L7_2 = {}
        L8_2 = 50
        L9_2 = 70
        L7_2[1] = L8_2
        L7_2[2] = L9_2
        L5_2 = L5_2(L6_2, L7_2)
        if L5_2 then
        end
        L5_2 = _ENV["_返回"]
        L5_2 = L5_2["地图"]
        L6_2 = "无名城"
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = mSleep
          L6_2 = 777
          L5_2(L6_2)
        end
        L5_2 = _find
        L6_2 = Color
        L6_2 = L6_2["跑商"]
        L6_2 = L6_2["车夫金钱不足"]
        L5_2 = L5_2(L6_2)
        if L5_2 then
          L5_2 = _call_script
          L6_2 = "dd_7"
          L5_2(L6_2)
        end
      else
        L5_2 = _ENV["起号"]
        L5_2 = L5_2["坐标"]
        L5_2, L6_2 = L5_2()
        L7_2 = _ENV["车夫x"]
        L7_2 = L7_2 - L5_2
        L8_2 = _ENV["车夫y"]
        L8_2 = L8_2 - L6_2
        L9_2 = math
        L9_2 = L9_2.abs
        L10_2 = L7_2
        L9_2 = L9_2(L10_2)
        if L9_2 <= 12 then
          L9_2 = math
          L9_2 = L9_2.abs
          L10_2 = L8_2
          L9_2 = L9_2(L10_2)
          if L9_2 <= 12 then
            L9_2 = _ENV["_计算"]
            L9_2 = L9_2["取目标屏幕坐标"]
            L10_2 = "帮派"
            L11_2 = L5_2
            L12_2 = L6_2
            L13_2 = _ENV["车夫x"]
            L14_2 = _ENV["车夫y"]
            L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2)
            L6_2 = L10_2
            L5_2 = L9_2
            L9_2 = _tap
            L10_2 = L5_2
            L11_2 = math
            L11_2 = L11_2.random
            L12_2 = -15
            L13_2 = -5
            L11_2 = L11_2(L12_2, L13_2)
            L11_2 = L6_2 + L11_2
            L9_2(L10_2, L11_2)
            L9_2 = _find_cx
            L10_2 = Color
            L10_2 = L10_2["跑商"]
            L10_2 = L10_2["进入金库"]
            L11_2 = {}
            L12_2 = 50
            L13_2 = 70
            L11_2[1] = L12_2
            L11_2[2] = L13_2
            L9_2 = L9_2(L10_2, L11_2)
            if L9_2 then
            end
            L9_2 = _ENV["_返回"]
            L9_2 = L9_2["地图"]
            L10_2 = "无名城"
            L9_2 = L9_2(L10_2)
            if L9_2 then
              L9_2 = mSleep
              L10_2 = 777
              L9_2(L10_2)
            end
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["跑商"]
            L10_2 = L10_2["车夫金钱不足"]
            L9_2 = L9_2(L10_2)
            if L9_2 then
              L9_2 = _call_script
              L10_2 = "dd_7"
              L9_2(L10_2)
            end
          end
        end
      end
    else
      L5_2 = goToBP
      L6_2 = travelMod
      L5_2(L6_2)
    end
  end
end

enterExchequer = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = 0
  while true do
    L1_2 = scrnLog
    L2_2 = "领取跑商任务"
    L1_2(L2_2)
    L1_2 = checkMerchantTask
    L1_2 = L1_2()
    if L1_2 then
      L1_2 = closeScrnLog
      L2_2 = "领取跑商任务"
      L1_2(L2_2)
      L1_2 = _ENV["初始金额"]
      chequeBalance = L1_2
      L1_2 = true
      return L1_2
    else
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["跑商"]
      L2_2 = L2_2["赏金任务列表"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = _ENV["通用功能"]
        L1_2 = L1_2["关闭"]
        L1_2()
      end
      L1_2 = _ENV["押镖屏蔽"]
      L1_2()
      L1_2 = findFeat
      L2_2 = color
      L2_2 = L2_2.npcPos
      L2_2 = L2_2.exchequerDirector
      L1_2, L2_2 = L1_2(L2_2)
      if 0 < L1_2 then
        L3_2 = scrnLog
        L4_2 = "点击总管"
        L3_2(L4_2)
        L0_2 = L0_2 + 1
        if 5 < L0_2 then
          L3_2 = randomTap
          L4_2 = 612
          L5_2 = 180
          L6_2 = 30
          L3_2(L4_2, L5_2, L6_2)
          L3_2 = mSleep
          L4_2 = 3333
          L3_2(L4_2)
          L3_2 = _ENV["moveJudge押镖"]
          L3_2()
          L3_2 = enterExchequer
          L3_2()
          L0_2 = 0
        end
        L3_2 = {}
        L4_2 = L1_2 + 45
        L5_2 = L2_2 - 70
        L3_2[1] = L4_2
        L3_2[2] = L5_2
        L4_2 = singleTap
        L5_2 = L3_2
        L6_2 = 25
        L4_2(L5_2, L6_2)
        L4_2 = _Sleep
        L5_2 = 100
        L6_2 = 150
        L4_2(L5_2, L6_2)
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2["跑商"]
        L5_2 = L5_2["白虎堂重叠"]
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = mSleep
          L5_2 = 1
          L4_2(L5_2)
        end
        L4_2 = _find_cx
        L5_2 = Color
        L5_2 = L5_2["起号"]
        L5_2 = L5_2["出现对话框"]
        L6_2 = {}
        L7_2 = 50
        L8_2 = 50
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = _ENV["UI_赏金选择"]
          if L4_2 == "无赏金下号" then
            L4_2 = _find
            L5_2 = Color
            L5_2 = L5_2["跑商"]
            L5_2 = L5_2["赏金任务存在"]
            L4_2 = L4_2(L5_2)
            if L4_2 == false then
              L4_2 = _ENV["_游戏"]
              L4_2 = L4_2["退出到登录界面"]
              L4_2()
              L4_2 = UI_msg
              if L4_2 == "响铃" then
                L4_2 = setVolumeLevel
                L5_2 = 1
                L4_2(L5_2)
                L4_2 = playAudio
                L5_2 = userPath
                L5_2 = L5_2()
                L6_2 = "/res/GameGX.mp3"
                L5_2 = L5_2 .. L6_2
                L4_2(L5_2)
              else
                L4_2 = vibrator
                L4_2()
              end
              L4_2 = dialog
              L5_2 = "没有赏金任务"
              L6_2 = time
              L4_2(L5_2, L6_2)
              L4_2 = lua_exit
              L4_2()
            end
          end
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["跑商"]
          L5_2 = L5_2["赏金任务存在"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = _ENV["UI_赏金任务"]
            if L4_2 == "赏金任务" then
              L4_2 = 1
              L5_2 = 5
              L6_2 = 1
              for L7_2 = L4_2, L5_2, L6_2 do
                L8_2 = _find
                L9_2 = Color
                L9_2 = L9_2["跑商"]
                L9_2 = L9_2["赏金任务存在"]
                L8_2 = L8_2(L9_2)
                if L8_2 then
                  L8_2 = tap
                  L9_2 = 1509
                  L10_2 = 486
                  L8_2(L9_2, L10_2)
                  L8_2 = mSleep
                  L9_2 = 300
                  L8_2(L9_2)
                  L8_2 = _find_cx
                  L9_2 = Color
                  L9_2 = L9_2["跑商"]
                  L9_2 = L9_2["进入赏金任务"]
                  L10_2 = {}
                  L11_2 = 40
                  L12_2 = 50
                  L10_2[1] = L11_2
                  L10_2[2] = L12_2
                  L8_2(L9_2, L10_2)
                end
                L8_2 = _find
                L9_2 = Color
                L9_2 = L9_2["跑商"]
                L9_2 = L9_2["进入赏金任务"]
                L8_2 = L8_2(L9_2)
                if L8_2 then
                  L8_2 = mSleep
                  L9_2 = 300
                  L8_2(L9_2)
                  L8_2 = _find_cx
                  L9_2 = Color
                  L9_2 = L9_2["跑商"]
                  L9_2 = L9_2["赏金任务列表"]
                  L10_2 = {}
                  L11_2 = 40
                  L12_2 = 50
                  L10_2[1] = L11_2
                  L10_2[2] = L12_2
                  L8_2(L9_2, L10_2)
                end
                L8_2 = _find
                L9_2 = Color
                L9_2 = L9_2["跑商"]
                L9_2 = L9_2["赏金任务列表"]
                L8_2 = L8_2(L9_2)
                if L8_2 then
                  L8_2 = 1
                  L9_2 = 10
                  L10_2 = 1
                  for L11_2 = L8_2, L9_2, L10_2 do
                    L12_2 = _find
                    L13_2 = Color
                    L13_2 = L13_2["跑商"]
                    L13_2 = L13_2["赏金任务列表"]
                    L12_2 = L12_2(L13_2)
                    if L12_2 then
                      L12_2 = _find
                      L13_2 = Color
                      L13_2 = L13_2["跑商"]
                      L13_2 = L13_2["点击赏金任务"]
                      L12_2 = L12_2(L13_2)
                      if L12_2 then
                        L12_2 = mSleep
                        L13_2 = 300
                        L12_2(L13_2)
                      end
                    end
                    L12_2 = _find
                    L13_2 = Color
                    L13_2 = L13_2["跑商"]
                    L13_2 = L13_2["选中赏金任务"]
                    L12_2 = L12_2(L13_2)
                    if L12_2 then
                      L12_2 = mSleep
                      L13_2 = 300
                      L12_2(L13_2)
                      L12_2 = _find
                      L13_2 = Color
                      L13_2 = L13_2["跑商"]
                      L13_2 = L13_2["领取赏金任务"]
                      L12_2 = L12_2(L13_2)
                      if L12_2 then
                        L12_2 = mSleep
                        L13_2 = 300
                        L12_2(L13_2)
                        L12_2 = _find_cx
                        L13_2 = Color
                        L13_2 = L13_2["起号"]
                        L13_2 = L13_2["出现对话框"]
                        L14_2 = {}
                        L15_2 = 40
                        L16_2 = 40
                        L14_2[1] = L15_2
                        L14_2[2] = L16_2
                        L12_2 = L12_2(L13_2, L14_2)
                        if L12_2 then
                        end
                        L12_2 = _find
                        L13_2 = Color
                        L13_2 = L13_2["跑商"]
                        L13_2 = L13_2["赏金满20次"]
                        L12_2 = L12_2(L13_2)
                        if not L12_2 then
                          L12_2 = _find
                          L13_2 = Color
                          L13_2 = L13_2["跑商"]
                          L13_2 = L13_2["帮派资金足够"]
                          L12_2 = L12_2(L13_2)
                          if L12_2 then
                            -- 继续执行后续逻辑
                          else
                            -- 替换 goto lbl_289，直接跳转到对应逻辑
                            L12_2 = _find
                            L13_2 = Color
                            L13_2 = L13_2["跑商"]
                            L13_2 = L13_2["领取体验状态"]
                            L12_2 = L12_2(L13_2)
                            if L12_2 then
                              L12_2 = randomClick
                              L13_2 = 2
                              L14_2 = 1000
                              L15_2 = 1500
                              L16_2 = 925
                              L17_2 = 1229
                              L18_2 = 10
                              L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                            end
                            return
                          end
                        end
                        L12_2 = _ENV["通用功能"]
                        L12_2 = L12_2["关闭"]
                        L12_2()
                        L12_2 = _ENV["UI_赏金选择"]
                        if L12_2 == "跑满20赏金下号" or L12_2 == "无赏金下号" then
                          -- 继续执行退出逻辑
                        else
                          -- 替换 goto lbl_285，直接执行对应逻辑
                          L12_2 = _ENV["UI_赏金选择"]
                          if L12_2 == "优先赏金" then
                            _ENV["UI_赏金任务"] = "赏"
                          end
                          -- 跳转到lbl_289的逻辑
                          L12_2 = _find
                          L13_2 = Color
                          L13_2 = L13_2["跑商"]
                          L13_2 = L13_2["领取体验状态"]
                          L12_2 = L12_2(L13_2)
                          if L12_2 then
                            L12_2 = randomClick
                            L13_2 = 2
                            L14_2 = 1000
                            L15_2 = 1500
                            L16_2 = 925
                            L17_2 = 1229
                            L18_2 = 10
                            L12_2(L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                          end
                          return
                        end
                        L12_2 = _ENV["_游戏"]
                        L12_2 = L12_2["退出到登录界面"]
                        L12_2()
                        L12_2 = UI_msg
                        if L12_2 == "响铃" then
                          L12_2 = setVolumeLevel
                          L13_2 = 1
                          L12_2(L13_2)
                          L12_2 = playAudio
                          L13_2 = userPath
                          L13_2 = L13_2()
                          L14_2 = "/res/GameGX.mp3"
                          L13_2 = L13_2 .. L14_2
                          L12_2(L13_2)
                        else
                          L12_2 = vibrator
                          L12_2()
                        end
                        L12_2 = dialog
                        L13_2 = "已跑完20票赏金"
                        L14_2 = time
                        L12_2(L13_2, L14_2)
                        L12_2 = lua_exit
                        L12_2()
                        -- 移除 goto lbl_289 和未使用的标签
                        L12_2 = _find
                        L13_2 = Color
                        L13_2 = L13_2["跑商"]
                        L13_2 = L13_2["领取体验状态"]
                        L12_2 = L12_2(L13_2)
                        if L12_2 then
                          L12_2 = _ENV["_游戏"]
                          L12_2 = L12_2["退出到登录界面"]
                          L12_2()
                          L12_2 = dialog
                          L13_2 = "欠费了"
                          L14_2 = time
                          L12_2(L13_2, L14_2)
                        end
                        L12_2 = findAndTap
                        L13_2 = color
                        L13_2 = L13_2.btn
                        L13_2 = L13_2.tradingTask15w
                        L14_2 = "singleTap"
                        L15_2 = 20
                        L12_2 = L12_2(L13_2, L14_2, L15_2)
                        if L12_2 == false then
                          L12_2 = mSleep
                          L13_2 = 200
                          L12_2(L13_2)
                          L12_2 = _find
                          L13_2 = Color
                          L13_2 = L13_2["跑商"]
                          L13_2 = L13_2["没有商人申请"]
                          L12_2 = L12_2(L13_2)
                          if L12_2 then
                            L12_2 = scrnLog
                            L13_2 = "申请商人1"
                            L12_2(L13_2)
                            L12_2 = _ENV["通用功能"]
                            L12_2 = L12_2["关闭"]
                            L12_2()
                            L12_2 = mSleep
                            L13_2 = math
                            L13_2 = L13_2.random
                            L14_2 = 450
                            L15_2 = 550
                            L13_2, L14_2, L15_2, L16_2, L17_2 = L13_2(L14_2, L15_2)
                            L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
                            L12_2 = singleTap
                            L13_2 = L3_2
                            L14_2 = 20
                            L12_2(L13_2, L14_2)
                            L12_2 = mSleep
                            L13_2 = math
                            L13_2 = L13_2.random
                            L14_2 = 450
                            L15_2 = 550
                            L13_2, L14_2, L15_2, L16_2, L17_2 = L13_2(L14_2, L15_2)
                            L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
                            L12_2 = _find_cx
                            L13_2 = Color
                            L13_2 = L13_2["起号"]
                            L13_2 = L13_2["出现对话框"]
                            L14_2 = {}
                            L15_2 = 50
                            L16_2 = 50
                            L14_2[1] = L15_2
                            L14_2[2] = L16_2
                            L12_2 = L12_2(L13_2, L14_2)
                            if L12_2 then
                            end
                            L12_2 = findFeat
                            L13_2 = color
                            L13_2 = L13_2.btn
                            L13_2 = L13_2.giveMeTradingTask
                            L12_2 = L12_2(L13_2)
                            if 0 < L12_2 then
                              L12_2 = {}
                              L13_2 = {}
                              L14_2 = 1647
                              L15_2 = 580
                              L13_2[1] = L14_2
                              L13_2[2] = L15_2
                              L14_2 = {}
                              L15_2 = 1635
                              L16_2 = 392
                              L14_2[1] = L15_2
                              L14_2[2] = L16_2
                              L12_2[1] = L13_2
                              L12_2[2] = L14_2
                              L13_2 = slide
                              L14_2 = L12_2
                              L13_2(L14_2)
                              L13_2 = mSleep
                              L14_2 = math
                              L14_2 = L14_2.random
                              L15_2 = 500
                              L16_2 = 650
                              L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2, L16_2)
                              L13_2(L14_2, L15_2, L16_2, L17_2)
                              L13_2 = findAndTap
                              L14_2 = color
                              L14_2 = L14_2.btn
                              L14_2 = L14_2.becomeBussinessMan
                              L15_2 = "singleTap"
                              L16_2 = 30
                              L13_2(L14_2, L15_2, L16_2)
                              L13_2 = closeScrnLog
                              L14_2 = "申请商人2"
                              L13_2(L14_2)
                            end
                          end
                        end
                      end
                    end
                    L12_2 = myBlockcoloraa
                    L13_2 = sevenColor
                    L13_2 = L13_2["跑商任务暗"]
                    L12_2 = L12_2(L13_2)
                    if L12_2 then
                      L12_2 = _find
                      L13_2 = Color
                      L13_2 = L13_2.npc
                      L13_2 = L13_2["通用关闭对话框"]
                      L14_2 = {}
                      L15_2 = -1000
                      L16_2 = -100
                      L14_2[1] = L15_2
                      L14_2[2] = L16_2
                      L12_2 = L12_2(L13_2, L14_2)
                      if L12_2 then
                        L12_2 = mSleep
                        L13_2 = 340
                        L12_2(L13_2)
                      end
                      break
                    end
                    L12_2 = mSleep
                    L13_2 = 200
                    L12_2(L13_2)
                  end
                end
                L8_2 = mSleep
                L9_2 = 200
                L8_2(L9_2)
              end
          end
          else
            L4_2 = _find
            L5_2 = Color
            L5_2 = L5_2["跑商"]
            L5_2 = L5_2["给我些任务"]
            L6_2 = {}
            L7_2 = math
            L7_2 = L7_2.random
            L8_2 = 10
            L9_2 = 200
            L7_2 = L7_2(L8_2, L9_2)
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 1
            L10_2 = 10
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L8_2(L9_2, L10_2)
            L6_2[1] = L7_2
            L6_2[2] = L8_2
            L6_2[3] = L9_2
            L6_2[4] = L10_2
            L6_2[5] = L11_2
            L6_2[6] = L12_2
            L6_2[7] = L13_2
            L6_2[8] = L14_2
            L6_2[9] = L15_2
            L6_2[10] = L16_2
            L6_2[11] = L17_2
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = mSleep
              L5_2 = math
              L5_2 = L5_2.random
              L6_2 = 550
              L7_2 = 750
              L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
              L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
              L4_2 = _find_cx
              L5_2 = Color
              L5_2 = L5_2["起号"]
              L5_2 = L5_2["出现对话框"]
              L6_2 = {}
              L7_2 = 50
              L8_2 = 50
              L6_2[1] = L7_2
              L6_2[2] = L8_2
              L4_2 = L4_2(L5_2, L6_2)
              if L4_2 then
              end
              L4_2 = _find
              L5_2 = Color
              L5_2 = L5_2["跑商"]
              L5_2 = L5_2["领取体验状态"]
              L4_2 = L4_2(L5_2)
              if L4_2 then
                L4_2 = _ENV["_游戏"]
                L4_2 = L4_2["退出到登录界面"]
                L4_2()
                L4_2 = dialog
                L5_2 = "欠费了"
                L6_2 = time
                L4_2(L5_2, L6_2)
              end
              L4_2 = findAndTap
              L5_2 = color
              L5_2 = L5_2.btn
              L5_2 = L5_2.tradingTask15w
              L6_2 = "singleTap"
              L7_2 = 20
              L4_2 = L4_2(L5_2, L6_2, L7_2)
              if L4_2 == false then
                L4_2 = findFeat
                L5_2 = color
                L5_2 = L5_2.btn
                L5_2 = L5_2.notBussinessMan
                L4_2 = L4_2(L5_2)
                if 0 < L4_2 then
                  L4_2 = scrnLog
                  L5_2 = "申请商人3"
                  L4_2(L5_2)
                  L4_2 = closeAllDialog
                  L4_2()
                  L4_2 = mSleep
                  L5_2 = math
                  L5_2 = L5_2.random
                  L6_2 = 450
                  L7_2 = 550
                  L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
                  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  L4_2 = singleTap
                  L5_2 = L3_2
                  L6_2 = 20
                  L4_2(L5_2, L6_2)
                  L4_2 = mSleep
                  L5_2 = math
                  L5_2 = L5_2.random
                  L6_2 = 450
                  L7_2 = 550
                  L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
                  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  L4_2 = _find_cx
                  L5_2 = Color
                  L5_2 = L5_2["起号"]
                  L5_2 = L5_2["出现对话框"]
                  L6_2 = {}
                  L7_2 = 50
                  L8_2 = 50
                  L6_2[1] = L7_2
                  L6_2[2] = L8_2
                  L4_2 = L4_2(L5_2, L6_2)
                  if L4_2 then
                  end
                  L4_2 = findFeat
                  L5_2 = color
                  L5_2 = L5_2.btn
                  L5_2 = L5_2.giveMeTradingTask
                  L4_2 = L4_2(L5_2)
                  if 0 < L4_2 then
                    L4_2 = {}
                    L5_2 = {}
                    L6_2 = 1647
                    L7_2 = 580
                    L5_2[1] = L6_2
                    L5_2[2] = L7_2
                    L6_2 = {}
                    L7_2 = 1635
                    L8_2 = 392
                    L6_2[1] = L7_2
                    L6_2[2] = L8_2
                    L4_2[1] = L5_2
                    L4_2[2] = L6_2
                    L5_2 = slide
                    L6_2 = L4_2
                    L5_2(L6_2)
                    L5_2 = mSleep
                    L6_2 = math
                    L6_2 = L6_2.random
                    L7_2 = 600
                    L8_2 = 650
                    L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L6_2(L7_2, L8_2)
                    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                    L5_2 = findAndTap
                    L6_2 = color
                    L6_2 = L6_2.btn
                    L6_2 = L6_2.becomeBussinessMan
                    L7_2 = "singleTap"
                    L8_2 = 30
                    L5_2(L6_2, L7_2, L8_2)
                    L5_2 = closeScrnLog
                    L6_2 = "申请商人4"
                    L5_2(L6_2)
                    L5_2 = mSleep
                    L6_2 = 150
                    L5_2(L6_2)
                  end
                end
              end
            end
          end
        end
        L4_2 = _find
        L5_2 = Color
        L5_2 = L5_2.npc
        L5_2 = L5_2["通用关闭对话框"]
        L6_2 = {}
        L7_2 = -1000
        L8_2 = -100
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = mSleep
          L5_2 = 340
          L4_2(L5_2)
        end
      else
        L3_2 = getCurMap
        L3_2 = L3_2()
        if L3_2 == "WMC" then
          L4_2 = randomTap
          L5_2 = 612
          L6_2 = 180
          L7_2 = 30
          L4_2(L5_2, L6_2, L7_2)
          L4_2 = _ENV["moveJudge押镖"]
          L4_2()
          L4_2 = enterExchequer
          L4_2()
        else
          L4_2 = findFeat
          L5_2 = color
          L5_2 = L5_2.VERINF
          L5_2 = L5_2.VERmoveWord
          L4_2 = L4_2(L5_2)
          if 0 < L4_2 then
            L4_2 = _ENV["_验证"]
            L4_2 = L4_2["漂浮2"]
            L4_2()
          else
            L4_2 = findFeat
            L5_2 = color
            L5_2 = L5_2.VERINF
            L5_2 = L5_2.VERidiom
            L4_2 = L4_2(L5_2)
            if 0 < L4_2 then
              L4_2 = _ENV["_验证"]
              L4_2 = L4_2["成语"]
              L4_2 = L4_2["验证"]
              L4_2()
            else
              L4_2 = {}
              L5_2 = "000000000000000000000007f0007f0007000060000600006000060000e0001e03ffe0fffe07ffe01e1e01e0e01e0e01e0601e0601e0601e0601e0601e0601e0601e0601e0601e07f1e07f1e07f0e03f0c000000000000000000$点$194$20$36"
              L4_2[1] = L5_2
              L5_2 = addTSOcrDictEx
              L6_2 = L4_2
              L5_2 = L5_2(L6_2)
              L6_2 = tsFindText
              L7_2 = L5_2
              L8_2 = "点"
              L9_2 = 593
              L10_2 = 266
              L11_2 = 1100
              L12_2 = 781
              L13_2 = "FFFFFF , 030303 # FCFEFE , 92908F"
              L14_2 = 85
              L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
              if 0 < L6_2 then
                L8_2 = _ENV["_验证"]
                L8_2 = L8_2["漂浮2"]
                L8_2()
              end
              L8_2 = myBlockcolor
              L9_2 = sevenColor
              L9_2 = L9_2["叉叉按钮"]
              L8_2 = L8_2(L9_2)
              if L8_2 then
                L8_2 = randomTap
                L9_2 = L1_2
                L10_2 = L2_2
                L11_2 = 15
                L8_2(L9_2, L10_2, L11_2)
                L8_2 = _ENV["_随机延时"]
                L9_2 = 411
                L8_2(L9_2)
              end
              L8_2 = _find
              L9_2 = Color
              L9_2 = L9_2.npc
              L9_2 = L9_2["通用关闭对话框"]
              L10_2 = {}
              L11_2 = -1000
              L12_2 = -100
              L10_2[1] = L11_2
              L10_2[2] = L12_2
              L8_2 = L8_2(L9_2, L10_2)
              if L8_2 then
                L8_2 = mSleep
                L9_2 = 340
                L8_2(L9_2)
              end
              L8_2 = mSleep
              L9_2 = 300
              L8_2(L9_2)
              L8_2 = _cmp_tb
              L9_2 = Color
              L9_2 = L9_2["初始化"]
              L9_2 = L9_2["展开任务栏"]
              L8_2(L9_2)
              L8_2 = mSleep
              L9_2 = math
              L9_2 = L9_2.random
              L10_2 = 450
              L11_2 = 550
              L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L9_2(L10_2, L11_2)
              L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            end
          end
        end
      end
    end
  end
end

getTradingTask = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = os
  L0_2 = L0_2.time
  L0_2 = L0_2()
  while true do
    L1_2 = scrnLog
    L2_2 = "离开帮派"
    L1_2(L2_2)
    L1_2 = getCurMap
    L1_2 = L1_2()
    if L1_2 == "BP" then
      L2_2 = _ENV["UI_车夫寻找方式"]
      if L2_2 == "自动识别" then
        L2_2 = _ENV["_通用"]
        L2_2 = L2_2["道具栏"]
        L3_2 = "收缩"
        L2_2(L3_2)
        L2_2 = os
        L2_2 = L2_2.time
        L2_2 = L2_2()
        L2_2 = L2_2 - L0_2
        if 5 < L2_2 then
          L2_2 = _tap
          L3_2 = 1000
          L4_2 = 500
          L5_2 = 1
          L6_2 = 200
          L2_2(L3_2, L4_2, L5_2, L6_2)
          L2_2 = _Sleep
          L3_2 = 250
          L4_2 = 350
          L2_2(L3_2, L4_2)
          L2_2 = os
          L2_2 = L2_2.time
          L2_2 = L2_2()
          L0_2 = L2_2
        end
        L2_2 = _find_ps_tb_cx
        L3_2 = Color
        L3_2 = L3_2["跑商"]
        L3_2 = L3_2["新传送圈"]
        L4_2 = {}
        L5_2 = 100
        L6_2 = 5
        L4_2[1] = L5_2
        L4_2[2] = L6_2
        L5_2 = {}
        L6_2 = randomList
        L7_2 = {}
        L8_2 = -30
        L9_2 = 0
        L10_2 = 30
        L7_2[1] = L8_2
        L7_2[2] = L9_2
        L7_2[3] = L10_2
        L6_2 = L6_2(L7_2)
        L7_2 = randomList
        L8_2 = {}
        L9_2 = -30
        L10_2 = 0
        L11_2 = 30
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L8_2[3] = L11_2
        L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L7_2(L8_2)
        L5_2[1] = L6_2
        L5_2[2] = L7_2
        L5_2[3] = L8_2
        L5_2[4] = L9_2
        L5_2[5] = L10_2
        L5_2[6] = L11_2
        L5_2[7] = L12_2
        L6_2 = nil
        L7_2 = nil
        L8_2 = {}
        L9_2 = 3158064
        L10_2 = 3158064
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
        if L2_2 then
        end
      else
        L2_2 = _ENV["起号"]
        L2_2 = L2_2["坐标"]
        L2_2, L3_2 = L2_2()
        L4_2 = _ENV["_计算"]
        L4_2 = L4_2["取目标屏幕坐标"]
        L5_2 = "帮派"
        L6_2 = L2_2
        L7_2 = L3_2
        L8_2 = _ENV["出口x"]
        L9_2 = _ENV["出口y"]
        L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
        yx1 = L5_2
        xx1 = L4_2
        L4_2 = yx1
        if 880 < L4_2 then
          yx1 = 880
        end
        L4_2 = _tap
        L5_2 = xx1
        L6_2 = yx1
        L7_2 = 1
        L8_2 = 15
        L4_2(L5_2, L6_2, L7_2, L8_2)
        L4_2 = _Sleep
        L5_2 = 300
        L6_2 = 500
        L4_2(L5_2, L6_2)
      end
      L2_2 = _Sleep
      L3_2 = 300
      L4_2 = 500
      L2_2(L3_2, L4_2)
      L2_2 = _ENV["_功能"]
      L2_2 = L2_2["寻路"]
      L2_2()
      L2_2 = _cmp_tb_cx
      L3_2 = Color
      L3_2 = L3_2["地图"]
      L3_2 = L3_2["到达长安城"]
      L4_2 = {}
      L5_2 = 300
      L6_2 = 10
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L2_2 = L2_2(L3_2, L4_2)
      if L2_2 then
        L2_2 = _cmp_tb
        L3_2 = Color
        L3_2 = L3_2["初始化"]
        L3_2 = L3_2["A展开道具栏"]
        L2_2 = L2_2(L3_2)
        if L2_2 then
          L2_2 = _Sleep
          L3_2 = 200
          L4_2 = 300
          L2_2(L3_2, L4_2)
        end
        L2_2 = true
        return L2_2
      end
    elseif L1_2 == "WMC" then
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.npcPos
      L3_2 = L3_2["机关人"]
      L2_2, L3_2 = L2_2(L3_2)
      L4_2 = findFeat
      L5_2 = color
      L5_2 = L5_2.npcPos
      L5_2 = L5_2["机关人红"]
      L4_2, L5_2 = L4_2(L5_2)
      if 0 < L2_2 then
        L6_2 = {}
        L7_2 = L2_2 + 1
        L8_2 = L3_2 - 70
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L7_2 = singleTap
        L8_2 = L6_2
        L9_2 = 15
        L7_2(L8_2, L9_2)
      elseif 0 < L4_2 then
        L6_2 = {}
        L7_2 = L4_2 + 170
        L8_2 = L5_2 + 70
        L6_2[1] = L7_2
        L6_2[2] = L8_2
        L7_2 = singleTap
        L8_2 = L6_2
        L9_2 = 15
        L7_2(L8_2, L9_2)
      else
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 1
        L8_2 = 5
        L6_2 = L6_2(L7_2, L8_2)
        if 3 < L6_2 then
          L7_2 = randomTap
          L8_2 = 409
          L9_2 = 348
          L10_2 = 40
          L7_2(L8_2, L9_2, L10_2)
        else
          L7_2 = randomTap
          L8_2 = 1467
          L9_2 = 398
          L10_2 = 5
          L7_2(L8_2, L9_2, L10_2)
        end
        L7_2 = mSleep
        L8_2 = 1111
        L7_2(L8_2)
        L7_2 = _ENV["moveJudge押镖"]
        L7_2()
      end
      L6_2 = _find_cx
      L7_2 = Color
      L7_2 = L7_2["跑商"]
      L7_2 = L7_2["送我回出口"]
      L8_2 = {}
      L9_2 = 50
      L10_2 = 50
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L6_2 = L6_2(L7_2, L8_2)
      if L6_2 then
        L6_2 = _ENV["_返回"]
        L6_2 = L6_2["地图"]
        L7_2 = "帮派"
        L6_2 = L6_2(L7_2)
        if L6_2 then
          L6_2 = mSleep
          L7_2 = 222
          L6_2(L7_2)
        end
      end
    else
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.VERINF
      L3_2 = L3_2.VERmoveWord
      L2_2 = L2_2(L3_2)
      if 0 < L2_2 then
        L2_2 = _ENV["_验证"]
        L2_2 = L2_2["漂浮2"]
        L2_2()
      else
        L2_2 = findFeat
        L3_2 = color
        L3_2 = L3_2.VERINF
        L3_2 = L3_2.VERidiom
        L2_2 = L2_2(L3_2)
        if 0 < L2_2 then
          L2_2 = _ENV["_验证"]
          L2_2 = L2_2["成语"]
          L2_2 = L2_2["验证"]
          L2_2()
        else
          L2_2 = _cmp_tb
          L3_2 = Color
          L3_2 = L3_2["初始化"]
          L3_2 = L3_2["展开任务栏"]
          L2_2(L3_2)
          L2_2 = mSleep
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 450
          L5_2 = 550
          L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L3_2(L4_2, L5_2)
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
        end
      end
      L2_2 = myBlockcolor
      L3_2 = sevenColor
      L3_2 = L3_2["叉叉按钮"]
      L2_2 = L2_2(L3_2)
      if L2_2 then
        L2_2 = randomTap
        L3_2 = x
        L4_2 = y
        L5_2 = 15
        L2_2(L3_2, L4_2, L5_2)
        L2_2 = _ENV["_随机延时"]
        L3_2 = 411
        L2_2(L3_2)
      end
      L2_2 = _find
      L3_2 = Color
      L3_2 = L3_2.npc
      L3_2 = L3_2["通用关闭对话框"]
      L4_2 = {}
      L5_2 = -1000
      L6_2 = -100
      L4_2[1] = L5_2
      L4_2[2] = L6_2
      L2_2 = L2_2(L3_2, L4_2)
      if L2_2 then
        L2_2 = mSleep
        L3_2 = 340
        L2_2(L3_2)
      end
      L2_2 = _ENV["_通用"]
      L2_2 = L2_2["道具栏"]
      L2_2()
      L2_2 = true
      return L2_2
    end
  end
end

goOutOfBP = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  while true do
    L0_2 = scrnLog
    L1_2 = "交票"
    L0_2(L1_2)
    L0_2 = checkMerchantTask
    L0_2 = L0_2()
    if L0_2 then
      L0_2 = enterExchequer
      L0_2()
      L0_2 = mSleep
      L1_2 = 300
      L0_2(L1_2)
      L0_2 = findFeat
      L1_2 = color
      L1_2 = L1_2.npcPos
      L1_2 = L1_2.exchequerDirector
      L0_2, L1_2 = L0_2(L1_2)
      L2_2 = findFeat
      L3_2 = color
      L3_2 = L3_2.npcPos
      L3_2 = L3_2["白虎堂红"]
      L2_2, L3_2 = L2_2(L3_2)
      if 0 < L0_2 then
        L4_2 = {}
        L5_2 = L0_2 + 45
        L6_2 = L1_2 - 70
        L4_2[1] = L5_2
        L4_2[2] = L6_2
        L5_2 = singleTap
        L6_2 = L4_2
        L7_2 = 15
        L5_2(L6_2, L7_2)
        L5_2 = _find_cx
        L6_2 = Color
        L6_2 = L6_2["跑商"]
        L6_2 = L6_2["给我些任务"]
        L7_2 = {}
        L8_2 = 50
        L9_2 = 50
        L7_2[1] = L8_2
        L7_2[2] = L9_2
        L8_2 = {}
        L9_2 = math
        L9_2 = L9_2.random
        L10_2 = 10
        L11_2 = 200
        L9_2 = L9_2(L10_2, L11_2)
        L10_2 = math
        L10_2 = L10_2.random
        L11_2 = 1
        L12_2 = 10
        L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2)
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L8_2[3] = L11_2
        L8_2[4] = L12_2
        L8_2[5] = L13_2
        L5_2 = L5_2(L6_2, L7_2, L8_2)
        if L5_2 then
          L5_2 = mSleep
          L6_2 = 300
          L5_2(L6_2)
        end
        L5_2 = 1
        L6_2 = 8
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = mSleep
          L10_2 = 1000
          L9_2(L10_2)
          L9_2 = _find_tb
          L10_2 = Color
          L10_2 = L10_2["验证"]
          L10_2 = L10_2["成语"]
          L9_2 = L9_2(L10_2)
          if L9_2 then
            break
          end
        end
        L5_2 = _ENV["_验证"]
        L5_2 = L5_2["成语"]
        L5_2 = L5_2["验证"]
        L5_2()
        L5_2 = mSleep
        L6_2 = _ENV["卡顿掉帧"]
        L5_2(L6_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 550
        L8_2 = 700
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L5_2 = scrnLog
        L6_2 = "完毕"
        L5_2(L6_2)
        L5_2 = moveTips
        L5_2()
        L5_2 = os
        L5_2 = L5_2.time
        L5_2 = L5_2()
        repeat
          L6_2 = _find
          L7_2 = Color
          L7_2 = L7_2.npc
          L7_2 = L7_2["通用关闭对话框"]
          L8_2 = {}
          L9_2 = -1000
          L10_2 = -100
          L8_2[1] = L9_2
          L8_2[2] = L10_2
          L6_2 = L6_2(L7_2, L8_2)
          if L6_2 then
            L6_2 = mSleep
            L7_2 = 340
            L6_2(L7_2)
          end
          L6_2 = os
          L6_2 = L6_2.time
          L6_2 = L6_2()
          L6_2 = L6_2 - L5_2
          if 10 < L6_2 then
            L6_2 = _ENV["_验证"]
            L6_2 = L6_2["成语"]
            L6_2 = L6_2["验证"]
            L6_2()
            L6_2 = _ENV["通用功能"]
            L6_2 = L6_2["关闭"]
            L6_2()
          end
          L6_2 = _cmp_tb
          L7_2 = Color
          L7_2 = L7_2["主界面"]
          L6_2 = L6_2(L7_2)
        until L6_2
      elseif 0 < L2_2 then
        L4_2 = {}
        L5_2 = math
        L5_2 = L5_2.random
        L6_2 = 180
        L7_2 = 220
        L5_2 = L5_2(L6_2, L7_2)
        L5_2 = L2_2 + L5_2
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 0
        L8_2 = 20
        L6_2 = L6_2(L7_2, L8_2)
        L6_2 = L3_2 + L6_2
        L4_2[1] = L5_2
        L4_2[2] = L6_2
        L5_2 = singleTap
        L6_2 = L4_2
        L7_2 = 15
        L5_2(L6_2, L7_2)
        L5_2 = mSleep
        L6_2 = math
        L6_2 = L6_2.random
        L7_2 = 1000
        L8_2 = 1400
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2, L8_2)
        L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L5_2 = _ENV["_功能"]
        L5_2 = L5_2["寻路"]
        L5_2()
      end
    else
      L0_2 = closeScrnLog
      L1_2 = "交票"
      L0_2(L1_2)
      chequeBalance = 0
      L0_2 = true
      return L0_2
    end
  end
end

finishTrading = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = ts
  L0_2 = L0_2.ms
  L0_2 = L0_2()
  js1 = L0_2
  L0_2 = ts
  L0_2 = L0_2.ms
  L0_2 = L0_2()
  js3 = L0_2
  L0_2 = ts
  L0_2 = L0_2.ms
  L0_2 = L0_2()
  jsks = L0_2
  L0_2 = ts
  L0_2 = L0_2.ms
  L0_2 = L0_2()
  jsspys = L0_2
  _ENV["已跑票数"] = -1
  _ENV["已跑票数间隔"] = -1
  num1 = true
  _ENV["买到药品"] = false
  while true do
    L0_2 = _ENV["已跑票数"]
    L0_2 = L0_2 + 1
    _ENV["已跑票数"] = L0_2
    L0_2 = _ENV["已跑票数间隔"]
    L0_2 = L0_2 + 1
    _ENV["已跑票数间隔"] = L0_2
    L0_2 = _ENV["已跑票数间隔"]
    L1_2 = _ENV["转移二药间隔"]
    if L0_2 > L1_2 then
      L0_2 = _ENV["UI_转移二药"]
      if L0_2 == "每X票转移" then
        L0_2 = _ENV["转移二药总流程"]
        L0_2()
      end
      _ENV["已跑票数间隔"] = -1
    end
    L0_2 = _ENV["已跑票数"]
    L1_2 = _ENV["目标跑商票数"]
    if L0_2 > L1_2 then
      L0_2 = _ENV["循环上号"]
      if L0_2 == "单号循环" then
        L0_2 = _ENV["_游戏"]
        L0_2 = L0_2["退出到登录界面"]
        L0_2()
        L0_2 = UI_msg
        if L0_2 == "响铃" then
          L0_2 = setVolumeLevel
          L1_2 = 1
          L0_2(L1_2)
          L0_2 = playAudio
          L1_2 = userPath
          L1_2 = L1_2()
          L2_2 = "/res/GameGX.mp3"
          L1_2 = L1_2 .. L2_2
          L0_2(L1_2)
        else
          L0_2 = vibrator
          L0_2()
        end
        L0_2 = dialog
        L1_2 = "已跑完"
        L2_2 = _ENV["目标跑商票数"]
        L3_2 = "票"
        L1_2 = L1_2 .. L2_2 .. L3_2
        L2_2 = time
        L0_2(L1_2, L2_2)
        L0_2 = lua_exit
        L0_2()
      else
        return
      end
    end
    _ENV["购买帮派物品"] = false
    L0_2 = tradingInit
    L0_2()
    L0_2 = checkMerchantTask
    L0_2 = L0_2()
    if L0_2 then
      L0_2 = checkMyBag
      L0_2()
    else
      L0_2 = enterExchequer
      L0_2()
      L0_2 = getTradingTask
      L0_2()
      L0_2 = _ENV["进入帮派购买界面"]
      L0_2 = L0_2()
      if L0_2 then
        L0_2 = _ENV["帮派购买物品"]
        L0_2()
      end
      L0_2 = ts
      L0_2 = L0_2.ms
      L0_2 = L0_2()
      js4 = L0_2
      L0_2 = js4
      L1_2 = js3
      L0_2 = L0_2 - L1_2
      L0_2 = L0_2 / 60
      sfcy = L0_2
      L0_2 = _ENV["UI_二药频率"]
      if L0_2 == "每票买二药" then
        num1 = true
      end
      L0_2 = sfcy
      if 60 < L0_2 or num1 then
        -- 继续执行购买逻辑
      else
        -- 替换 goto lbl_131，直接跳转到对应逻辑
        L0_2 = ts
        L0_2 = L0_2.ms
        L0_2 = L0_2()
        jsjpys = L0_2
        return
      end
      num1 = false
      L0_2 = js4
      js3 = L0_2
      L0_2 = _ENV["UI_帮贡换二药"]
      if L0_2 then
        L0_2 = _ENV["金库到帮派"]
        L0_2()
        L0_2 = _ENV["帮派前往书院"]
        L0_2()
        L0_2 = _ENV["进入帮派购买药品界面"]
        L0_2 = L0_2()
        if L0_2 then
          L0_2 = _ENV["买药品"]
          L0_2()
        end
        L0_2 = _ENV["书院到帮派"]
        L0_2()
        _ENV["买到药品"] = true
      end
    end
    -- 移除 lbl_131 标签，这部分逻辑已经在前面处理过了
    L0_2 = ts
    L0_2 = L0_2.ms
    L0_2 = L0_2()
    jsjpys = L0_2
    L0_2 = jsjpys
    L1_2 = jsspys
    L0_2 = L0_2 - L1_2
    L0_2 = L0_2 / 60
    jsspys = L0_2
    L0_2 = ts
    L0_2 = L0_2.ms
    L0_2 = L0_2()
    jsdq = L0_2
    L0_2 = jsdq
    L1_2 = jsks
    L0_2 = L0_2 - L1_2
    L0_2 = L0_2 / 60
    L1_2 = _ENV["已跑票数"]
    L0_2 = L0_2 / L1_2
    pjys = L0_2
    L0_2 = math
    L0_2 = L0_2.modf
    L1_2 = pjys
    L0_2 = L0_2(L1_2)
    apjys = L0_2
    L0_2 = math
    L0_2 = L0_2.modf
    L1_2 = jsspys
    L0_2 = L0_2(L1_2)
    jsspys = L0_2
    L0_2 = _ENV["票数统计"]
    L1_2 = "已跑： "
    L2_2 = _ENV["已跑票数"]
    L3_2 = " 票，上票用时: "
    L4_2 = jsspys
    L5_2 = " 分，平均： "
    L6_2 = apjys
    L7_2 = "分"
    L1_2 = L1_2 .. L2_2 .. L3_2 .. L4_2 .. L5_2 .. L6_2 .. L7_2
    L0_2(L1_2)
    L0_2 = ts
    L0_2 = L0_2.ms
    L0_2 = L0_2()
    jsspys = L0_2
    needDodge = true
    L0_2 = goOutOfBP
    L0_2()
    L0_2 = _ENV["购买帮派物品"]
    if L0_2 then
      L0_2 = _ENV["长安卖货"]
      L0_2()
    end
    L0_2 = _cmp_tb
    L1_2 = Color
    L1_2 = L1_2["地图"]
    L1_2 = L1_2["到达长安城"]
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _ENV["买到药品"]
      if L0_2 then
        L0_2 = _ENV["存物品"]
        L0_2()
        _ENV["买到药品"] = false
      end
      L0_2 = ts
      L0_2 = L0_2.ms
      L0_2 = L0_2()
      js2 = L0_2
      L0_2 = js2
      L1_2 = js1
      L0_2 = L0_2 - L1_2
      L0_2 = L0_2 / 60
      sfmt = L0_2
      L0_2 = sfmt
      L1_2 = _ENV["卖体间隔tim"]
      if L0_2 > L1_2 then
        L0_2 = _ENV["UI_活力处置"]
        if L0_2 == "烹饪" then
          L0_2 = _ENV["烹饪"]
          L0_2()
        else
          L0_2 = _ENV["UI_活力处置"]
          if L0_2 == "飞行符" then
            L0_2 = _ENV["去做飞行符"]
            L0_2()
          else
            L0_2 = _ENV["UI_活力处置"]
            if L0_2 == "炼药" then
              L0_2 = _ENV["炼药"]
              L0_2()
            end
          end
        end
        L0_2 = _ENV["_功能"]
        L0_2 = L0_2["卖体"]
        L0_2()
        L0_2 = js2
        js1 = L0_2
      end
    end
    travelMod = "walk"
    L0_2 = _ENV["UI_跑商模式"]
    if L0_2 == "联动跑商" then
      L0_2 = _ENV["UI_跑商路线"]
      if L0_2 == "长安长寿" then
        L0_2 = _ENV["联动_跑商长安长寿"]
        L0_2()
      else
        L0_2 = _ENV["联动_跑商混合"]
        L0_2()
      end
    else
      L0_2 = _ENV["UI_跑商模式"]
      if L0_2 == "抢货模式" then
        L0_2 = _ENV["UI_跑商路线"]
        if L0_2 == "长安长寿" then
          L0_2 = _ENV["抢票_长安长寿"]
          L0_2()
        else
          L0_2 = _ENV["抢票_地府北俱"]
          L0_2()
        end
      else
        L0_2 = _ENV["UI_跑商路线"]
        if L0_2 == "比价换线" then
          L0_2 = _ENV["跑商_比价换线"]
          L0_2()
        else
          L0_2 = _ENV["UI_跑商路线"]
          if L0_2 == "长安长寿" then
            L0_2 = _ENV["跑商_长安长寿"]
            L0_2()
          else
            L0_2 = _ENV["跑商_混合"]
            L0_2()
          end
        end
      end
    end
  end
end

startTrading = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = pairs
  L1_2 = priceList
  L0_2, L1_2, L2_2 = L0_2(L1_2)
  for L3_2, L4_2 in L0_2, L1_2, L2_2 do
    L5_2 = _ENV["随机胖瘦"]
    L5_2 = L4_2[L5_2]
    L6_2 = proposedPrice
    L6_2 = L6_2[L3_2]
    if L5_2 < L6_2 then
      L5_2 = priceList
      L5_2 = L5_2[L3_2]
      L6_2 = _ENV["随机胖瘦"]
      L5_2.theRightOne = L6_2
    else
      L5_2 = priceList
      L5_2 = L5_2[L3_2]
      L5_2.theRightOne = "none"
    end
  end
  L0_2 = pairs
  L1_2 = myBag
  L0_2, L1_2, L2_2 = L0_2(L1_2)
  for L3_2, L4_2 in L0_2, L1_2, L2_2 do
    L5_2 = L4_2.A
    L6_2 = L4_2.B
    if L5_2 >= L6_2 then
      L5_2 = myBag
      L5_2 = L5_2[L3_2]
      L5_2.theRightOne = "A"
    else
      L5_2 = L4_2.B
      L6_2 = L4_2.A
      if L5_2 >= L6_2 then
        L5_2 = myBag
        L5_2 = L5_2[L3_2]
        L5_2.theRightOne = "B"
      end
    end
  end
  L0_2 = nLog
  L1_2 = "比价-priceList="
  L2_2 = json
  L2_2 = L2_2.encode
  L3_2 = priceList
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2 .. L2_2
  L0_2(L1_2)
  L0_2 = nLog
  L1_2 = "比价-myBag="
  L2_2 = json
  L2_2 = L2_2.encode
  L3_2 = myBag
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2 .. L2_2
  L0_2(L1_2)
end

_ENV["抢货判断"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = math
  L0_2 = L0_2.random
  L1_2 = 1
  L2_2 = 2
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 == 1 then
    _ENV["随机胖瘦"] = "A"
  else
    _ENV["随机胖瘦"] = "B"
  end
  while true do
    L1_2 = {}
    priceList = L1_2
    L1_2 = 0
    L2_2 = ""
    L3_2 = pairs
    L4_2 = myBag
    L3_2, L4_2, L5_2 = L3_2(L4_2)
    for L6_2, L7_2 in L3_2, L4_2, L5_2 do
      L1_2 = L1_2 + 1
      L2_2 = L7_2.to
      break
    end
    L3_2 = getCurMap
    L3_2 = L3_2()
    if L3_2 == "" then
      L4_2 = cleanupINF
      L4_2()
    end
    if 0 < L1_2 then
      L4_2 = ""
      if (L3_2 == "BJLZ" or L3_2 == "HGS" or L3_2 == "ALG" or L3_2 == "DHW" or L3_2 == "JYC" or L3_2 == "JNYW" or L3_2 == "CAC" or L3_2 == "DF") and L2_2 == "BJLZ" then
        L5_2 = openTrading
        L6_2 = L2_2
        L7_2 = "B"
        L5_2(L6_2, L7_2)
        L4_2 = "A"
      else
        L5_2 = openTrading
        L6_2 = L2_2
        L7_2 = "A"
        L5_2(L6_2, L7_2)
        L4_2 = "B"
      end
      L5_2 = checkPrice
      L5_2()
      L5_2 = closeAllWnd
      L5_2()
      L5_2 = openTrading
      L6_2 = L2_2
      L7_2 = L4_2
      L5_2(L6_2, L7_2)
      L5_2 = checkPrice
      L5_2()
      L5_2 = comparePrices
      L5_2()
      L5_2 = sellGoods
      L5_2()
      L5_2 = buyGoods
      L5_2()
      L5_2 = math
      L5_2 = L5_2.random
      L6_2 = 1
      L7_2 = 2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 == 1 then
        _ENV["随机胖瘦"] = "A"
      else
        _ENV["随机胖瘦"] = "B"
      end
    else
      L4_2 = chequeBalance
      L5_2 = _ENV["交票金额"]
      if L4_2 >= L5_2 then
        L4_2 = finishTrading
        L4_2()
        break
      elseif L3_2 == "CSC" or L3_2 == "CSJW" or L3_2 == "BJLZ" or L3_2 == "HGS" or L3_2 == "ALG" or L3_2 == "DHW" or L3_2 == "JYC" or L3_2 == "DTJW" or L3_2 == "DTGJ" or L3_2 == "JNYW" then
        L4_2 = openTrading
        L5_2 = "CSC"
        L6_2 = _ENV["随机胖瘦"]
        L4_2(L5_2, L6_2)
        L4_2 = checkPrice
        L4_2()
        L4_2 = _ENV["抢货判断"]
        L4_2()
        L4_2 = buyGoods
        L4_2()
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["随机胖瘦"]
          if L4_2 == "A" then
            _ENV["随机胖瘦"] = "B"
          else
            _ENV["随机胖瘦"] = "A"
          end
          L4_2 = openTrading
          L5_2 = "CSC"
          L6_2 = _ENV["随机胖瘦"]
          L4_2(L5_2, L6_2)
          L4_2 = checkPrice
          L4_2()
          L4_2 = _ENV["抢货判断"]
          L4_2()
          L4_2 = buyGoods
          L4_2()
        end
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.merchantPos
          L6_2 = _ENV["随机胖瘦"]
          L5_2 = L5_2[L6_2]
          L5_2 = L5_2.CSC
          L6_2 = "CSC"
          L4_2(L5_2, L6_2)
          L4_2 = _ENV["_通用"]
          L4_2 = L4_2["跑商等刷"]
          L4_2()
        end
      elseif L3_2 == "CAC" then
        L4_2 = openTrading
        L5_2 = "CAC"
        L6_2 = _ENV["随机胖瘦"]
        L4_2(L5_2, L6_2)
        L4_2 = checkPrice
        L4_2()
        L4_2 = _ENV["抢货判断"]
        L4_2()
        L4_2 = buyGoods
        L4_2()
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["随机胖瘦"]
          if L4_2 == "A" then
            _ENV["随机胖瘦"] = "B"
          else
            _ENV["随机胖瘦"] = "A"
          end
          L4_2 = openTrading
          L5_2 = "CAC"
          L6_2 = _ENV["随机胖瘦"]
          L4_2(L5_2, L6_2)
          L4_2 = checkPrice
          L4_2()
          L4_2 = _ENV["抢货判断"]
          L4_2()
          L4_2 = buyGoods
          L4_2()
        end
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.merchantPos
          L6_2 = _ENV["随机胖瘦"]
          L5_2 = L5_2[L6_2]
          L5_2 = L5_2.CAC
          L6_2 = "CAC"
          L4_2(L5_2, L6_2)
          L4_2 = _ENV["_通用"]
          L4_2 = L4_2["跑商等刷"]
          L4_2()
        end
      end
    end
  end
end

_ENV["抢票_长安长寿"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = math
  L0_2 = L0_2.random
  L1_2 = 1
  L2_2 = 2
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 == 1 then
    _ENV["随机胖瘦"] = "A"
  else
    _ENV["随机胖瘦"] = "B"
  end
  while true do
    L1_2 = {}
    priceList = L1_2
    L1_2 = 0
    L2_2 = ""
    L3_2 = pairs
    L4_2 = myBag
    L3_2, L4_2, L5_2 = L3_2(L4_2)
    for L6_2, L7_2 in L3_2, L4_2, L5_2 do
      L1_2 = L1_2 + 1
      L2_2 = L7_2.to
      break
    end
    L3_2 = getCurMap
    L3_2 = L3_2()
    if L3_2 == "" then
      L4_2 = cleanupINF
      L4_2()
    end
    if 0 < L1_2 then
      L4_2 = ""
      if (L3_2 == "BJLZ" or L3_2 == "HGS" or L3_2 == "ALG" or L3_2 == "DHW" or L3_2 == "JYC" or L3_2 == "JNYW" or L3_2 == "CAC" or L3_2 == "DF") and L2_2 == "BJLZ" then
        L5_2 = openTrading
        L6_2 = L2_2
        L7_2 = "B"
        L5_2(L6_2, L7_2)
        L4_2 = "A"
      else
        L5_2 = openTrading
        L6_2 = L2_2
        L7_2 = "A"
        L5_2(L6_2, L7_2)
        L4_2 = "B"
      end
      L5_2 = checkPrice
      L5_2()
      L5_2 = closeAllWnd
      L5_2()
      L5_2 = openTrading
      L6_2 = L2_2
      L7_2 = L4_2
      L5_2(L6_2, L7_2)
      L5_2 = checkPrice
      L5_2()
      L5_2 = comparePrices
      L5_2()
      L5_2 = sellGoods
      L5_2()
      L5_2 = buyGoods
      L5_2()
      L5_2 = math
      L5_2 = L5_2.random
      L6_2 = 1
      L7_2 = 2
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 == 1 then
        _ENV["随机胖瘦"] = "A"
      else
        _ENV["随机胖瘦"] = "B"
      end
    else
      L4_2 = chequeBalance
      L5_2 = _ENV["交票金额"]
      if L4_2 >= L5_2 then
        L4_2 = finishTrading
        L4_2()
        break
      elseif L3_2 == "CSC" or L3_2 == "CSJW" or L3_2 == "BJLZ" or L3_2 == "HGS" or L3_2 == "ALG" or L3_2 == "DHW" or L3_2 == "JYC" or L3_2 == "DTJW" or L3_2 == "JNYW" then
        L4_2 = openTrading
        L5_2 = "BJLZ"
        L6_2 = _ENV["随机胖瘦"]
        L4_2(L5_2, L6_2)
        L4_2 = checkPrice
        L4_2()
        L4_2 = _ENV["抢货判断"]
        L4_2()
        L4_2 = buyGoods
        L4_2()
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["随机胖瘦"]
          if L4_2 == "A" then
            _ENV["随机胖瘦"] = "B"
          else
            _ENV["随机胖瘦"] = "A"
          end
          L4_2 = openTrading
          L5_2 = "BJLZ"
          L6_2 = _ENV["随机胖瘦"]
          L4_2(L5_2, L6_2)
          L4_2 = checkPrice
          L4_2()
          L4_2 = _ENV["抢货判断"]
          L4_2()
          L4_2 = buyGoods
          L4_2()
        end
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["UI_回到地府"]
          if L4_2 then
            L4_2 = openTrading
            L5_2 = "DF"
            L6_2 = _ENV["随机胖瘦"]
            L4_2(L5_2, L6_2)
            L4_2 = goTo
            L5_2 = constPos
            L5_2 = L5_2.merchantPos
            L6_2 = _ENV["随机胖瘦"]
            L5_2 = L5_2[L6_2]
            L5_2 = L5_2.DF
            L6_2 = "DF"
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_通用"]
            L4_2 = L4_2["跑商等刷"]
            L4_2()
          else
            L4_2 = goTo
            L5_2 = constPos
            L5_2 = L5_2.merchantPos
            L6_2 = _ENV["随机胖瘦"]
            L5_2 = L5_2[L6_2]
            L5_2 = L5_2.BJLZ
            L6_2 = "BJLZ"
            L4_2(L5_2, L6_2)
            L4_2 = _ENV["_通用"]
            L4_2 = L4_2["跑商等刷"]
            L4_2()
          end
        end
      elseif L3_2 == "CAC" or L3_2 == "DF" or L3_2 == "DTGJ" then
        L4_2 = openTrading
        L5_2 = "DF"
        L6_2 = _ENV["随机胖瘦"]
        L4_2(L5_2, L6_2)
        L4_2 = checkPrice
        L4_2()
        L4_2 = _ENV["抢货判断"]
        L4_2()
        L4_2 = buyGoods
        L4_2()
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = _ENV["随机胖瘦"]
          if L4_2 == "A" then
            _ENV["随机胖瘦"] = "B"
          else
            _ENV["随机胖瘦"] = "A"
          end
          L4_2 = openTrading
          L5_2 = "DF"
          L6_2 = _ENV["随机胖瘦"]
          L4_2(L5_2, L6_2)
          L4_2 = checkPrice
          L4_2()
          L4_2 = _ENV["抢货判断"]
          L4_2()
          L4_2 = buyGoods
          L4_2()
        end
        L4_2 = _ENV["判断表数量"]
        L5_2 = myBag
        L4_2 = L4_2(L5_2)
        if L4_2 then
          L4_2 = goTo
          L5_2 = constPos
          L5_2 = L5_2.merchantPos
          L6_2 = _ENV["随机胖瘦"]
          L5_2 = L5_2[L6_2]
          L5_2 = L5_2.DF
          L6_2 = "DF"
          L4_2(L5_2, L6_2)
          L4_2 = _ENV["_通用"]
          L4_2 = L4_2["跑商等刷"]
          L4_2()
        end
      end
    end
  end
end

_ENV["抢票_地府北俱"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = 0
    L1_2 = ""
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L0_2 = L0_2 + 1
      L1_2 = L6_2.to
      break
    end
    L2_2 = getCurMap
    L2_2 = L2_2()
    if 0 < L0_2 then
      L3_2 = ""
      if (L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" or L2_2 == "CAC" or L2_2 == "DF") and L1_2 == "BJLZ" then
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "B"
        L4_2(L5_2, L6_2)
        L3_2 = "A"
      else
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "A"
        L4_2(L5_2, L6_2)
        L3_2 = "B"
      end
      L4_2 = checkPrice
      L4_2()
      L4_2 = closeAllWnd
      L4_2()
      L4_2 = openTrading
      L5_2 = L1_2
      L6_2 = L3_2
      L4_2(L5_2, L6_2)
      L4_2 = checkPrice
      L4_2()
      L4_2 = comparePrices
      L4_2()
      L4_2 = sellGoods
      L4_2()
      L4_2 = buyGoods
      L4_2()
    else
      L3_2 = chequeBalance
      L4_2 = _ENV["交票金额"]
      if L3_2 >= L4_2 then
        L3_2 = finishTrading
        L3_2()
        break
      else
        if L2_2 == "CSC" or L2_2 == "CSJW" or (L2_2 == "BJLZ" and chequeBalance > _ENV["回城金额"] and _ENV["北俱地府"] == false) then
          -- 继续执行交易逻辑
        else
          -- 替换 goto lbl_103，直接执行对应逻辑
          if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
            L3_2 = chequeBalance
            L4_2 = _ENV["回城金额"]
            if L3_2 <= L4_2 then
              L3_2 = _ENV["北俱地府"]
              if L3_2 == false then
                L3_2 = goToMap
                L4_2 = "BJLZ"
                L3_2(L4_2)
              end
            end
          end
          return
        end
        L3_2 = openTrading
        L4_2 = "CSC"
        L5_2 = "A"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = closeAllWnd
        L3_2()
        L3_2 = openTrading
        L4_2 = "CSC"
        L5_2 = "B"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = comparePrices
        L3_2()
        L3_2 = buyGoods
        L3_2()
        -- 替换 goto lbl_237，直接跳到函数结束
        return
      else
        -- 移除 lbl_103 标签，这部分逻辑已经在前面处理过了
        if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
          L3_2 = chequeBalance
          L4_2 = _ENV["回城金额"]
          if L3_2 <= L4_2 then
            L3_2 = _ENV["北俱地府"]
            if L3_2 == false then
              L3_2 = openTrading
              L4_2 = "ALG"
              L5_2 = "A"
              L3_2(L4_2, L5_2)
              L3_2 = checkPrice
              L3_2()
              L3_2 = closeAllWnd
              L3_2()
              L3_2 = openTrading
              L4_2 = "ALG"
              L5_2 = "B"
              L3_2(L4_2, L5_2)
              L3_2 = checkPrice
              L3_2()
              L3_2 = comparePrices
              L3_2()
              L3_2 = buyGoods
              L3_2()
          end
        end
        else
          if L2_2 == "ALG" then
            L3_2 = chequeBalance
            L4_2 = _ENV["回城金额"]
            if L3_2 > L4_2 then
              L3_2 = _ENV["北俱地府"]
              if L3_2 == false then
                L3_2 = openTrading
                L4_2 = "ALG"
                L5_2 = "A"
                L3_2(L4_2, L5_2)
                L3_2 = checkPrice
                L3_2()
                L3_2 = closeAllWnd
                L3_2()
                L3_2 = openTrading
                L4_2 = "ALG"
                L5_2 = "B"
                L3_2(L4_2, L5_2)
                L3_2 = checkPrice
                L3_2()
                L3_2 = comparePrices
                L3_2()
                L3_2 = buyGoods
                L3_2()
            end
          end
          else
            if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
              L3_2 = _ENV["北俱地府"]
              if L3_2 then
                L3_2 = chequeBalance
                L4_2 = _ENV["交票金额"]
                if L3_2 < L4_2 then
                  L3_2 = openTrading
                  L4_2 = "BJLZ"
                  L5_2 = "A"
                  L3_2(L4_2, L5_2)
                  L3_2 = checkPrice
                  L3_2()
                  L3_2 = closeAllWnd
                  L3_2()
                  L3_2 = openTrading
                  L4_2 = "BJLZ"
                  L5_2 = "B"
                  L3_2(L4_2, L5_2)
                  L3_2 = checkPrice
                  L3_2()
                  L3_2 = comparePrices
                  L3_2()
                  L3_2 = buyGoods
                  L3_2()
              end
            end
            elseif L2_2 == "DTJW" or L2_2 == "CAC" or L2_2 == "DTGJ" or L2_2 == "DF" then
              L3_2 = chequeBalance
              L4_2 = _ENV["交票金额"]
              if L3_2 < L4_2 then
                L3_2 = openTrading
                L4_2 = "DF"
                L5_2 = "A"
                L3_2(L4_2, L5_2)
                L3_2 = checkPrice
                L3_2()
                L3_2 = closeAllWnd
                L3_2()
                L3_2 = openTrading
                L4_2 = "DF"
                L5_2 = "B"
                L3_2(L4_2, L5_2)
                L3_2 = checkPrice
                L3_2()
                L3_2 = comparePrices
                L3_2()
                L3_2 = buyGoods
                L3_2()
              end
            end
          end
        end
      end
    end
    -- 移除 lbl_237 标签
  end
end

_ENV["跑商_混合"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  _ENV["起步"] = true
  while true do
    L0_2 = {}
    priceList = L0_2
    L0_2 = 0
    L1_2 = ""
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L0_2 = L0_2 + 1
      L1_2 = L6_2.to
      break
    end
    L2_2 = getCurMap
    L2_2 = L2_2()
    if 0 < L0_2 then
      L3_2 = ""
      if (L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" or L2_2 == "CAC" or L2_2 == "DF") and L1_2 == "BJLZ" then
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "B"
        L4_2(L5_2, L6_2)
        L3_2 = "A"
      else
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "A"
        L4_2(L5_2, L6_2)
        L3_2 = "B"
      end
      L4_2 = checkPrice
      L4_2()
      L4_2 = closeAllWnd
      L4_2()
      L4_2 = openTrading
      L5_2 = L1_2
      L6_2 = L3_2
      L4_2(L5_2, L6_2)
      L4_2 = checkPrice
      L4_2()
      L4_2 = comparePrices
      L4_2()
      L4_2 = sellGoods
      L4_2()
      L4_2 = buyGoods
      L4_2()
    else
      L3_2 = chequeBalance
      L4_2 = _ENV["交票金额"]
      if L3_2 >= L4_2 then
        L3_2 = finishTrading
        L3_2()
        break
      else
        if L2_2 == "CAC" then
          L3_2 = _ENV["起步"]
          if L3_2 then
            _ENV["起步"] = false
            L3_2 = openTrading
            L4_2 = "CAC"
            L5_2 = "B"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = closeAllWnd
            L3_2()
            L3_2 = openTrading
            L4_2 = "CAC"
            L5_2 = "A"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = comparePrices
            L3_2()
            L3_2 = buyGoods
            L3_2()
        end
        else
          if L2_2 == "DF" then
            L3_2 = _ENV["起步"]
            if L3_2 then
              _ENV["起步"] = false
              L3_2 = openTrading
              L4_2 = "DF"
              L5_2 = "A"
              L3_2(L4_2, L5_2)
              L3_2 = checkPrice
              L3_2()
              L3_2 = closeAllWnd
              L3_2()
              L3_2 = openTrading
              L4_2 = "DF"
              L5_2 = "B"
              L3_2(L4_2, L5_2)
              L3_2 = checkPrice
              L3_2()
              L3_2 = comparePrices
              L3_2()
              L3_2 = buyGoods
              L3_2()
          end
          elseif L2_2 == "CSC" or L2_2 == "CSJW" then
            L3_2 = openTrading
            L4_2 = "BJLZ"
            L5_2 = "A"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = closeAllWnd
            L3_2()
            L3_2 = openTrading
            L4_2 = "BJLZ"
            L5_2 = "B"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = comparePrices
            L3_2()
            L3_2 = buyGoods
            L3_2()
          elseif L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
            L3_2 = openTrading
            L4_2 = "CSC"
            L5_2 = "A"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = closeAllWnd
            L3_2()
            L3_2 = openTrading
            L4_2 = "CSC"
            L5_2 = "B"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = comparePrices
            L3_2()
            L3_2 = buyGoods
            L3_2()
          elseif L2_2 == "DTJW" or L2_2 == "CAC" or L2_2 == "DTGJ" then
            L3_2 = openTrading
            L4_2 = "DF"
            L5_2 = "A"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = closeAllWnd
            L3_2()
            L3_2 = openTrading
            L4_2 = "DF"
            L5_2 = "B"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = comparePrices
            L3_2()
            L3_2 = buyGoods
            L3_2()
          elseif L2_2 == "DF" then
            L3_2 = openTrading
            L4_2 = "CAC"
            L5_2 = "A"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = closeAllWnd
            L3_2()
            L3_2 = openTrading
            L4_2 = "CAC"
            L5_2 = "B"
            L3_2(L4_2, L5_2)
            L3_2 = checkPrice
            L3_2()
            L3_2 = comparePrices
            L3_2()
            L3_2 = buyGoods
            L3_2()
          end
        end
      end
    end
  end
end

_ENV["跑商_比价换线"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  while true do
    L0_2 = {}
    priceList = L0_2
    L0_2 = 0
    L1_2 = ""
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L0_2 = L0_2 + 1
      L1_2 = L6_2.to
      break
    end
    L2_2 = getCurMap
    L2_2 = L2_2()
    if L2_2 == "" then
      L3_2 = cleanupINF
      L3_2()
    end
    if 0 < L0_2 then
      L3_2 = ""
      if (L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" or L2_2 == "CAC" or L2_2 == "DF") and L1_2 == "BJLZ" then
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "B"
        L4_2(L5_2, L6_2)
        L3_2 = "A"
      else
        L4_2 = openTrading
        L5_2 = L1_2
        L6_2 = "A"
        L4_2(L5_2, L6_2)
        L3_2 = "B"
      end
      L4_2 = checkPrice
      L4_2()
      L4_2 = closeAllWnd
      L4_2()
      L4_2 = openTrading
      L5_2 = L1_2
      L6_2 = L3_2
      L4_2(L5_2, L6_2)
      L4_2 = checkPrice
      L4_2()
      L4_2 = comparePrices
      L4_2()
      L4_2 = sellGoods
      L4_2()
      L4_2 = buyGoods
      L4_2()
    else
      L3_2 = chequeBalance
      L4_2 = _ENV["交票金额"]
      if L3_2 >= L4_2 then
        L3_2 = finishTrading
        L3_2()
        break
      elseif L2_2 == "CSC" or L2_2 == "CSJW" or L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "DTJW" or L2_2 == "DTGJ" or L2_2 == "JNYW" then
        L3_2 = openTrading
        L4_2 = "CSC"
        L5_2 = "A"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = closeAllWnd
        L3_2()
        L3_2 = openTrading
        L4_2 = "CSC"
        L5_2 = "B"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = comparePrices
        L3_2()
        L3_2 = buyGoods
        L3_2()
      elseif L2_2 == "CAC" then
        L3_2 = openTrading
        L4_2 = "CAC"
        L5_2 = "B"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = closeAllWnd
        L3_2()
        L3_2 = openTrading
        L4_2 = "CAC"
        L5_2 = "A"
        L3_2(L4_2, L5_2)
        L3_2 = checkPrice
        L3_2()
        L3_2 = comparePrices
        L3_2()
        L3_2 = buyGoods
        L3_2()
      end
    end
  end
end

_ENV["跑商_长安长寿"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  while true do
    L0_2 = {}
    priceList = L0_2
    L0_2 = 0
    L1_2 = ""
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L0_2 = L0_2 + 1
      L1_2 = L6_2.to
      break
    end
    L2_2 = getCurMap
    L2_2 = L2_2()
    if L2_2 == "" then
      L3_2 = cleanupINF
      L3_2()
    end
    if 0 < L0_2 then
      L3_2 = goToMap
      L4_2 = L1_2
      L5_2 = "walk"
      L3_2(L4_2, L5_2)
      L3_2 = _ENV["联动_比价"]
      L4_2 = L1_2
      L3_2, L4_2 = L3_2(L4_2)
      if L3_2 then
        L5_2 = json
        L5_2 = L5_2.decode
        L6_2 = L4_2
        L5_2 = L5_2(L6_2)
        priceList = L5_2
        _ENV["联动_卖出_ps"] = ""
        _ENV["联动_对比_记录A"] = 0
        _ENV["联动_对比_记录B"] = 0
        L5_2 = pairs
        L6_2 = priceList
        L5_2, L6_2, L7_2 = L5_2(L6_2)
        for L8_2, L9_2 in L5_2, L6_2, L7_2 do
          L10_2 = priceList
          L10_2 = L10_2[L8_2]
          L10_2 = L10_2.A
          if L10_2 < 99999 then
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.B
            if L10_2 < 99999 then
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.A
              L11_2 = priceList
              L11_2 = L11_2[L8_2]
              L11_2 = L11_2.B
              if L10_2 < L11_2 then
                L10_2 = _ENV["联动_对比_记录A"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录A"] = L10_2
              else
                L10_2 = priceList
                L10_2 = L10_2[L8_2]
                L10_2 = L10_2.B
                L11_2 = priceList
                L11_2 = L11_2[L8_2]
                L11_2 = L11_2.A
                if L10_2 < L11_2 then
                  L10_2 = _ENV["联动_对比_记录B"]
                  L10_2 = L10_2 + 1
                  _ENV["联动_对比_记录B"] = L10_2
                end
              end
          end
          else
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.A
            if 99999 < L10_2 then
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.B
              if L10_2 < 99999 and L8_2 ~= "salt" then
                L10_2 = _ENV["联动_对比_记录A"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录A"] = L10_2
            end
            else
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.B
              if 99999 < L10_2 then
                L10_2 = priceList
                L10_2 = L10_2[L8_2]
                L10_2 = L10_2.A
                if L10_2 < 99999 and L8_2 ~= "salt" then
                  L10_2 = _ENV["联动_对比_记录B"]
                  L10_2 = L10_2 + 1
                  _ENV["联动_对比_记录B"] = L10_2
                end
              end
            end
          end
        end
        L5_2 = _ENV["联动_对比_记录A"]
        L6_2 = _ENV["联动_对比_记录B"]
        if L5_2 > L6_2 then
          _ENV["联动_卖出_ps"] = "B"
        else
          _ENV["联动_卖出_ps"] = "A"
        end
        L5_2 = openTrading
        L6_2 = L1_2
        L7_2 = _ENV["联动_卖出_ps"]
        L5_2(L6_2, L7_2)
        L5_2 = _ENV["联动_卖出物品"]
        L6_2 = L1_2
        L5_2(L6_2)
        L5_2 = buyGoods
        L5_2()
      else
        L5_2 = ""
        if (L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" or L2_2 == "CAC" or L2_2 == "DF") and L1_2 == "BJLZ" then
          L6_2 = openTrading
          L7_2 = L1_2
          L8_2 = "B"
          L6_2(L7_2, L8_2)
          L5_2 = "A"
        else
          L6_2 = openTrading
          L7_2 = L1_2
          L8_2 = "A"
          L6_2(L7_2, L8_2)
          L5_2 = "B"
        end
        L6_2 = checkPrice
        L6_2()
        L6_2 = closeAllWnd
        L6_2()
        L6_2 = openTrading
        L7_2 = L1_2
        L8_2 = L5_2
        L6_2(L7_2, L8_2)
        L6_2 = checkPrice
        L6_2()
        L6_2 = comparePrices
        L6_2()
        L6_2 = sellGoods
        L6_2()
        L6_2 = buyGoods
        L6_2()
      end
    else
      L3_2 = chequeBalance
      L4_2 = _ENV["交票金额"]
      if L3_2 >= L4_2 then
        L3_2 = finishTrading
        L3_2()
        break
      elseif L2_2 == "CSC" or L2_2 == "CSJW" or L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "DTJW" or L2_2 == "DTGJ" or L2_2 == "JNYW" then
        L3_2 = goToMap
        L4_2 = "CSC"
        L5_2 = "walk"
        L3_2(L4_2, L5_2)
        L3_2 = _ENV["联动_比价"]
        L4_2 = "CSC"
        L3_2, L4_2 = L3_2(L4_2)
        if L3_2 then
          L5_2 = json
          L5_2 = L5_2.decode
          L6_2 = L4_2
          L5_2 = L5_2(L6_2)
          priceList = L5_2
          _ENV["联动_购买_ps"] = ""
          _ENV["联动_对比_记录A"] = 0
          _ENV["联动_对比_记录B"] = 0
          L5_2 = pairs
          L6_2 = priceList
          L5_2, L6_2, L7_2 = L5_2(L6_2)
          for L8_2, L9_2 in L5_2, L6_2, L7_2 do
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.theRightOne
            if L10_2 == "A" then
              L10_2 = _ENV["联动_对比_记录A"]
              L10_2 = L10_2 + 1
              _ENV["联动_对比_记录A"] = L10_2
            else
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.theRightOne
              if L10_2 == "B" then
                L10_2 = _ENV["联动_对比_记录B"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录B"] = L10_2
              end
            end
          end
          L5_2 = _ENV["联动_对比_记录A"]
          L6_2 = _ENV["联动_对比_记录B"]
          if L5_2 > L6_2 then
            _ENV["联动_购买_ps"] = "A"
          else
            L5_2 = _ENV["联动_对比_记录B"]
            L6_2 = _ENV["联动_对比_记录A"]
            if L5_2 > L6_2 then
              _ENV["联动_购买_ps"] = "B"
            end
          end
          L5_2 = _ENV["联动_购买_ps"]
          if L5_2 ~= "" then
            L5_2 = openTrading
            L6_2 = "CSC"
            L7_2 = _ENV["联动_购买_ps"]
            L5_2(L6_2, L7_2)
            L5_2 = buyGoods
            L5_2()
            L5_2 = _ENV["判断表数量"]
            L6_2 = myBag
            L5_2 = L5_2(L6_2)
            if L5_2 then
              L5_2 = goTo
              L6_2 = constPos
              L6_2 = L6_2.merchantPos
              L6_2 = L6_2.A
              L6_2 = L6_2.CSC
              L7_2 = "CSC"
              L5_2(L6_2, L7_2)
              L5_2 = _ENV["_通用"]
              L5_2 = L5_2["跑商等刷"]
              L5_2()
            end
          else
            L5_2 = goTo
            L6_2 = constPos
            L6_2 = L6_2.merchantPos
            L6_2 = L6_2.A
            L6_2 = L6_2.CSC
            L7_2 = "CSC"
            L5_2(L6_2, L7_2)
            L5_2 = _ENV["_通用"]
            L5_2 = L5_2["跑商等刷"]
            L5_2()
          end
        else
          L5_2 = openTrading
          L6_2 = "CSC"
          L7_2 = "A"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = closeAllWnd
          L5_2()
          L5_2 = openTrading
          L6_2 = "CSC"
          L7_2 = "B"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = comparePrices
          L5_2()
          L5_2 = buyGoods
          L5_2()
        end
      elseif L2_2 == "CAC" then
        L3_2 = goToMap
        L4_2 = "CAC"
        L5_2 = "walk"
        L3_2(L4_2, L5_2)
        L3_2 = _ENV["联动_比价"]
        L4_2 = "CAC"
        L3_2, L4_2 = L3_2(L4_2)
        if L3_2 then
          L5_2 = json
          L5_2 = L5_2.decode
          L6_2 = L4_2
          L5_2 = L5_2(L6_2)
          priceList = L5_2
          _ENV["联动_购买_ps"] = ""
          _ENV["联动_对比_记录A"] = 0
          _ENV["联动_对比_记录B"] = 0
          L5_2 = pairs
          L6_2 = priceList
          L5_2, L6_2, L7_2 = L5_2(L6_2)
          for L8_2, L9_2 in L5_2, L6_2, L7_2 do
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.theRightOne
            if L10_2 == "A" then
              L10_2 = _ENV["联动_对比_记录A"]
              L10_2 = L10_2 + 1
              _ENV["联动_对比_记录A"] = L10_2
            else
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.theRightOne
              if L10_2 == "B" then
                L10_2 = _ENV["联动_对比_记录B"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录B"] = L10_2
              end
            end
          end
          L5_2 = _ENV["联动_对比_记录A"]
          L6_2 = _ENV["联动_对比_记录B"]
          if L5_2 > L6_2 then
            _ENV["联动_购买_ps"] = "A"
          else
            L5_2 = _ENV["联动_对比_记录B"]
            L6_2 = _ENV["联动_对比_记录A"]
            if L5_2 > L6_2 then
              _ENV["联动_购买_ps"] = "B"
            end
          end
          L5_2 = _ENV["联动_购买_ps"]
          if L5_2 ~= "" then
            L5_2 = openTrading
            L6_2 = "CAC"
            L7_2 = _ENV["联动_购买_ps"]
            L5_2(L6_2, L7_2)
            L5_2 = buyGoods
            L5_2()
            L5_2 = _ENV["判断表数量"]
            L6_2 = myBag
            L5_2 = L5_2(L6_2)
            if L5_2 then
              L5_2 = goTo
              L6_2 = constPos
              L6_2 = L6_2.merchantPos
              L6_2 = L6_2.B
              L6_2 = L6_2.CAC
              L7_2 = "CAC"
              L5_2(L6_2, L7_2)
              L5_2 = _ENV["_通用"]
              L5_2 = L5_2["跑商等刷"]
              L5_2()
            end
          else
            L5_2 = goTo
            L6_2 = constPos
            L6_2 = L6_2.merchantPos
            L6_2 = L6_2.B
            L6_2 = L6_2.CAC
            L7_2 = "CAC"
            L5_2(L6_2, L7_2)
            L5_2 = _ENV["_通用"]
            L5_2 = L5_2["跑商等刷"]
            L5_2()
          end
        else
          L5_2 = openTrading
          L6_2 = "CAC"
          L7_2 = "B"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = closeAllWnd
          L5_2()
          L5_2 = openTrading
          L6_2 = "CAC"
          L7_2 = "A"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = comparePrices
          L5_2()
          L5_2 = buyGoods
          L5_2()
        end
      end
    end
  end
end

_ENV["联动_跑商长安长寿"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  while true do
    L0_2 = {}
    priceList = L0_2
    L0_2 = 0
    L1_2 = ""
    L2_2 = pairs
    L3_2 = myBag
    L2_2, L3_2, L4_2 = L2_2(L3_2)
    for L5_2, L6_2 in L2_2, L3_2, L4_2 do
      L0_2 = L0_2 + 1
      L1_2 = L6_2.to
      break
    end
    L2_2 = getCurMap
    L2_2 = L2_2()
    if L2_2 == "" then
      L3_2 = cleanupINF
      L3_2()
    end
    if 0 < L0_2 then
      while true do
        L3_2 = getCurMap
        L3_2 = L3_2()
        if L1_2 == L3_2 then
          break
        end
        L3_2 = goToMap
        L4_2 = L1_2
        L5_2 = "walk"
        L3_2(L4_2, L5_2)
      end
      L3_2 = _ENV["联动_比价"]
      L4_2 = L1_2
      L3_2, L4_2 = L3_2(L4_2)
      if L3_2 then
        L5_2 = toast
        L6_2 = 0
        L7_2 = 1
        L5_2(L6_2, L7_2)
        L5_2 = json
        L5_2 = L5_2.decode
        L6_2 = L4_2
        L5_2 = L5_2(L6_2)
        priceList = L5_2
        _ENV["联动_卖出_ps"] = ""
        _ENV["联动_对比_记录A"] = 0
        _ENV["联动_对比_记录B"] = 0
        L5_2 = pairs
        L6_2 = priceList
        L5_2, L6_2, L7_2 = L5_2(L6_2)
        for L8_2, L9_2 in L5_2, L6_2, L7_2 do
          L10_2 = priceList
          L10_2 = L10_2[L8_2]
          L10_2 = L10_2.A
          if L10_2 < 99999 then
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.B
            if L10_2 < 99999 then
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.A
              L11_2 = priceList
              L11_2 = L11_2[L8_2]
              L11_2 = L11_2.B
              if L10_2 < L11_2 then
                L10_2 = _ENV["联动_对比_记录A"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录A"] = L10_2
              else
                L10_2 = priceList
                L10_2 = L10_2[L8_2]
                L10_2 = L10_2.B
                L11_2 = priceList
                L11_2 = L11_2[L8_2]
                L11_2 = L11_2.A
                if L10_2 < L11_2 then
                  L10_2 = _ENV["联动_对比_记录B"]
                  L10_2 = L10_2 + 1
                  _ENV["联动_对比_记录B"] = L10_2
                end
              end
          end
          else
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.A
            if 99999 < L10_2 then
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.B
              if L10_2 < 99999 and L8_2 ~= "salt" then
                L10_2 = _ENV["联动_对比_记录A"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录A"] = L10_2
            end
            else
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.B
              if 99999 < L10_2 then
                L10_2 = priceList
                L10_2 = L10_2[L8_2]
                L10_2 = L10_2.A
                if L10_2 < 99999 and L8_2 ~= "salt" then
                  L10_2 = _ENV["联动_对比_记录B"]
                  L10_2 = L10_2 + 1
                  _ENV["联动_对比_记录B"] = L10_2
                end
              end
            end
          end
        end
        L5_2 = _ENV["联动_对比_记录A"]
        L6_2 = _ENV["联动_对比_记录B"]
        if L5_2 > L6_2 then
          _ENV["联动_卖出_ps"] = "B"
        else
          _ENV["联动_卖出_ps"] = "A"
        end
        L5_2 = openTrading
        L6_2 = L1_2
        L7_2 = _ENV["联动_卖出_ps"]
        L5_2(L6_2, L7_2)
        L5_2 = _ENV["联动_卖出物品"]
        L6_2 = L1_2
        L5_2(L6_2)
        L5_2 = buyGoods
        L5_2()
      else
        L5_2 = toast
        L6_2 = 1
        L7_2 = 1
        L5_2(L6_2, L7_2)
        L5_2 = ""
        if (L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" or L2_2 == "CAC" or L2_2 == "DF") and L1_2 == "BJLZ" then
          L6_2 = openTrading
          L7_2 = L1_2
          L8_2 = "B"
          L6_2(L7_2, L8_2)
          L5_2 = "A"
        else
          L6_2 = openTrading
          L7_2 = L1_2
          L8_2 = "A"
          L6_2(L7_2, L8_2)
          L5_2 = "B"
        end
        L6_2 = checkPrice
        L6_2()
        L6_2 = closeAllWnd
        L6_2()
        L6_2 = openTrading
        L7_2 = L1_2
        L8_2 = L5_2
        L6_2(L7_2, L8_2)
        L6_2 = checkPrice
        L6_2()
        L6_2 = comparePrices
        L6_2()
        L6_2 = sellGoods
        L6_2()
        L6_2 = buyGoods
        L6_2()
      end
    else
      L3_2 = chequeBalance
      L4_2 = _ENV["交票金额"]
      if L3_2 >= L4_2 then
        L3_2 = finishTrading
        L3_2()
        break
      else
        if L2_2 == "CSC" or L2_2 == "CSJW" or (L2_2 == "BJLZ" and chequeBalance > _ENV["回城金额"] and _ENV["北俱地府"] == false) then
          -- 继续执行交易逻辑
        else
          -- 替换 goto lbl_314，直接执行对应逻辑
          if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
            L3_2 = chequeBalance
            L4_2 = _ENV["回城金额"]
            if L3_2 <= L4_2 then
              L3_2 = _ENV["北俱地府"]
              if L3_2 == false then
                L3_2 = goToMap
                L4_2 = "BJLZ"
                L3_2(L4_2)
              end
            end
          end
          return
        end
        L3_2 = goToMap
        L4_2 = "CSC"
        L5_2 = "walk"
        L3_2(L4_2, L5_2)
        L3_2 = _ENV["联动_比价"]
        L4_2 = "CSC"
        L3_2, L4_2 = L3_2(L4_2)
        if L3_2 then
          L5_2 = json
          L5_2 = L5_2.decode
          L6_2 = L4_2
          L5_2 = L5_2(L6_2)
          priceList = L5_2
          _ENV["联动_购买_ps"] = ""
          _ENV["联动_对比_记录A"] = 0
          _ENV["联动_对比_记录B"] = 0
          L5_2 = pairs
          L6_2 = priceList
          L5_2, L6_2, L7_2 = L5_2(L6_2)
          for L8_2, L9_2 in L5_2, L6_2, L7_2 do
            L10_2 = priceList
            L10_2 = L10_2[L8_2]
            L10_2 = L10_2.theRightOne
            if L10_2 == "A" then
              L10_2 = _ENV["联动_对比_记录A"]
              L10_2 = L10_2 + 1
              _ENV["联动_对比_记录A"] = L10_2
            else
              L10_2 = priceList
              L10_2 = L10_2[L8_2]
              L10_2 = L10_2.theRightOne
              if L10_2 == "B" then
                L10_2 = _ENV["联动_对比_记录B"]
                L10_2 = L10_2 + 1
                _ENV["联动_对比_记录B"] = L10_2
              end
            end
          end
          L5_2 = _ENV["联动_对比_记录A"]
          L6_2 = _ENV["联动_对比_记录B"]
          if L5_2 > L6_2 then
            _ENV["联动_购买_ps"] = "A"
          else
            L5_2 = _ENV["联动_对比_记录B"]
            L6_2 = _ENV["联动_对比_记录A"]
            if L5_2 > L6_2 then
              _ENV["联动_购买_ps"] = "B"
            end
          end
          L5_2 = _ENV["联动_购买_ps"]
          if L5_2 ~= "" then
            L5_2 = openTrading
            L6_2 = "CSC"
            L7_2 = _ENV["联动_购买_ps"]
            L5_2(L6_2, L7_2)
            L5_2 = buyGoods
            L5_2()
            L5_2 = _ENV["判断表数量"]
            L6_2 = myBag
            L5_2 = L5_2(L6_2)
            if L5_2 then
              L5_2 = goTo
              L6_2 = constPos
              L6_2 = L6_2.merchantPos
              L6_2 = L6_2.A
              L6_2 = L6_2.CSC
              L7_2 = "CSC"
              L5_2(L6_2, L7_2)
              L5_2 = _ENV["_通用"]
              L5_2 = L5_2["跑商等刷"]
              L5_2()
            end
          else
            L5_2 = goTo
            L6_2 = constPos
            L6_2 = L6_2.merchantPos
            L6_2 = L6_2.A
            L6_2 = L6_2.CSC
            L7_2 = "CSC"
            L5_2(L6_2, L7_2)
            L5_2 = _ENV["_通用"]
            L5_2 = L5_2["跑商等刷"]
            L5_2()
          end
        else
          L5_2 = openTrading
          L6_2 = "CSC"
          L7_2 = "A"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = closeAllWnd
          L5_2()
          L5_2 = openTrading
          L6_2 = "CSC"
          L7_2 = "B"
          L5_2(L6_2, L7_2)
          L5_2 = checkPrice
          L5_2()
          L5_2 = comparePrices
          L5_2()
          L5_2 = buyGoods
          L5_2()
          goto lbl_796
          ::lbl_314::
          if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
            L3_2 = chequeBalance
            L4_2 = _ENV["回城金额"]
            if L3_2 <= L4_2 then
              L3_2 = _ENV["北俱地府"]
              if L3_2 == false then
                L3_2 = goToMap
                L4_2 = "ALG"
                L5_2 = "walk"
                L3_2(L4_2, L5_2)
                L3_2 = _ENV["联动_比价"]
                L4_2 = "ALG"
                L3_2, L4_2 = L3_2(L4_2)
                if L3_2 then
                  L5_2 = json
                  L5_2 = L5_2.decode
                  L6_2 = L4_2
                  L5_2 = L5_2(L6_2)
                  priceList = L5_2
                  _ENV["联动_购买_ps"] = ""
                  _ENV["联动_对比_记录A"] = 0
                  _ENV["联动_对比_记录B"] = 0
                  L5_2 = pairs
                  L6_2 = priceList
                  L5_2, L6_2, L7_2 = L5_2(L6_2)
                  for L8_2, L9_2 in L5_2, L6_2, L7_2 do
                    L10_2 = priceList
                    L10_2 = L10_2[L8_2]
                    L10_2 = L10_2.theRightOne
                    if L10_2 == "A" then
                      L10_2 = _ENV["联动_对比_记录A"]
                      L10_2 = L10_2 + 1
                      _ENV["联动_对比_记录A"] = L10_2
                    else
                      L10_2 = priceList
                      L10_2 = L10_2[L8_2]
                      L10_2 = L10_2.theRightOne
                      if L10_2 == "B" then
                        L10_2 = _ENV["联动_对比_记录B"]
                        L10_2 = L10_2 + 1
                        _ENV["联动_对比_记录B"] = L10_2
                      end
                    end
                  end
                  L5_2 = _ENV["联动_对比_记录A"]
                  L6_2 = _ENV["联动_对比_记录B"]
                  if L5_2 > L6_2 then
                    _ENV["联动_购买_ps"] = "A"
                  else
                    L5_2 = _ENV["联动_对比_记录B"]
                    L6_2 = _ENV["联动_对比_记录A"]
                    if L5_2 > L6_2 then
                      _ENV["联动_购买_ps"] = "B"
                    end
                  end
                  L5_2 = _ENV["联动_购买_ps"]
                  if L5_2 ~= "" then
                    L5_2 = openTrading
                    L6_2 = "ALG"
                    L7_2 = _ENV["联动_购买_ps"]
                    L5_2(L6_2, L7_2)
                    L5_2 = buyGoods
                    L5_2()
                    L5_2 = _ENV["判断表数量"]
                    L6_2 = myBag
                    L5_2 = L5_2(L6_2)
                    if L5_2 then
                      L5_2 = goTo
                      L6_2 = constPos
                      L6_2 = L6_2.merchantPos
                      L6_2 = L6_2.A
                      L6_2 = L6_2.ALG
                      L7_2 = "ALG"
                      L5_2(L6_2, L7_2)
                      L5_2 = _ENV["_通用"]
                      L5_2 = L5_2["跑商等刷"]
                      L5_2()
                    end
                  else
                    L5_2 = goTo
                    L6_2 = constPos
                    L6_2 = L6_2.merchantPos
                    L6_2 = L6_2.A
                    L6_2 = L6_2.ALG
                    L7_2 = "ALG"
                    L5_2(L6_2, L7_2)
                    L5_2 = _ENV["_通用"]
                    L5_2 = L5_2["跑商等刷"]
                    L5_2()
                  end
                else
                  L5_2 = openTrading
                  L6_2 = "ALG"
                  L7_2 = "A"
                  L5_2(L6_2, L7_2)
                  L5_2 = checkPrice
                  L5_2()
                  L5_2 = closeAllWnd
                  L5_2()
                  L5_2 = openTrading
                  L6_2 = "ALG"
                  L7_2 = "B"
                  L5_2(L6_2, L7_2)
                  L5_2 = checkPrice
                  L5_2()
                  L5_2 = comparePrices
                  L5_2()
                  L5_2 = buyGoods
                  L5_2()
                end
            end
          end
          else
            if L2_2 == "ALG" then
              L3_2 = chequeBalance
              L4_2 = _ENV["回城金额"]
              if L3_2 > L4_2 then
                L3_2 = _ENV["北俱地府"]
                if L3_2 == false then
                  L3_2 = goToMap
                  L4_2 = "ALG"
                  L5_2 = "walk"
                  L3_2(L4_2, L5_2)
                  L3_2 = _ENV["联动_比价"]
                  L4_2 = "ALG"
                  L3_2, L4_2 = L3_2(L4_2)
                  if L3_2 then
                    L5_2 = json
                    L5_2 = L5_2.decode
                    L6_2 = L4_2
                    L5_2 = L5_2(L6_2)
                    priceList = L5_2
                    _ENV["联动_购买_ps"] = ""
                    _ENV["联动_对比_记录A"] = 0
                    _ENV["联动_对比_记录B"] = 0
                    L5_2 = pairs
                    L6_2 = priceList
                    L5_2, L6_2, L7_2 = L5_2(L6_2)
                    for L8_2, L9_2 in L5_2, L6_2, L7_2 do
                      L10_2 = priceList
                      L10_2 = L10_2[L8_2]
                      L10_2 = L10_2.theRightOne
                      if L10_2 == "A" then
                        L10_2 = _ENV["联动_对比_记录A"]
                        L10_2 = L10_2 + 1
                        _ENV["联动_对比_记录A"] = L10_2
                      else
                        L10_2 = priceList
                        L10_2 = L10_2[L8_2]
                        L10_2 = L10_2.theRightOne
                        if L10_2 == "B" then
                          L10_2 = _ENV["联动_对比_记录B"]
                          L10_2 = L10_2 + 1
                          _ENV["联动_对比_记录B"] = L10_2
                        end
                      end
                    end
                    L5_2 = _ENV["联动_对比_记录A"]
                    L6_2 = _ENV["联动_对比_记录B"]
                    if L5_2 > L6_2 then
                      _ENV["联动_购买_ps"] = "A"
                    else
                      L5_2 = _ENV["联动_对比_记录B"]
                      L6_2 = _ENV["联动_对比_记录A"]
                      if L5_2 > L6_2 then
                        _ENV["联动_购买_ps"] = "B"
                      end
                    end
                    L5_2 = _ENV["联动_购买_ps"]
                    if L5_2 ~= "" then
                      L5_2 = openTrading
                      L6_2 = "ALG"
                      L7_2 = _ENV["联动_购买_ps"]
                      L5_2(L6_2, L7_2)
                      L5_2 = buyGoods
                      L5_2()
                      L5_2 = _ENV["判断表数量"]
                      L6_2 = myBag
                      L5_2 = L5_2(L6_2)
                      if L5_2 then
                        L5_2 = goTo
                        L6_2 = constPos
                        L6_2 = L6_2.merchantPos
                        L6_2 = L6_2.A
                        L6_2 = L6_2.ALG
                        L7_2 = "ALG"
                        L5_2(L6_2, L7_2)
                        L5_2 = _ENV["_通用"]
                        L5_2 = L5_2["跑商等刷"]
                        L5_2()
                      end
                    else
                      L5_2 = goTo
                      L6_2 = constPos
                      L6_2 = L6_2.merchantPos
                      L6_2 = L6_2.A
                      L6_2 = L6_2.ALG
                      L7_2 = "ALG"
                      L5_2(L6_2, L7_2)
                      L5_2 = _ENV["_通用"]
                      L5_2 = L5_2["跑商等刷"]
                      L5_2()
                    end
                  else
                    L5_2 = openTrading
                    L6_2 = "ALG"
                    L7_2 = "A"
                    L5_2(L6_2, L7_2)
                    L5_2 = checkPrice
                    L5_2()
                    L5_2 = closeAllWnd
                    L5_2()
                    L5_2 = openTrading
                    L6_2 = "ALG"
                    L7_2 = "B"
                    L5_2(L6_2, L7_2)
                    L5_2 = checkPrice
                    L5_2()
                    L5_2 = comparePrices
                    L5_2()
                    L5_2 = buyGoods
                    L5_2()
                  end
              end
            end
            else
              if L2_2 == "BJLZ" or L2_2 == "HGS" or L2_2 == "ALG" or L2_2 == "DHW" or L2_2 == "JYC" or L2_2 == "JNYW" then
                L3_2 = _ENV["北俱地府"]
                if L3_2 then
                  L3_2 = chequeBalance
                  L4_2 = _ENV["交票金额"]
                  if L3_2 < L4_2 then
                    L3_2 = goToMap
                    L4_2 = "BJLZ"
                    L5_2 = "walk"
                    L3_2(L4_2, L5_2)
                    L3_2 = _ENV["联动_比价"]
                    L4_2 = "BJLZ"
                    L3_2, L4_2 = L3_2(L4_2)
                    if L3_2 then
                      L5_2 = json
                      L5_2 = L5_2.decode
                      L6_2 = L4_2
                      L5_2 = L5_2(L6_2)
                      priceList = L5_2
                      _ENV["联动_购买_ps"] = ""
                      _ENV["联动_对比_记录A"] = 0
                      _ENV["联动_对比_记录B"] = 0
                      L5_2 = pairs
                      L6_2 = priceList
                      L5_2, L6_2, L7_2 = L5_2(L6_2)
                      for L8_2, L9_2 in L5_2, L6_2, L7_2 do
                        L10_2 = priceList
                        L10_2 = L10_2[L8_2]
                        L10_2 = L10_2.theRightOne
                        if L10_2 == "A" then
                          L10_2 = _ENV["联动_对比_记录A"]
                          L10_2 = L10_2 + 1
                          _ENV["联动_对比_记录A"] = L10_2
                        else
                          L10_2 = priceList
                          L10_2 = L10_2[L8_2]
                          L10_2 = L10_2.theRightOne
                          if L10_2 == "B" then
                            L10_2 = _ENV["联动_对比_记录B"]
                            L10_2 = L10_2 + 1
                            _ENV["联动_对比_记录B"] = L10_2
                          end
                        end
                      end
                      L5_2 = _ENV["联动_对比_记录A"]
                      L6_2 = _ENV["联动_对比_记录B"]
                      if L5_2 > L6_2 then
                        _ENV["联动_购买_ps"] = "A"
                      else
                        L5_2 = _ENV["联动_对比_记录B"]
                        L6_2 = _ENV["联动_对比_记录A"]
                        if L5_2 > L6_2 then
                          _ENV["联动_购买_ps"] = "B"
                        end
                      end
                      L5_2 = _ENV["联动_购买_ps"]
                      if L5_2 ~= "" then
                        L5_2 = openTrading
                        L6_2 = "BJLZ"
                        L7_2 = _ENV["联动_购买_ps"]
                        L5_2(L6_2, L7_2)
                        L5_2 = buyGoods
                        L5_2()
                        L5_2 = _ENV["判断表数量"]
                        L6_2 = myBag
                        L5_2 = L5_2(L6_2)
                        if L5_2 then
                          L5_2 = goTo
                          L6_2 = constPos
                          L6_2 = L6_2.merchantPos
                          L6_2 = L6_2.A
                          L6_2 = L6_2.BJLZ
                          L7_2 = "BJLZ"
                          L5_2(L6_2, L7_2)
                          L5_2 = _ENV["_通用"]
                          L5_2 = L5_2["跑商等刷"]
                          L5_2()
                        end
                      else
                        L5_2 = goTo
                        L6_2 = constPos
                        L6_2 = L6_2.merchantPos
                        L6_2 = L6_2.A
                        L6_2 = L6_2.BJLZ
                        L7_2 = "BJLZ"
                        L5_2(L6_2, L7_2)
                        L5_2 = _ENV["_通用"]
                        L5_2 = L5_2["跑商等刷"]
                        L5_2()
                      end
                    else
                      L5_2 = openTrading
                      L6_2 = "BJLZ"
                      L7_2 = "A"
                      L5_2(L6_2, L7_2)
                      L5_2 = checkPrice
                      L5_2()
                      L5_2 = closeAllWnd
                      L5_2()
                      L5_2 = openTrading
                      L6_2 = "BJLZ"
                      L7_2 = "B"
                      L5_2(L6_2, L7_2)
                      L5_2 = checkPrice
                      L5_2()
                      L5_2 = comparePrices
                      L5_2()
                      L5_2 = buyGoods
                      L5_2()
                    end
                end
              end
              elseif L2_2 == "DTJW" or L2_2 == "CAC" or L2_2 == "DTGJ" or L2_2 == "DF" then
                L3_2 = chequeBalance
                L4_2 = _ENV["交票金额"]
                if L3_2 < L4_2 then
                  L3_2 = goToMap
                  L4_2 = "DF"
                  L5_2 = "walk"
                  L3_2(L4_2, L5_2)
                  L3_2 = _ENV["联动_比价"]
                  L4_2 = "DF"
                  L3_2, L4_2 = L3_2(L4_2)
                  if L3_2 then
                    L5_2 = json
                    L5_2 = L5_2.decode
                    L6_2 = L4_2
                    L5_2 = L5_2(L6_2)
                    priceList = L5_2
                    _ENV["联动_购买_ps"] = ""
                    _ENV["联动_对比_记录A"] = 0
                    _ENV["联动_对比_记录B"] = 0
                    L5_2 = pairs
                    L6_2 = priceList
                    L5_2, L6_2, L7_2 = L5_2(L6_2)
                    for L8_2, L9_2 in L5_2, L6_2, L7_2 do
                      L10_2 = priceList
                      L10_2 = L10_2[L8_2]
                      L10_2 = L10_2.theRightOne
                      if L10_2 == "A" then
                        L10_2 = _ENV["联动_对比_记录A"]
                        L10_2 = L10_2 + 1
                        _ENV["联动_对比_记录A"] = L10_2
                      else
                        L10_2 = priceList
                        L10_2 = L10_2[L8_2]
                        L10_2 = L10_2.theRightOne
                        if L10_2 == "B" then
                          L10_2 = _ENV["联动_对比_记录B"]
                          L10_2 = L10_2 + 1
                          _ENV["联动_对比_记录B"] = L10_2
                        end
                      end
                    end
                    L5_2 = _ENV["联动_对比_记录A"]
                    L6_2 = _ENV["联动_对比_记录B"]
                    if L5_2 > L6_2 then
                      _ENV["联动_购买_ps"] = "A"
                    else
                      L5_2 = _ENV["联动_对比_记录B"]
                      L6_2 = _ENV["联动_对比_记录A"]
                      if L5_2 > L6_2 then
                        _ENV["联动_购买_ps"] = "B"
                      end
                    end
                    L5_2 = _ENV["联动_购买_ps"]
                    if L5_2 ~= "" then
                      L5_2 = openTrading
                      L6_2 = "DF"
                      L7_2 = _ENV["联动_购买_ps"]
                      L5_2(L6_2, L7_2)
                      L5_2 = buyGoods
                      L5_2()
                      L5_2 = _ENV["判断表数量"]
                      L6_2 = myBag
                      L5_2 = L5_2(L6_2)
                      if L5_2 then
                        L5_2 = goTo
                        L6_2 = constPos
                        L6_2 = L6_2.merchantPos
                        L6_2 = L6_2.A
                        L6_2 = L6_2.DF
                        L7_2 = "DF"
                        L5_2(L6_2, L7_2)
                        L5_2 = _ENV["_通用"]
                        L5_2 = L5_2["跑商等刷"]
                        L5_2()
                      end
                    else
                      L5_2 = goTo
                      L6_2 = constPos
                      L6_2 = L6_2.merchantPos
                      L6_2 = L6_2.A
                      L6_2 = L6_2.DF
                      L7_2 = "DF"
                      L5_2(L6_2, L7_2)
                      L5_2 = _ENV["_通用"]
                      L5_2 = L5_2["跑商等刷"]
                      L5_2()
                    end
                  else
                    L5_2 = openTrading
                    L6_2 = "DF"
                    L7_2 = "A"
                    L5_2(L6_2, L7_2)
                    L5_2 = checkPrice
                    L5_2()
                    L5_2 = closeAllWnd
                    L5_2()
                    L5_2 = openTrading
                    L6_2 = "DF"
                    L7_2 = "B"
                    L5_2(L6_2, L7_2)
                    L5_2 = checkPrice
                    L5_2()
                    L5_2 = comparePrices
                    L5_2()
                    L5_2 = buyGoods
                    L5_2()
                  end
                end
              end
            end
          end
        end
      end
    end
    ::lbl_796::
  end
end

_ENV["联动_跑商混合"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  if A0_2 == "DF" then
    _ENV["地图1"] = "BJLZ"
  elseif A0_2 == "ALG" or A0_2 == "CAC" then
    _ENV["地图1"] = "CSC"
  elseif A0_2 == "CSC" then
    L1_2 = _ENV["UI_跑商路线"]
    if L1_2 == "长安长寿" then
      _ENV["地图1"] = "CAC"
    else
      _ENV["地图1"] = "ALG"
    end
  elseif A0_2 == "BJLZ" then
    _ENV["地图1"] = "DF"
  end
  L1_2 = pairs
  L2_2 = color
  L2_2 = L2_2.goods
  L3_2 = _ENV["地图1"]
  L2_2 = L2_2[L3_2]
  L1_2, L2_2, L3_2 = L1_2(L2_2)
  for L4_2, L5_2 in L1_2, L2_2, L3_2 do
    L6_2 = {}
    L7_2 = 999
    L8_2 = 184
    L9_2 = 1666
    L10_2 = 718
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L6_2[3] = L9_2
    L6_2[4] = L10_2
    L7_2 = {}
    L8_2 = L5_2[1]
    L9_2 = L5_2[2]
    L10_2 = L5_2[3]
    L11_2 = L6_2[1]
    L12_2 = L6_2[2]
    L13_2 = L6_2[3]
    L14_2 = L6_2[4]
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    L7_2[3] = L10_2
    L7_2[4] = L11_2
    L7_2[5] = L12_2
    L7_2[6] = L13_2
    L7_2[7] = L14_2
    L8_2 = findAndTap
    L9_2 = L7_2
    L10_2 = "singleTap"
    L11_2 = 20
    L8_2 = L8_2(L9_2, L10_2, L11_2)
    if L8_2 then
      L8_2 = 1
      L9_2 = 10
      L10_2 = 1
      for L11_2 = L8_2, L9_2, L10_2 do
        L12_2 = _find
        L13_2 = Color
        L13_2 = L13_2["跑商"]
        L13_2 = L13_2["最大"]
        L12_2 = L12_2(L13_2)
        if L12_2 then
          L12_2 = mSleep
          L13_2 = math
          L13_2 = L13_2.random
          L14_2 = 250
          L15_2 = 350
          L13_2, L14_2, L15_2, L16_2 = L13_2(L14_2, L15_2)
          L12_2(L13_2, L14_2, L15_2, L16_2)
        end
        L12_2 = _find
        L13_2 = Color
        L13_2 = L13_2["跑商"]
        L13_2 = L13_2["卖出"]
        L12_2 = L12_2(L13_2)
        if L12_2 then
          L12_2 = mSleep
          L13_2 = math
          L13_2 = L13_2.random
          L14_2 = 550
          L15_2 = 750
          L13_2, L14_2, L15_2, L16_2 = L13_2(L14_2, L15_2)
          L12_2(L13_2, L14_2, L15_2, L16_2)
        end
        L12_2 = findAndTap
        L13_2 = L7_2
        L14_2 = "singleTap"
        L15_2 = 20
        L12_2 = L12_2(L13_2, L14_2, L15_2)
        if L12_2 == false then
          break
        end
        L12_2 = mSleep
        L13_2 = 400
        L12_2(L13_2)
      end
      L8_2 = mSleep
      L9_2 = math
      L9_2 = L9_2.random
      L10_2 = 850
      L11_2 = 1250
      L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L9_2(L10_2, L11_2)
      L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
      L8_2 = checkBalance
      L8_2()
    end
  end
  L1_2 = tradingMap
  if L1_2 ~= "BJLZ" then
    L1_2 = tradingMap
    if L1_2 ~= "CAC" then
      goto lbl_117
    end
  end
  L1_2 = _ENV["北俱地府"]
  if L1_2 == false then
    L1_2 = closeAllWnd
    L1_2()
  end
  ::lbl_117::
  L1_2 = {}
  myBag = L1_2
end

_ENV["联动_卖出物品"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = _ENV["青松_对象操作"]
  L2_2 = "读取对象"
  L3_2 = _ENV["_联动_服务器名"]
  L4_2 = A0_2
  L1_2, L2_2 = L1_2(L2_2, L3_2, L4_2)
  _ENV["执行结果"] = L2_2
  _ENV["执行状态"] = L1_2
  L1_2 = _ENV["执行状态"]
  if L1_2 then
    L1_2 = _ENV["执行结果"]
    if L1_2 ~= nil then
      L1_2 = _ENV["执行结果"]
      if L1_2 ~= "" then
        L1_2 = string
        L1_2 = L1_2.len
        L2_2 = _ENV["执行结果"]
        L1_2 = L1_2(L2_2)
        if 6 < L1_2 then
          L1_2 = strSplit
          L2_2 = _ENV["执行结果"]
          L3_2 = ";"
          L1_2 = L1_2(L2_2, L3_2)
          data = L1_2
          L1_2 = _ENV["计算价格时间"]
          L2_2 = data
          L2_2 = L2_2[1]
          L1_2 = L1_2(L2_2)
          if L1_2 then
            L1_2 = true
            L2_2 = data
            L2_2 = L2_2[2]
            return L1_2, L2_2
          else
            L1_2 = false
            return L1_2
          end
      end
    end
    else
      L1_2 = false
      return L1_2
    end
  else
    L1_2 = false
    return L1_2
  end
end

_ENV["联动_比价"] = L0_1
