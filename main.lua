local L0_1, L1_1, L2_1, L3_1
L0_1 = require
L1_1 = "TSLib"
L0_1(L1_1)
L0_1 = require
L1_1 = "红尘试炼"
L0_1(L1_1)
L0_1 = require
L1_1 = "核心调用库"
L0_1(L1_1)
L0_1 = require
L1_1 = "无名打码"
L0_1(L1_1)
L0_1 = require
L1_1 = "通用传送库"
L0_1(L1_1)
L0_1 = require
L1_1 = "颜色库"
L0_1(L1_1)
L0_1 = require
L1_1 = "res"
L0_1(L1_1)
L0_1 = require
L1_1 = "share"
L0_1(L1_1)
L0_1 = require
L1_1 = "Colorful"
L0_1(L1_1)
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
L0_1 = require
L1_1 = "押镖调用库"
L0_1(L1_1)
L0_1 = require
L1_1 = "PublicFunc"
L0_1(L1_1)
L0_1 = require
L1_1 = "FlightFlag"
L0_1(L1_1)
L0_1 = require
L1_1 = "MyGameData"
L0_1(L1_1)
L0_1 = require
L1_1 = "PetTreatment"
L0_1(L1_1)
L0_1 = require
L1_1 = "登录模式"
L0_1(L1_1)
L0_1 = require
L1_1 = "初出茅庐"
L0_1(L1_1)
L0_1 = init
L1_1 = 1
L0_1(L1_1)
L0_1 = require
L1_1 = "无名打码"
L0_1(L1_1)
L0_1 = require
L1_1 = "GameCJData"
L0_1(L1_1)
L0_1 = require
L1_1 = "GameTaskData"
L0_1(L1_1)
L0_1 = require
L1_1 = "Calligraphy"
L0_1(L1_1)
L0_1 = readFileString
L1_1 = userPath
L1_1 = L1_1()
L2_1 = "/log/wmo.log"
L1_1 = L1_1 .. L2_1
L0_1 = L0_1(L1_1)
text = L0_1

function L0_1(...)
  local L0_2, L1_2, L2_2
  L0_2 = mSleep
  L1_2 = 1
  L0_2(L1_2)
end

MyGetRunningAccess = L0_1
L0_1 = getScreenSize
L0_1, L1_1 = L0_1()
hhhh = L1_1
wwww = L0_1
L0_1 = math
L0_1 = L0_1.randomseed
L1_1 = getRndNum
L1_1, L2_1, L3_1 = L1_1()
L0_1(L1_1, L2_1, L3_1)
startTask = false
_ENV["卡顿掉帧"] = 1
_ENV["没点角色"] = ""

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L0_2 = getScreenSize
  L0_2, L1_2 = L0_2()
  L2_2 = UINew
  L3_2 = 5
  L4_2 = "【基础设置】,【坐标价格】,【战斗设置】,【特殊设置】,【必看！！】"
  L5_2 = "运行脚本"
  L6_2 = "退出脚本"
  L7_2 = "uiconfigfuben.dat"
  L8_2 = 0
  L9_2 = 180
  L10_2 = 1920
  L11_2 = 1080
  L12_2 = "255,255,250"
  L13_2 = "142,229,238"
  L14_2 = ""
  L15_2 = "tab"
  L16_2 = 1
  L17_2 = 31
  L18_2 = "left"
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
  L2_2 = UILabel
  L3_2 = "功能选择:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "Gameorder1"
  L4_2 = "跑商,青龙,定时买药,摆摊卖二药,转移二药"
  L5_2 = "3"
  L6_2 = 280
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "循环上号:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,0"
  L7_2 = 220
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "循环上号"
  L4_2 = "单号模式,普通循环"
  L5_2 = "0"
  L6_2 = 300
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "循环数量:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,0"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "角色数量"
  L4_2 = ""
  L5_2 = ""
  L6_2 = 12
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 150
  L11_2 = 0
  L12_2 = false
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = "跑商票数:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = 1
  L4_2 = "UI_跑商票数"
  L5_2 = ""
  L6_2 = "999"
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 200
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = "青龙次数:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = 1
  L4_2 = "UI_青龙次数"
  L5_2 = ""
  L6_2 = "999"
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 200
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = "卡顿掉帧:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = 1
  L4_2 = "UI_卡顿掉帧"
  L5_2 = "1000=1秒"
  L6_2 = ""
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 260
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = "跑商路线:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_跑商路线"
  L4_2 = "鬼区智能,地府北俱,长安长寿,比价换线"
  L5_2 = "0"
  L6_2 = 280
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "赏金选择:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_赏金选择"
  L4_2 = "无赏金下号,跑满20赏金下号,优先赏金,不跑赏金"
  L5_2 = "3"
  L6_2 = 310
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "跑商模式:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_跑商模式"
  L4_2 = "普通跑商,联动跑商,抢货模式"
  L5_2 = "3"
  L6_2 = 310
  L7_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = 1
  L4_2 = "跑商等级:"
  L5_2 = 15
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 200
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 1
  L4_2 = "UI_跑商等级"
  L5_2 = "80,40,60"
  L6_2 = ""
  L7_2 = 280
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 1
  L4_2 = "购买二药:"
  L5_2 = 15
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 200
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = "UI_二药频率"
  L4_2 = "每小时买二药,每票买二药,不买二药"
  L5_2 = "3"
  L6_2 = 310
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UICheck
  L3_2 = 1
  L4_2 = "UI_完成不下线"
  L5_2 = "完成任务不下线"
  L6_2 = ""
  L7_2 = 510
  L8_2 = 1
  L9_2 = "-"
  L10_2 = 1
  L11_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UICheck
  L3_2 = 1
  L4_2 = "UI_签到"
  L5_2 = "签到"
  L6_2 = ""
  L7_2 = 350
  L8_2 = 0
  L9_2 = "-"
  L10_2 = 1
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = "卖体间隔:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = 1
  L4_2 = "卖体间隔time"
  L5_2 = "分钟"
  L6_2 = "180"
  L7_2 = 14
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 240
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = "活力处置:"
  L4_2 = 15
  L5_2 = "left"
  L6_2 = "0,0,5"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_活力处置"
  L4_2 = "换修业,烹饪,飞行符,炼药,不操作"
  L5_2 = "0"
  L6_2 = 310
  L7_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILine
  L3_2 = "center"
  L2_2(L3_2)
  L2_2 = UILabel
  L3_2 = "旗帜设置"
  L4_2 = 16
  L5_2 = "center"
  L6_2 = "0,168,233"
  L7_2 = -1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "长安城"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,5"
  L7_2 = 150
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_qizi_长安"
  L4_2 = "红旗,白旗,黄旗,绿旗,蓝旗"
  L5_2 = "0"
  L6_2 = 190
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "傲来国"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,2"
  L7_2 = 150
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_qizi_傲来"
  L4_2 = "关闭,黄旗,红旗,白旗,绿旗,蓝旗"
  L5_2 = "0"
  L6_2 = 190
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "朱紫国"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,2"
  L7_2 = 150
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_qizi_朱紫"
  L4_2 = "关闭,白旗,红旗,黄旗,绿旗,蓝旗"
  L5_2 = "0"
  L6_2 = 190
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "长寿村"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,2"
  L7_2 = 150
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_qizi_长寿"
  L4_2 = "关闭,绿旗,红旗,白旗,黄旗,蓝旗"
  L5_2 = "0"
  L6_2 = 190
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "帮派"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,2"
  L7_2 = 150
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = "UI_qizi_帮派"
  L4_2 = "关闭,绿旗,红旗,白旗,黄旗,蓝旗"
  L5_2 = "0"
  L6_2 = 190
  L2_2(L3_2, L4_2, L5_2, L6_2)
  L2_2 = UILine
  L3_2 = "center"
  L2_2(L3_2)
  L2_2 = UILabel
  L3_2 = "物品补充"
  L4_2 = 16
  L5_2 = "center"
  L6_2 = "0,168,233"
  L7_2 = -1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "旗子价格"
  L4_2 = 14
  L5_2 = "left"
  L6_2 = "0,0,0"
  L7_2 = 180
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_goumai_qizi_price"
  L4_2 = ""
  L5_2 = "80000"
  L6_2 = 14
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 223
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UICheck
  L3_2 = "UI_goumai_qizi_1"
  L4_2 = "购买位置"
  L5_2 = ""
  L6_2 = 360
  L7_2 = 1
  L8_2 = "-"
  L9_2 = 1
  L10_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L2_2 = UICombo
  L3_2 = "UI_goumai_qizi_1_la"
  L4_2 = "长安城,傲来国,朱紫国,长寿村"
  L5_2 = "0"
  L6_2 = 270
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "摆摊坐标"
  L4_2 = 14
  L5_2 = "right"
  L6_2 = "0,0,2"
  L7_2 = 180
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = "X"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_goumai_qizi_1_X"
  L4_2 = ""
  L5_2 = "457"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = "Y"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_goumai_qizi_1_Y"
  L4_2 = ""
  L5_2 = "166"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L11_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = "上面四色常规下面蓝色"
  L4_2 = 14
  L5_2 = "left"
  L6_2 = "0,0,0"
  L7_2 = 430
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = "UI_goumai_qizi_2"
  L4_2 = "购买位置"
  L5_2 = ""
  L6_2 = 360
  L7_2 = 1
  L8_2 = "-"
  L9_2 = 1
  L10_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L2_2 = UICombo
  L3_2 = "UI_goumai_qizi_2_la"
  L4_2 = "长安城,傲来国,朱紫国,长寿村"
  L5_2 = "0"
  L6_2 = 270
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "摆摊坐标"
  L4_2 = 14
  L5_2 = "right"
  L6_2 = "0,0,2"
  L7_2 = 180
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = "X"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_goumai_qizi_2_X"
  L4_2 = ""
  L5_2 = "457"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = "Y"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_goumai_qizi_2_Y"
  L4_2 = ""
  L5_2 = "166"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L2_2 = UILabel
  L3_2 = "物品出售"
  L4_2 = 16
  L5_2 = "center"
  L6_2 = "0,168,233"
  L7_2 = -1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "二药价格"
  L4_2 = 14
  L5_2 = "left"
  L6_2 = "0,0,0"
  L7_2 = 180
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_卖二药价格"
  L4_2 = ""
  L5_2 = "1222"
  L6_2 = 14
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 223
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UICombo
  L3_2 = "UI_卖二药地址"
  L4_2 = "建邺城,长寿村,傲来国,长安城"
  L5_2 = "0"
  L6_2 = 270
  L7_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = UILabel
  L3_2 = "摆摊坐标"
  L4_2 = 14
  L5_2 = "right"
  L6_2 = "0,0,2"
  L7_2 = 180
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = "X"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_卖药坐标X"
  L4_2 = ""
  L5_2 = "85"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L11_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = "Y"
  L4_2 = 14
  L5_2 = "center"
  L6_2 = "0,0,0"
  L7_2 = 25
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = "UI_卖药坐标Y"
  L4_2 = ""
  L5_2 = "28"
  L6_2 = 15
  L7_2 = "left"
  L8_2 = "0,0,0"
  L9_2 = "default"
  L10_2 = 190
  L11_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  L2_2 = UILabel
  L3_2 = 1
  L4_2 = "清空任务:"
  L5_2 = 15
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 200
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 1
  L4_2 = "清空任务栏啊"
  L5_2 = "是,否"
  L6_2 = "1"
  L7_2 = 250
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 1
  L4_2 = "转移FF:"
  L5_2 = 15
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 200
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 1
  L4_2 = "UI_转移FF"
  L5_2 = "是,否"
  L6_2 = "1"
  L7_2 = 250
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "长安:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = 100
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "刀"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_刀"
  L5_2 = "6200"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "扇子"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_扇子"
  L5_2 = "5500"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "佛珠"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_佛珠"
  L5_2 = "9200"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "车夫坐标X"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 210
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_车夫x"
  L5_2 = "x"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "Y"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 30
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_车夫y"
  L5_2 = "y"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "长寿:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = 100
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "面粉"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_面粉"
  L5_2 = "3950"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "鹿茸"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_鹿茸"
  L5_2 = "8900"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "符咒"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_符咒"
  L5_2 = "6500"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "出口坐标X"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 210
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_出口x"
  L5_2 = "x"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "Y"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 30
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_出口y"
  L5_2 = "y"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "地府:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = 100
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "纸钱"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_纸钱"
  L5_2 = "4120"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "首饰"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_首饰"
  L5_2 = "6540"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "宝珠"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_宝珠"
  L5_2 = "10000"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "青龙入口X"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 210
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_青龙x"
  L5_2 = "x"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "Y"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 30
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_青龙y"
  L5_2 = "y"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "北俱:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = 100
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "灯油"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_灯油"
  L5_2 = "5420"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "人参"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_人参"
  L5_2 = "9500"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "铃铛"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_铃铛"
  L5_2 = "6410"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "青龙中转X"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 210
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_青龙中转x"
  L5_2 = "x"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "Y"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 30
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_青龙中转y"
  L5_2 = "y"
  L6_2 = ""
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "傲来:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = 100
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "酒"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_酒"
  L5_2 = "5920"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "帽子"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_帽子"
  L5_2 = "4800"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "盐"
  L5_2 = 13
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 90
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 2
  L4_2 = "UI_盐"
  L5_2 = "8000"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 195
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "长期总有烤火风险，不建议大号跑"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "不写则按默认,可以不写,但别写错.不想买某个物品价格写1"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "如商品价格刷的超过上面限额，就不会购买了。可以适当提高默认限价"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 2
  L4_2 = "价格过低可能导致频繁换线或等刷不买物品！！"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "222,0,0"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "角色战斗"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,18"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "rw_zdfs"
  L5_2 = "普通攻击,便捷法术1"
  L6_2 = "0"
  L7_2 = 270
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "宝宝战斗"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,5"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "bb_zdfs"
  L5_2 = "攻宝宝,法宝宝"
  L6_2 = "0"
  L7_2 = 270
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_zhandou_BB_selet"
  L5_2 = "地狱烈火,泰山压顶,奔雷咒,水漫金山"
  L6_2 = "0"
  L7_2 = 270
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "人物加血:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_角色回复"
  L5_2 = "50%,70%,90%"
  L6_2 = "2"
  L7_2 = 200
  L8_2 = 1
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "人物加蓝:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_角色回蓝"
  L5_2 = "50%,70%,90%"
  L6_2 = "2"
  L7_2 = 250
  L8_2 = 1
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "宠物加血:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_宠物回复"
  L5_2 = "50%,70%,90%"
  L6_2 = "2"
  L7_2 = 250
  L8_2 = 1
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "宠物加蓝:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_宠物回蓝"
  L5_2 = "50%,70%,90%"
  L6_2 = "2"
  L7_2 = 250
  L8_2 = 0
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "补给方式"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,2"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_回复方式"
  L5_2 = "道具,住店/巫医"
  L6_2 = "0"
  L7_2 = 200
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "补血道具:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "补血道具"
  L5_2 = "包子1,红罗羹"
  L6_2 = "0"
  L7_2 = 250
  L8_2 = 1
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "补蓝道具:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 190
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "补蓝道具"
  L5_2 = "佛手1,绿罗羹"
  L6_2 = "0"
  L7_2 = 250
  L8_2 = 0
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "是否转移二药:"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 300
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_转移二药"
  L5_2 = "不转移,下线前转移二药,每X票转移"
  L6_2 = "0"
  L7_2 = 330
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "UI_转移二药间隔"
  L5_2 = "每几票转移？"
  L6_2 = ""
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 350
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "添加丢钱好友ID"
  L5_2 = "填写转移id"
  L6_2 = ""
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 400
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UICheck
  L3_2 = 3
  L4_2 = "UI_添加好友"
  L5_2 = "添加好友"
  L6_2 = ""
  L7_2 = 450
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "上下不相关！暂时测试"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 220
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 3
  L4_2 = "UI_goumai_宝图_la"
  L5_2 = "长安城,建邺城"
  L6_2 = "0"
  L7_2 = 270
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "购买宝图"
  L5_2 = 15
  L6_2 = "right"
  L7_2 = "0,0,0"
  L8_2 = 180
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "X"
  L5_2 = 15
  L6_2 = "center"
  L7_2 = "0,0,0"
  L8_2 = 25
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "UI_goumai_宝图_X"
  L5_2 = ""
  L6_2 = "455"
  L7_2 = 14
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 190
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "Y"
  L5_2 = 15
  L6_2 = "center"
  L7_2 = "0,0,0"
  L8_2 = 25
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "UI_goumai_宝图_Y"
  L5_2 = ""
  L6_2 = "155"
  L7_2 = 14
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 190
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 3
  L4_2 = "宝图价格"
  L5_2 = 15
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 180
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "UI_goumai_宝图_price"
  L5_2 = ""
  L6_2 = "25999"
  L7_2 = 14
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 223
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UIEdit
  L3_2 = 3
  L4_2 = "预留金额"
  L5_2 = "准备剩多少钱"
  L6_2 = ""
  L7_2 = 12
  L8_2 = "left"
  L9_2 = "5,0,0"
  L10_2 = "default"
  L11_2 = 320
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_1级帮"
  L5_2 = "点不到总管勾选"
  L6_2 = ""
  L7_2 = 520
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_卡小地图"
  L5_2 = "卡小地图"
  L6_2 = ""
  L7_2 = 450
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "跑商:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 150
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_跑商_屏蔽摆摊"
  L5_2 = "摆摊地图屏蔽摆摊"
  L6_2 = ""
  L7_2 = 560
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_合适都买"
  L5_2 = "便宜卖空去隔壁买"
  L6_2 = ""
  L7_2 = 570
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_抢货价钱"
  L5_2 = "抢货不看价钱"
  L6_2 = ""
  L7_2 = 570
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "青龙:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 150
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_走入青龙"
  L5_2 = "步行到仓库"
  L6_2 = "0"
  L7_2 = 500
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_走入青龙中转"
  L5_2 = "步行先中转"
  L6_2 = ""
  L7_2 = 500
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "定时买药:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 220
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 4
  L4_2 = "UI_定时买药"
  L5_2 = "维护时间,写分钟"
  L6_2 = ""
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 450
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "车夫和出口寻找方式:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 550
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 4
  L4_2 = "UI_车夫寻找方式"
  L5_2 = "填入坐标,自动识别"
  L6_2 = "0"
  L7_2 = 290
  L8_2 = 1
  L9_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "自动识别部分帮派不适用"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 550
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "XX:X0整分刷价后第几秒点商人:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 690
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 4
  L4_2 = "UI_跑商刷价等待"
  L5_2 = "默认第12秒,写数字"
  L6_2 = ""
  L7_2 = 15
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 470
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "抢货模式用的！！！"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 550
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_回到地府"
  L5_2 = "抢货回地府"
  L6_2 = ""
  L7_2 = 500
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 4
  L4_2 = "UI_仙玉补旗"
  L5_2 = "仙玉补旗"
  L6_2 = ""
  L7_2 = 500
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "提醒方式:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 230
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 4
  L4_2 = "UI_msg"
  L5_2 = "响铃,震动提示,无声"
  L6_2 = ""
  L7_2 = 250
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "家具放弃方式:"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = 150
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICombo
  L3_2 = 4
  L4_2 = "UI_家具放弃方式"
  L5_2 = "下线放弃,等待3分钟放弃"
  L6_2 = "0"
  L7_2 = 290
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "帮派总管X"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 210
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 4
  L4_2 = "UI_帮派总管x"
  L5_2 = "x"
  L6_2 = "21"
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 4
  L4_2 = "Y"
  L5_2 = 14
  L6_2 = "left"
  L7_2 = "0,0,0"
  L8_2 = 30
  L9_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UIEdit
  L3_2 = 4
  L4_2 = "UI_帮派总管y"
  L5_2 = "y"
  L6_2 = "23"
  L7_2 = 13
  L8_2 = "left"
  L9_2 = "0,0,0"
  L10_2 = "default"
  L11_2 = 170
  L12_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "本机分辨率高为："
  L5_2 = "hhhh"
  L5_2 = _ENV[L5_2]
  L6_2 = "宽为："
  L7_2 = "wwww"
  L7_2 = _ENV[L7_2]
  L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "抢货模式：适用于比较新的区，价格合适直接购买不去比价。刷价时间有波动你可以看你区情况和你网络情况去调整，抢货只支持长安长寿和地府北俱。别的路线利润太低。"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "联动模式：不适合比较新或货物不充足例如刷价后1分钟左右物品就卖完了，适合能买到第二次物品的区例如刷价后2-5分钟还有货的区，一个号也可以开联动"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "普通模式：不看时辰，不等刷价，只比胖瘦商人价格，低于你设定就会买"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "青龙需准备点位【帮派】【吴举人】【王夫人】【陈员外】青龙旗子,如果没有标准点位则选关闭！！！如果想步行进入帮派仓库需写仓库入口坐标，可以调试一下进入为止。部分太绕的地图不可使用"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "必须有长安旗子！！！长安城旗子必须有点位【驿站】【江南野外口】【酒店】【大唐国境口】！！！！"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "如果点补到车夫！！！注意修改关掉自动识别，选填入坐标，坐标是和车夫完全重叠的坐标"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "活力处置会按体力时间一并操作，获得的东西会存仓库，把仓库设置成普通仓库"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "如果帮派屋子格局特殊，太小或太大导致点总管不顺畅勾选特殊设置里的点不到总管"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UILabel
  L3_2 = 5
  L4_2 = "清理进程会关掉后台运行的软件！！可能包括本机ip工具！！"
  L5_2 = 16
  L6_2 = "left"
  L7_2 = "0,168,233"
  L8_2 = -1
  L9_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L2_2 = UICheck
  L3_2 = 5
  L4_2 = "UI_清理后台进程"
  L5_2 = "清理后台进程"
  L6_2 = ""
  L7_2 = 550
  L8_2 = 0
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = UICheck
  L3_2 = 5
  L4_2 = "UI_上传截图"
  L5_2 = "上传截图"
  L6_2 = ""
  L7_2 = 355
  L8_2 = 1
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L2_2 = "UIShow"
  L2_2 = _ENV[L2_2]
  L2_2()
end

SmUI = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  L0_2 = delFile
  L1_2 = userPath
  L1_2 = L1_2()
  L2_2 = "/log/hblog.log"
  L1_2 = L1_2 .. L2_2
  L0_2(L1_2)
  L0_2 = _cmp_tb_cx
  L1_2 = Color
  L1_2 = L1_2["主界面"]
  L2_2 = {}
  L3_2 = 10
  L4_2 = 100
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 == false then
    L0_2 = _print
    L1_2 = "未进入游戏,开始执行进入游戏操作！"
    L0_2(L1_2)
    L0_2 = _ENV["_游戏"]
    L0_2 = L0_2["进入"]
    L0_2()
  else
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["屏蔽"]
    L1_2 = "close"
    L0_2(L1_2)
    L0_2 = _print
    L1_2 = "正在游戏中"
    L0_2(L1_2)
  end
  L0_2 = toast
  L1_2 = "正在游戏中"
  L2_2 = 1
  L0_2(L1_2, L2_2)
  L0_2 = UI_DATU
  if L0_2 == "自动打图" then
    L0_2 = _ENV["_打图"]
    L0_2 = L0_2["流程"]
    L0_2 = L0_2()
    if L0_2 then
      L0_2 = _ENV["UI_自动卖图"]
      if L0_2 == "自动卖图" then
        L0_2 = _ENV["_卖图"]
        L0_2 = L0_2["自动卖图"]
        L1_2 = _ENV["UI_图源"]
        L0_2(L1_2)
      end
    end
  else
    L0_2 = _ENV["UI_自动卖图"]
    if L0_2 == "自动卖图" then
      L0_2 = _ENV["_卖图"]
      L0_2 = L0_2["自动卖图"]
      L1_2 = _ENV["UI_图源"]
      L0_2(L1_2)
      return
    else
      L0_2 = _ENV["UI_只丢图"]
      if L0_2 == "自动丢图" then
        L0_2 = _ENV["_卖图"]
        L0_2 = L0_2["自动丢图"]
        L1_2 = _ENV["UI_图源"]
        L0_2(L1_2)
        return
      end
    end
  end
  L0_2 = _ENV["UI_完成后_操作"]
  if L0_2 == "完成后转移宝图" then
    L0_2 = _ENV["_卖图"]
    L0_2 = L0_2["自动丢图"]
    L1_2 = _ENV["UI_图源"]
    L0_2(L1_2)
    return
  else
    return
  end
end

_ENV["打图流程"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  _ENV["金钱不足"] = false
  _ENV["门派名字"] = ""
  _ENV["放弃豆斋"] = false
  _ENV["正在师门"] = false
  L0_2 = _ENV["UI_跑商路线"]
  if L0_2 == "地府北俱" or L0_2 == "比价换线" or L0_2 == "长安长寿" or _ENV["UI_跑商模式"] == "抢货模式" then
    _ENV["北俱地府"] = true
  else
    _ENV["北俱地府"] = false
  end
  L0_2 = _ENV["UI_赏金选择"]
  if L0_2 == "无赏金下号" or L0_2 == "跑满20赏金下号" or L0_2 == "优先赏金" then
    _ENV["UI_赏金任务"] = "赏金任务"
  end
  L0_2 = _ENV["UI_刀"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_刀"]
    L0_2 = L0_2(L1_2)
    _ENV["刀价格"] = L0_2
  else
    _ENV["刀价格"] = 6200
  end
  L0_2 = _ENV["UI_扇子"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_扇子"]
    L0_2 = L0_2(L1_2)
    _ENV["扇子价格"] = L0_2
  else
    _ENV["扇子价格"] = 5500
  end
  L0_2 = _ENV["UI_佛珠"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_佛珠"]
    L0_2 = L0_2(L1_2)
    _ENV["佛珠价格"] = L0_2
  else
    _ENV["佛珠价格"] = 9200
  end
  L0_2 = _ENV["UI_面粉"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_面粉"]
    L0_2 = L0_2(L1_2)
    _ENV["面粉价格"] = L0_2
  else
    _ENV["面粉价格"] = 3950
  end
  L0_2 = _ENV["UI_鹿茸"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_鹿茸"]
    L0_2 = L0_2(L1_2)
    _ENV["鹿茸价格"] = L0_2
  else
    _ENV["鹿茸价格"] = 8900
  end
  L0_2 = _ENV["UI_符咒"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_符咒"]
    L0_2 = L0_2(L1_2)
    _ENV["符咒价格"] = L0_2
  else
    _ENV["符咒价格"] = 6500
  end
  L0_2 = _ENV["UI_纸钱"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_纸钱"]
    L0_2 = L0_2(L1_2)
    _ENV["纸钱价格"] = L0_2
  else
    _ENV["纸钱价格"] = 4120
  end
  L0_2 = _ENV["UI_首饰"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_首饰"]
    L0_2 = L0_2(L1_2)
    _ENV["首饰价格"] = L0_2
  else
    _ENV["首饰价格"] = 6540
  end
  L0_2 = _ENV["UI_宝珠"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_宝珠"]
    L0_2 = L0_2(L1_2)
    _ENV["宝珠价格"] = L0_2
  else
    _ENV["宝珠价格"] = 10000
  end
  L0_2 = _ENV["UI_灯油"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_灯油"]
    L0_2 = L0_2(L1_2)
    _ENV["灯油价格"] = L0_2
  else
    _ENV["灯油价格"] = 5420
  end
  L0_2 = _ENV["UI_人参"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_人参"]
    L0_2 = L0_2(L1_2)
    _ENV["人参价格"] = L0_2
  else
    _ENV["人参价格"] = 9500
  end
  L0_2 = _ENV["UI_铃铛"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_铃铛"]
    L0_2 = L0_2(L1_2)
    _ENV["铃铛价格"] = L0_2
  else
    _ENV["铃铛价格"] = 6410
  end
  L0_2 = _ENV["UI_酒"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_酒"]
    L0_2 = L0_2(L1_2)
    _ENV["酒价格"] = L0_2
  else
    _ENV["酒价格"] = 5920
  end
  L0_2 = _ENV["UI_帽子"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_帽子"]
    L0_2 = L0_2(L1_2)
    _ENV["帽子价格"] = L0_2
  else
    _ENV["帽子价格"] = 4800
  end
  L0_2 = _ENV["UI_盐"]
  if L0_2 ~= "" then
    L0_2 = tonumber
    L1_2 = _ENV["UI_盐"]
    L0_2 = L0_2(L1_2)
    _ENV["盐价格"] = L0_2
  else
    _ENV["盐价格"] = 8000
  end
  L0_2 = {}
  L1_2 = _ENV["刀价格"]
  L0_2.dao = L1_2
  L1_2 = _ENV["佛珠价格"]
  L0_2.fozhu = L1_2
  L1_2 = _ENV["扇子价格"]
  L0_2.shanzi = L1_2
  L1_2 = _ENV["宝珠价格"]
  L0_2.baozhu = L1_2
  L1_2 = _ENV["纸钱价格"]
  L0_2.money = L1_2
  L1_2 = _ENV["首饰价格"]
  L0_2.shoushi = L1_2
  L1_2 = _ENV["盐价格"]
  L0_2.salt = L1_2
  L1_2 = _ENV["酒价格"]
  L0_2.wine = L1_2
  L1_2 = _ENV["帽子价格"]
  L0_2.hat = L1_2
  L1_2 = _ENV["面粉价格"]
  L0_2.mianfen = L1_2
  L1_2 = _ENV["鹿茸价格"]
  L0_2.antler = L1_2
  L1_2 = _ENV["符咒价格"]
  L0_2.amulet = L1_2
  L1_2 = _ENV["人参价格"]
  L0_2.renshen = L1_2
  L1_2 = _ENV["灯油价格"]
  L0_2.dengyou = L1_2
  L1_2 = _ENV["铃铛价格"]
  L0_2.lingdang = L1_2
  proposedPrice = L0_2
  L0_2 = init
  L1_2 = 1
  L0_2(L1_2)
  L0_2 = {}
  L1_2 = {}
  L2_2 = "40-69师门"
  L3_2 = _ENV["获取门派名称"]
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L2_2 = {}
  L3_2 = "卖体转钱"
  L4_2 = _ENV["卖体转钱任务"]
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L3_2 = {}
  L4_2 = "押镖测试"
  L5_2 = _ENV["押镖任务"]
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L4_2 = {}
  L5_2 = "打图"
  L6_2 = _ENV["打图流程"]
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L5_2 = {}
  L6_2 = "探访奇闻"
  L7_2 = _ENV["探访奇闻任务"]
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L6_2 = {}
  L7_2 = "红尘"
  L8_2 = _ENV["红尘试炼任务"]
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L7_2 = {}
  L8_2 = "起号"
  L9_2 = _ENV["起号任务"]
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L8_2 = {}
  L9_2 = "70-155师门"
  L10_2 = _ENV["百级师门任务"]
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L9_2 = {}
  L10_2 = "摆摊卖二药"
  L11_2 = _ENV["摆摊卖二药"]
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L10_2 = {}
  L11_2 = "跟队模式"
  L12_2 = _ENV["跟队模式"]
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L11_2 = {}
  L12_2 = "定时买药"
  L13_2 = _ENV["定时买药任务"]
  L11_2[1] = L12_2
  L11_2[2] = L13_2
  L12_2 = {}
  L13_2 = "青龙"
  L14_2 = _ENV["青龙任务"]
  L12_2[1] = L13_2
  L12_2[2] = L14_2
  L13_2 = {}
  L14_2 = "跑商"
  L15_2 = _ENV["跑商任务"]
  L13_2[1] = L14_2
  L13_2[2] = L15_2
  L14_2 = {}
  L15_2 = "初出茅庐"
  L16_2 = _ENV["初出茅庐任务"]
  L14_2[1] = L15_2
  L14_2[2] = L16_2
  L15_2 = {}
  L16_2 = "建邺探案"
  L17_2 = _ENV["建邺探案任务"]
  L15_2[1] = L16_2
  L15_2[2] = L17_2
  L16_2 = {}
  L17_2 = "测试邮箱识别"
  L18_2 = _ENV["获取充值账号"]
  L16_2[1] = L17_2
  L16_2[2] = L18_2
  L17_2 = {}
  L18_2 = "测试宝图"
  L19_2 = _ENV["买图转移"]
  L17_2[1] = L18_2
  L17_2[2] = L19_2
  L18_2 = {}
  L19_2 = "转移二药"
  L20_2 = _ENV["转移二药总流程"]
  L18_2[1] = L19_2
  L18_2[2] = L20_2
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L0_2[5] = L5_2
  L0_2[6] = L6_2
  L0_2[7] = L7_2
  L0_2[8] = L8_2
  L0_2[9] = L9_2
  L0_2[10] = L10_2
  L0_2[11] = L11_2
  L0_2[12] = L12_2
  L0_2[13] = L13_2
  L0_2[14] = L14_2
  L0_2[15] = L15_2
  L0_2[16] = L16_2
  L0_2[17] = L17_2
  L0_2[18] = L18_2
  loginArray = L0_2
  L0_2 = {}
  L1_2 = Gameorder1
  L2_2 = Gameorder2
  L3_2 = Gameorder3
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  sxljAR = L0_2
  L0_2 = _ENV["UI_清理后台进程"]
  if L0_2 then
    L0_2 = closeAllApp
    L1_2 = "com.touchsprite.android,com.netease.mhxyhtb"
    L0_2(L1_2)
  end
  L0_2 = 1
  L1_2 = sxljAR
  L1_2 = #L1_2
  L2_2 = 1
  for L3_2 = L0_2, L1_2, L2_2 do
    L4_2 = 1
    L5_2 = loginArray
    L5_2 = #L5_2
    L6_2 = 1
    for L7_2 = L4_2, L5_2, L6_2 do
      L8_2 = sxljAR
      L8_2 = L8_2[L3_2]
      L9_2 = loginArray
      L9_2 = L9_2[L7_2]
      L9_2 = L9_2[1]
      if L8_2 == L9_2 then
        L8_2 = setVolumeLevel
        L9_2 = 0
        L8_2(L9_2)
        L8_2 = loginArray
        L8_2 = L8_2[L7_2]
        L8_2 = L8_2[1]
        if L8_2 == "起号" or L8_2 == "红尘" then
          L8_2 = mSleep
          L9_2 = 10
          L8_2(L9_2)
          L8_2 = _ENV["_打图"]
          L8_2 = L8_2["初始"]
          L8_2()
        else
          L8_2 = loginArray
          L8_2 = L8_2[L7_2]
          L8_2 = L8_2[1]
          if L8_2 == "定时买药" then
            L8_2 = loginArray
            L8_2 = L8_2[L7_2]
            L8_2 = L8_2[2]
            L8_2()
          else
            L8_2 = _ENV["_打图"]
            L8_2 = L8_2["初始"]
            L8_2()
          end
        end
        L8_2 = toast
        L9_2 = "开始执行:"
        L10_2 = loginArray
        L10_2 = L10_2[L7_2]
        L10_2 = L10_2[1]
        L9_2 = L9_2 .. L10_2
        L8_2(L9_2)
        L8_2 = _ENV["通用功能"]
        L8_2 = L8_2["关闭"]
        L8_2()
        L8_2 = _ENV["通用功能"]
        L8_2 = L8_2["任务栏打开"]
        L8_2()
        L8_2 = _ENV["清空任务栏啊"]
        if L8_2 == "是" then
          L8_2 = myBlockcolor
          L9_2 = sevenColor
          L9_2 = L9_2["跑商任务"]
          L8_2 = L8_2(L9_2)
          if L8_2 == false then
            L8_2 = _ENV["通用功能"]
            L8_2 = L8_2["任务栏清理"]
            L8_2()
          end
        end
        L8_2 = _ENV["UI_测试上号充值"]
        if L8_2 then
          L8_2 = _ENV["自动充值流程"]
          L8_2()
        end
        L8_2 = loginArray
        L8_2 = L8_2[L7_2]
        L8_2 = L8_2[1]
        if L8_2 == "跑商" then
          L8_2 = myBlockcolor
          L9_2 = sevenColor
          L9_2 = L9_2["跑商任务"]
          L8_2 = L8_2(L9_2)
          if L8_2 == false then
            L8_2 = _ENV["通用功能"]
            L8_2 = L8_2["任务栏清理"]
            L8_2()
          end
        else
          L8_2 = loginArray
          L8_2 = L8_2[L7_2]
          L8_2 = L8_2[1]
          if L8_2 == "青龙" then
            L8_2 = _find
            L9_2 = Color
            L9_2 = L9_2["青龙"]
            L9_2 = L9_2["青龙任务绿"]
            L8_2 = L8_2(L9_2)
            if L8_2 == false then
              L8_2 = _ENV["通用功能"]
              L8_2 = L8_2["任务栏清理"]
              L8_2()
            end
          end
        end
        L8_2 = loginArray
        L8_2 = L8_2[L7_2]
        L8_2 = L8_2[2]
        L8_2()
      end
    end
  end
  L0_2 = _ENV["UI_卖体"]
  if L0_2 == "干完卖体" then
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["卖体"]
    L0_2()
  end
  L0_2 = _ENV["是否丢钱"]
  if L0_2 == "丢钱" then
    L0_2 = _ENV["检测钱数"]
    L0_2()
  end
  L0_2 = _ENV["是否存钱"]
  if L0_2 == "存钱" then
    L0_2 = _ENV["检测钱数存钱"]
    L0_2()
  end
  L0_2 = _ENV["UI_转移二药"]
  if L0_2 == "下线前转移二药" then
    L0_2 = _ENV["转移二药总流程"]
    L0_2()
  end
  L0_2 = _ENV["是否丢物"]
  if L0_2 == "丢物" then
    L0_2 = _ENV["UI_转物前开玲珑石"]
    if L0_2 == "转物前开玲珑石" then
      L0_2 = _ENV["_功能"]
      L0_2 = L0_2["背包"]
      L1_2 = "open"
      L0_2(L1_2)
      while true do
        L0_2 = myBlockcolor
        L1_2 = sevenColor
        L1_2 = L1_2["大礼包"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _tap
          L1_2 = x
          L2_2 = y
          L3_2 = 2
          L4_2 = 10
          L0_2(L1_2, L2_2, L3_2, L4_2)
          L0_2 = _ENV["_随机延时"]
          L1_2 = 200
          L0_2(L1_2)
        end
        L0_2 = myBlockcolor
        L1_2 = sevenColor
        L1_2 = L1_2["小礼包"]
        L0_2 = L0_2(L1_2)
        if L0_2 then
          L0_2 = _tap
          L1_2 = x
          L2_2 = y
          L3_2 = 2
          L4_2 = 10
          L0_2(L1_2, L2_2, L3_2, L4_2)
          L0_2 = _ENV["_随机延时"]
          L1_2 = 200
          L0_2(L1_2)
        end
        L0_2 = myBlockcolor
        L1_2 = sevenColor
        L1_2 = L1_2["大礼包"]
        L0_2 = L0_2(L1_2)
        if L0_2 == false then
          L0_2 = myBlockcolor
          L1_2 = sevenColor
          L1_2 = L1_2["小礼包"]
          L0_2 = L0_2(L1_2)
          if L0_2 == false then
            L0_2 = _ENV["通用功能"]
            L0_2 = L0_2["叉叉"]
            L0_2()
            break
          end
        end
      end
    end
    L0_2 = _ENV["转移物品流程"]
    L0_2()
  end
  L0_2 = _ENV["UI_检查武器修理"]
  if L0_2 == "检查武器修理" then
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["修武器"]
    L0_2()
  end
  L0_2 = _ENV["UI_宠物加点"]
  if L0_2 == "下线宠物加点" then
    L0_2 = _ENV["宠物加点"]
    L0_2()
  end
  L0_2 = _ENV["UI_自动升级"]
  if L0_2 == "自动升级" then
    L0_2 = _ENV["升级"]
    L0_2()
  end
  L0_2 = _ENV["UI_自动加点"]
  if L0_2 == "自动加点" then
    L0_2 = _ENV["加点"]
    L0_2()
  end
  L0_2 = _ENV["UI_太清符"]
  if L0_2 == "领太清符" then
    L0_2 = _ENV["领取太清符"]
    L0_2()
  end
  L0_2 = _ENV["UI_领礼物"]
  if L0_2 == "领礼物" then
    L0_2 = _ENV["_功能"]
    L0_2 = L0_2["领礼物"]
    L0_2()
  end
  L0_2 = _ENV["UI_完成不下线"]
  if L0_2 == "完成任务不下线" then
    L0_2 = UI_msg
    if L0_2 == "响铃" then
      L0_2 = setVolumeLevel
      L1_2 = 1
      L0_2(L1_2)
      L0_2 = playAudio
      L1_2 = userPath
      L1_2 = L1_2()
      L2_2 = "/res/GameGX.mp3"
      L1_2 = L1_2 .. L2_2
      L0_2(L1_2)
    end
    L0_2 = dialog
    L1_2 = "完成任务"
    L2_2 = time
    L0_2(L1_2, L2_2)
    L0_2 = lua_exit
    L0_2()
  end
  L0_2 = _ENV["_功能"]
  L0_2 = L0_2["屏蔽"]
  L1_2 = "close"
  L0_2(L1_2)
  ju_shehunxiang = 0
  ju_shehunxiang2 = 0
  one_ocr_datu_num = 0
  datu_num = 0
  dodgeTime = -1
end

GOGAME = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L0_2 = SmUI
  L0_2()
  L0_2 = _ENV["UI_上传截图"]
  if L0_2 then
    L0_2 = mSleep
    L1_2 = 2222
    L0_2(L1_2)
    L0_2 = _ENV["_记录图片"]
    L0_2()
    L0_2 = mSleep
    L1_2 = 1111
    L0_2(L1_2)
    L0_2 = dialog
    L1_2 = "上传成功"
    L2_2 = 5
    L0_2(L1_2, L2_2)
    L0_2 = lua_exit
    L0_2()
  end
  gameStart = true
  L0_2 = _ENV["UI_爱购_充值"]
  if L0_2 then
    L0_2 = _ENV["爱购_账号"]
    if L0_2 ~= "" then
      L0_2 = _ENV["爱购_密码"]
      if L0_2 ~= "" then
        L0_2 = _ENV["爱购_交易密码"]
        if L0_2 == "" then
          L0_2 = _ENV["爱购_密码"]
          _ENV["爱购_交易密码"] = L0_2
        end
      else
        L0_2 = dialog
        L1_2 = "自动充值:没有填写账号密码"
        L2_2 = time
        L0_2(L1_2, L2_2)
        L0_2 = lua_exit
        L0_2()
      end
    else
      L0_2 = dialog
      L1_2 = "自动充值:没有填写账号密码"
      L2_2 = time
      L0_2(L1_2, L2_2)
      L0_2 = lua_exit
      L0_2()
    end
  end
  L0_2 = rw_zdfs
  if L0_2 == "便捷法术1" then
    _ENV["人物攻击"] = "法术"
    _ENV["UI_zhandou_师门"] = "百级技能"
  end
  L0_2 = rw_zdfs
  if L0_2 == "普通攻击" then
    _ENV["人物攻击"] = "普攻"
    _ENV["UI_zhandou_师门"] = "百级普攻"
  end
  L0_2 = bb_zdfs
  if L0_2 == "攻宝宝" then
    _ENV["宠物攻击"] = "普攻"
    UI_zhandou_BB_model = "宝宝攻击"
  end
  L0_2 = bb_zdfs
  if L0_2 == "法宝宝" then
    _ENV["宠物攻击"] = "法术"
    UI_zhandou_BB_model = "宝宝技能"
  end
  L0_2 = _ENV["补血道具"]
  if L0_2 == "包子1" then
    _ENV["双击血"] = "双击包子1"
  end
  L0_2 = _ENV["补蓝道具"]
  if L0_2 == "佛手1" then
    _ENV["双击蓝"] = "双击佛手1"
  end
  L0_2 = _ENV["补血道具"]
  if L0_2 == "红罗羹" then
    _ENV["双击血"] = "双击红罗羹"
  end
  L0_2 = _ENV["补蓝道具"]
  if L0_2 == "绿罗羹" then
    _ENV["双击蓝"] = "双击绿罗羹"
  end
  L0_2 = _ENV["补血道具"]
  if L0_2 == "包子1" then
    _ENV["押镖检测"] = "包子"
  end
  L0_2 = _ENV["补血道具"]
  if L0_2 == "红罗羹" then
    _ENV["押镖检测"] = "红罗羹"
  end
  L0_2 = _ENV["UI_角色回复"]
  if L0_2 == "50%" then
    _ENV["UI_角色通用回复"] = "50%"
  else
    L0_2 = _ENV["UI_角色回复"]
    if L0_2 == "70%" then
      _ENV["UI_角色通用回复"] = "70%"
    else
      L0_2 = _ENV["UI_角色回复"]
      if L0_2 == "90%" then
        _ENV["UI_角色通用回复"] = "90%"
      end
    end
  end
  L0_2 = _ENV["UI_宠物回复"]
  if L0_2 == "50%" then
    _ENV["UI_宠物通用回复"] = "50%"
  else
    L0_2 = _ENV["UI_宠物回复"]
    if L0_2 == "70%" then
      _ENV["UI_宠物通用回复"] = "70%"
    else
      L0_2 = _ENV["UI_宠物回复"]
      if L0_2 == "90%" then
        _ENV["UI_宠物通用回复"] = "90%"
      end
    end
  end
  L0_2 = _ENV["UI_回复方式"]
  if L0_2 == "道具" then
    _ENV["UI_角色状态回复"] = "吃碗"
    _ENV["UI_宠物状态回复"] = "吃碗"
  else
    L0_2 = _ENV["UI_回复方式"]
    if L0_2 == "住店/巫医" then
      _ENV["UI_角色状态回复"] = "住店"
      _ENV["UI_宠物状态回复"] = "巫医"
    end
  end
  L0_2 = _ENV["UI_卡顿掉帧"]
  if L0_2 == "" then
    _ENV["UI_卡顿掉帧"] = "1"
  end
  L0_2 = _ENV["UI_跑商票数"]
  if L0_2 == "" then
    _ENV["UI_跑商票数"] = "999"
  end
  L0_2 = _ENV["UI_转移二药间隔"]
  if L0_2 == "" then
    _ENV["UI_转移二药间隔"] = "999"
  end
  L0_2 = _ENV["UI_青龙次数"]
  if L0_2 == "" then
    _ENV["UI_青龙次数"] = "9999"
  end
  L0_2 = _ENV["UI_跑商刷价等待"]
  if L0_2 == "" then
    _ENV["UI_跑商刷价等待"] = "12"
  end
  L0_2 = tonumber
  L1_2 = _ENV["UI_跑商刷价等待"]
  L0_2 = L0_2(L1_2)
  _ENV["跑商刷价等待"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_跑商票数"]
  L0_2 = L0_2(L1_2)
  _ENV["目标跑商票数"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_转移二药间隔"]
  L0_2 = L0_2(L1_2)
  _ENV["转移二药间隔"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_青龙次数"]
  L0_2 = L0_2(L1_2)
  _ENV["目标青龙次数"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_卡顿掉帧"]
  L0_2 = L0_2(L1_2)
  _ENV["卡顿掉帧"] = L0_2
  L0_2 = _ENV["UI_二药频率"]
  if L0_2 == "不买二药" then
    _ENV["UI_帮贡换二药"] = false
  else
    _ENV["UI_帮贡换二药"] = true
  end
  L0_2 = _ENV["UI_车夫寻找方式"]
  if L0_2 ~= "自动识别" then
    L0_2 = _ENV["UI_车夫x"]
    if L0_2 == "" or _ENV["UI_车夫y"] == "" or _ENV["UI_出口x"] == "" or _ENV["UI_出口y"] == "" then
      L0_2 = Gameorder1
      if L0_2 == "跑商" then
        L0_2 = dialog
        L1_2 = "请输入帮派坐标"
        L2_2 = time
        L0_2(L1_2, L2_2)
        L0_2 = lua_exit
        L0_2()
      end
    end
  end
  L0_2 = Gameorder1
  if L0_2 == "青龙" then
    L0_2 = _ENV["UI_走入青龙"]
    if L0_2 then
      L0_2 = _ENV["UI_青龙x"]
      if L0_2 ~= "" then
        L0_2 = _ENV["UI_青龙y"]
        if L0_2 ~= "" then
          L0_2 = _ENV["UI_走入青龙中转"]
          if L0_2 then
            L0_2 = _ENV["UI_青龙中转x"]
            if L0_2 ~= "" then
              L0_2 = _ENV["UI_青龙中转y"]
            end
            if L0_2 == "" then
              L0_2 = dialog
              L1_2 = "勾选了（步行中转）但是没输入坐标。部分地图需要中转一下才能步行进入仓库"
              L2_2 = time
              L0_2(L1_2, L2_2)
              L0_2 = lua_exit
              L0_2()
            end
          end
        else
          L0_2 = dialog
          L1_2 = "请输入帮派青龙入口坐标（此坐标是走入的坐标，坐标是你站在仓库门口，填入你所在位置的坐标+2或-2，可以调试一下）"
          L2_2 = time
          L0_2(L1_2, L2_2)
          L0_2 = lua_exit
          L0_2()
        end
      else
        L0_2 = dialog
        L1_2 = "请输入帮派青龙入口坐标（此坐标是走入的坐标，坐标是你站在仓库门口，填入你所在位置的坐标+2或-2，可以调试一下）"
        L2_2 = time
        L0_2(L1_2, L2_2)
        L0_2 = lua_exit
        L0_2()
      end
    else
      L0_2 = _ENV["UI_车夫寻找方式"]
      if L0_2 ~= "自动识别" then
        L0_2 = _ENV["UI_车夫x"]
        if L0_2 == "" or _ENV["UI_车夫y"] == "" then
          L0_2 = dialog
          L1_2 = "请输入帮派车夫坐标"
          L2_2 = time
          L0_2(L1_2, L2_2)
          L0_2 = lua_exit
          L0_2()
        end
      end
    end
  end
  L0_2 = tonumber
  L1_2 = _ENV["UI_青龙x"]
  L0_2 = L0_2(L1_2)
  _ENV["青龙x"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_青龙y"]
  L0_2 = L0_2(L1_2)
  _ENV["青龙y"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_青龙中转x"]
  L0_2 = L0_2(L1_2)
  _ENV["青龙中转x"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_青龙中转y"]
  L0_2 = L0_2(L1_2)
  _ENV["青龙中转y"] = L0_2
  L0_2 = _ENV["UI_家具放弃方式"]
  if L0_2 == "等待3分钟放弃" then
    L0_2 = _ENV["UI_帮派总管x"]
    if L0_2 == "" or _ENV["UI_帮派总管y"] == "" then
      L0_2 = dialog
      L1_2 = "如需等待放弃,请输入帮派聚义堂总管坐标"
      L2_2 = time
      L0_2(L1_2, L2_2)
    end
  end
  L0_2 = tonumber
  L1_2 = _ENV["UI_车夫x"]
  L0_2 = L0_2(L1_2)
  _ENV["车夫x"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_车夫y"]
  L0_2 = L0_2(L1_2)
  _ENV["车夫y"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_出口x"]
  L0_2 = L0_2(L1_2)
  _ENV["出口x"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_出口y"]
  L0_2 = L0_2(L1_2)
  _ENV["出口y"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_卖药坐标X"]
  L0_2 = L0_2(L1_2)
  _ENV["_卖药坐标_x"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_卖药坐标Y"]
  L0_2 = L0_2(L1_2)
  _ENV["_卖药坐标_y"] = L0_2
  L0_2 = tonumber
  L1_2 = _ENV["UI_卖二药价格"]
  L0_2 = L0_2(L1_2)
  _ENV["_卖二药价格"] = L0_2
  L0_2 = _ENV["UI_跑商等级"]
  if L0_2 == "40" then
    _ENV["初始金额"] = 40000
    _ENV["交票金额"] = 100000
    _ENV["回城金额"] = 85000
  else
    _ENV["初始金额"] = 42000
    _ENV["交票金额"] = 150000
    _ENV["回城金额"] = 130000
  end
  L0_2 = _ENV["卖体间隔time"]
  if L0_2 == "" then
    _ENV["卖体间隔time"] = "99999"
  end
  L0_2 = tonumber
  L1_2 = _ENV["卖体间隔time"]
  L0_2 = L0_2(L1_2)
  _ENV["卖体间隔tim"] = L0_2
  L0_2 = _ENV["循环上号"]
  if L0_2 == "单号模式" then
    _ENV["UI_当前循环"] = "当前循环"
  end
  L0_2 = _ENV["UI_活力处置"]
  if L0_2 == "换修业" then
    _ENV["UI_修业"] = true
  end
  L0_2 = _ENV["不买超过一万召唤兽"]
  if L0_2 == "不买超过一万召唤兽" then
    _ENV["买召唤兽循环"] = 1
  else
    _ENV["买召唤兽循环"] = 2
  end
  L0_2 = _ENV["循环上号"]
  if L0_2 == "文本循环" then
    L0_2 = isFileExist
    L1_2 = userPath
    L1_2 = L1_2()
    L2_2 = "/res/userData.txt"
    L1_2 = L1_2 .. L2_2
    L0_2 = L0_2(L1_2)
    if L0_2 then
      while true do
        L0_2 = os
        L0_2 = L0_2.date
        L1_2 = "%Y"
        L2_2 = os
        L2_2 = L2_2.time
        L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L2_2()
        L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
        L1_2 = os
        L1_2 = L1_2.date
        L2_2 = "%m"
        L3_2 = os
        L3_2 = L3_2.time
        L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L3_2()
        L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
        L2_2 = os
        L2_2 = L2_2.date
        L3_2 = "%d"
        L4_2 = os
        L4_2 = L4_2.time
        L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L4_2()
        L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
        L3_2 = readFile
        L4_2 = userPath
        L4_2 = L4_2()
        L5_2 = "/res/userData.txt"
        L4_2 = L4_2 .. L5_2
        L3_2 = L3_2(L4_2)
        L4_2 = 1
        L5_2 = #L3_2
        L6_2 = 1
        for L7_2 = L4_2, L5_2, L6_2 do
          L8_2 = L3_2[L7_2]
          if L8_2 == "\n" or L8_2 == "\r" then
            L8_2 = removeFirstLine
            L9_2 = userPath
            L9_2 = L9_2()
            L10_2 = "/res/userData.txt"
            L9_2 = L9_2 .. L10_2
            L10_2 = 1
            L8_2(L9_2, L10_2)
            L8_2 = table
            L8_2 = L8_2.remove
            L9_2 = L3_2
            L10_2 = L7_2
            L8_2(L9_2, L10_2)
          else
          ju_shehunxiang = 0
          BXTASK = true
          L8_2 = GameLogin
          L9_2 = L3_2[L7_2]
          L8_2(L9_2)
          L8_2 = GOGAME
          L8_2()
          L8_2 = removeFirstLine
          L9_2 = userPath
          L9_2 = L9_2()
          L10_2 = "/res/userData.txt"
          L9_2 = L9_2 .. L10_2
          L10_2 = 1
          L8_2(L9_2, L10_2)
          L8_2 = writeFileString
          L9_2 = userPath
          L9_2 = L9_2()
          L10_2 = "/res/userData.txt"
          L9_2 = L9_2 .. L10_2
          L10_2 = L3_2[L7_2]
          L11_2 = "a"
          L12_2 = 1
          L8_2(L9_2, L10_2, L11_2, L12_2)
          while true do
            L8_2 = _print
            L9_2 = "退出游戏"
            L8_2(L9_2)
            L8_2 = getColour
            L9_2 = colorList
            L10_2 = "团队副本"
            L8_2 = L8_2(L9_2, L10_2)
            if L8_2 then
              L8_2 = randomClick
              L9_2 = 2
              L10_2 = 300
              L11_2 = 1593
              L12_2 = 82
              L8_2(L9_2, L10_2, L11_2, L12_2)
            else
              L8_2 = getColour
              L9_2 = GameFB
              L10_2 = "打开系统"
              L8_2 = L8_2(L9_2, L10_2)
              if L8_2 then
                L8_2 = randomClick
                L9_2 = 0
                L10_2 = 500
                L11_2 = 1404
                L12_2 = 897
                L13_2 = 1544
                L14_2 = 937
                L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
              else
                L8_2 = getColour
                L9_2 = GameFB
                L10_2 = "退出游戏"
                L8_2 = L8_2(L9_2, L10_2)
                if L8_2 then
                  L8_2 = randomClick
                  L9_2 = 0
                  L10_2 = 1500
                  L11_2 = 1088
                  L12_2 = 612
                  L13_2 = 1232
                  L14_2 = 655
                  L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                  break
                else
                  L8_2 = getColour
                  L9_2 = GameFB
                  L10_2 = "打开成就"
                  L8_2 = L8_2(L9_2, L10_2)
                  if L8_2 then
                    L8_2 = randomClick
                    L9_2 = 2
                    L10_2 = 1300
                    L11_2 = 1657
                    L12_2 = 43
                    L8_2(L9_2, L10_2, L11_2, L12_2)
                  else
                    L8_2 = getColour
                    L9_2 = _ENV["商人鬼魂"]
                    L10_2 = "关闭对话框"
                    L8_2 = L8_2(L9_2, L10_2)
                    if L8_2 then
                      L8_2 = randomClick
                      L9_2 = 2
                      L10_2 = 300
                      L11_2 = 1873
                      L12_2 = 797
                      L8_2(L9_2, L10_2, L11_2, L12_2)
                    else
                      L8_2 = randomTap
                      L9_2 = 670
                      L10_2 = 983
                      L11_2 = 10
                      L8_2(L9_2, L10_2, L11_2)
                      L8_2 = mSleep
                      L9_2 = math
                      L9_2 = L9_2.random
                      L10_2 = 500
                      L11_2 = 1000
                      L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L9_2(L10_2, L11_2)
                      L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
                    end
                  end
                end
              end
            end
            L8_2 = mSleep
            L9_2 = 100
            L8_2(L9_2)
          end
          L8_2 = os
          L8_2 = L8_2.time
          L8_2 = L8_2()
          while true do
            L9_2 = _find
            L10_2 = Color
            L10_2 = L10_2["红尘"]
            L10_2 = L10_2["登录界面1"]
            L9_2 = L9_2(L10_2)
            if L9_2 then
              L9_2 = _ENV["_随机延时"]
              L10_2 = 811
              L9_2(L10_2)
              break
            end
            L9_2 = os
            L9_2 = L9_2.time
            L9_2 = L9_2()
            L9_2 = L9_2 - L8_2
            if 30 < L9_2 then
              L9_2 = os
              L9_2 = L9_2.time
              L9_2 = L9_2()
              L8_2 = L9_2
              L9_2 = _print
              L10_2 = "卡了？"
              L9_2(L10_2)
              L9_2 = closeApp
              L10_2 = "com.netease.mhxyhtb"
              L9_2(L10_2)
              L9_2 = mSleep
              L10_2 = 2000
              L9_2(L10_2)
              L9_2 = os
              L9_2 = L9_2.execute
              L10_2 = "input keyevent KEYCODE_HOME"
              L9_2(L10_2)
              L9_2 = mSleep
              L10_2 = 2000
              L9_2(L10_2)
              L9_2 = runApp
              L10_2 = "com.netease.mhxyhtb"
              L9_2(L10_2)
            end
          end
          end
        end
        L4_2 = closeApp
        L5_2 = "com.netease.mhxyhtb"
        L4_2(L5_2)
        while true do
          L4_2 = os
          L4_2 = L4_2.date
          L5_2 = "%Y"
          L6_2 = os
          L6_2 = L6_2.time
          L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L6_2()
          L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L5_2 = os
          L5_2 = L5_2.date
          L6_2 = "%m"
          L7_2 = os
          L7_2 = L7_2.time
          L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L7_2()
          L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L6_2 = os
          L6_2 = L6_2.date
          L7_2 = "%d"
          L8_2 = os
          L8_2 = L8_2.time
          L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2()
          L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
          L7_2 = printLog
          L8_2 = "完成所有角色："
          L9_2 = L0_2
          L10_2 = "."
          L11_2 = L1_2
          L12_2 = "."
          L13_2 = L2_2
          L14_2 = " / "
          L15_2 = os
          L15_2 = L15_2.date
          L16_2 = "%H:%M:%S"
          L17_2 = os
          L17_2 = L17_2.time
          L17_2, L18_2 = L17_2()
          L15_2 = L15_2(L16_2, L17_2, L18_2)
          L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
          L7_2(L8_2)
          L7_2 = L6_2 - L2_2
          if L7_2 == 1 then
            break
          end
          L7_2 = L5_2 - L1_2
          if L7_2 == 1 then
            break
          end
          L7_2 = L4_2 - L0_2
          if L7_2 == 1 then
            break
          end
          L7_2 = mSleep
          L8_2 = 1000
          L7_2(L8_2)
        end
      end
    else
      L0_2 = dialog
      L1_2 = "请先添加账号文件再使用文本循环登录。"
      L0_2(L1_2)
      L0_2 = luaExit
      L0_2()
    end
  else
    L0_2 = _ENV["循环上号"]
    if L0_2 == "普通循环" then
      L0_2 = _ENV["角色数量"]
      if L0_2 == "" then
        L0_2 = dialog
        L1_2 = "循环模式需填写数量"
        L2_2 = time
        L0_2(L1_2, L2_2)
      end
      L0_2 = tonumber
      L1_2 = _ENV["角色数量"]
      L0_2 = L0_2(L1_2)
      L1_2 = 1
      L2_2 = L0_2
      L3_2 = 1
      for L4_2 = L1_2, L2_2, L3_2 do
        BXTASK = true
        L5_2 = _ENV["_游戏"]
        L5_2 = L5_2["循环登录"]
        L5_2()
        L5_2 = GOGAME
        L5_2()
        L5_2 = _ENV["_游戏"]
        L5_2 = L5_2["退出到登录界面"]
        L5_2()
      end
      L1_2 = closeApp
      L2_2 = "com.netease.mhxyhtb"
      L1_2(L2_2)
      L1_2 = UI_msg
      if L1_2 == "响铃" then
        L1_2 = setVolumeLevel
        L2_2 = 1
        L1_2(L2_2)
        L1_2 = playAudio
        L2_2 = userPath
        L2_2 = L2_2()
        L3_2 = "/res/GameGX.mp3"
        L2_2 = L2_2 .. L3_2
        L1_2(L2_2)
      end
      L1_2 = _ENV["没点角色"]
      if L1_2 == "" then
        L1_2 = dialog
        L2_2 = "完成任务"
        L1_2(L2_2)
      else
        L1_2 = dialog
        L2_2 = "完成任务，"
        L3_2 = _ENV["没点角色"]
        L4_2 = "欠费了"
        L2_2 = L2_2 .. L3_2 .. L4_2
        L1_2(L2_2)
      end
    else
      BXTASK = true
      L0_2 = _ENV["_游戏"]
      L0_2 = L0_2["进入2"]
      L0_2()
      L0_2 = GOGAME
      L0_2()
      L0_2 = _ENV["_游戏"]
      L0_2 = L0_2["退出到登录界面"]
      L0_2()
      L0_2 = UI_msg
      if L0_2 == "响铃" then
        L0_2 = setVolumeLevel
        L1_2 = 1
        L0_2(L1_2)
        L0_2 = playAudio
        L1_2 = userPath
        L1_2 = L1_2()
        L2_2 = "/res/GameGX.mp3"
        L1_2 = L1_2 .. L2_2
        L0_2(L1_2)
      end
      L0_2 = _ENV["没点角色"]
      if L0_2 == "" then
        L0_2 = dialog
        L1_2 = "完成任务"
        L0_2(L1_2)
      else
        L0_2 = dialog
        L1_2 = "完成任务，"
        L2_2 = _ENV["没点角色"]
        L3_2 = "欠费了"
        L1_2 = L1_2 .. L2_2 .. L3_2
        L0_2(L1_2)
      end
    end
  end
end

start = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = getTarget
  L0_2 = L0_2()
  if L0_2 == "" then
    L0_2 = openProps
    L0_2()
    L0_2 = isItemExist
    L1_2 = _ENV["押镖检测"]
    L0_2 = L0_2(L1_2)
    if L0_2 == false then
      L0_2 = isItemExist
      L1_2 = "包子"
      L0_2 = L0_2(L1_2)
      if L0_2 == false then
        L0_2 = _ENV["_购买"]
        L0_2 = L0_2["包子"]
        L0_2()
      end
    end
    L0_2 = _ENV["宠物忠诚玲珑"]
    L0_2()
  end
  L0_2 = ""
  L1_2 = award
  if L1_2 == "现金" then
    L0_2 = "cash"
  else
    L1_2 = award
    if L1_2 == "储备" then
      L0_2 = "reserve"
    end
  end
  L1_2 = level1
  if L1_2 == "1级镖" then
    travelMod = "walk"
    L1_2 = startEscorting
    L2_2 = 1
    L3_2 = L0_2
    L1_2(L2_2, L3_2)
  else
    L1_2 = level1
    if L1_2 == "2级镖" then
      travelMod = "walk"
      L1_2 = startEscorting
      L2_2 = 2
      L3_2 = L0_2
      L1_2(L2_2, L3_2)
    else
      L1_2 = level1
      if L1_2 == "3级镖" then
        travelMod = "walk"
        L1_2 = startEscorting
        L2_2 = 3
        L3_2 = L0_2
        L1_2(L2_2, L3_2)
      else
        L1_2 = level1
        if L1_2 == "4级镖" then
          travelMod = "walk"
          L1_2 = startEscorting
          L2_2 = 4
          L3_2 = L0_2
          L1_2(L2_2, L3_2)
        end
      end
    end
  end
end

_ENV["押镖任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
end

_ENV["卖体转钱任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["UI_签到"]
  if L0_2 then
    L0_2 = _ENV["自动签到"]
    L0_2()
  end
  L0_2 = _ENV["UI_跑商模式"]
  if L0_2 == "联动跑商" then
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["识别服务器名"]
    L0_2()
  end
  L0_2 = startTrading
  L0_2()
end

_ENV["跑商任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  _ENV["当前内政已刷完"] = false
  _ENV["青龙_次数"] = 0
  L0_2 = _ENV["_青龙"]
  L0_2 = L0_2["识别任务"]
  L0_2()
end

_ENV["青龙任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = _ENV["UI_定时买药"]
  if L0_2 == "" then
    L0_2 = dialog
    L1_2 = "定时时间未填写，暂时默认当前为刷药时间"
    L2_2 = 3
    L0_2(L1_2, L2_2)
    L0_2 = os
    L0_2 = L0_2.date
    L1_2 = "*t"
    L2_2 = os
    L2_2 = L2_2.time
    L2_2, L3_2 = L2_2()
    L0_2 = L0_2(L1_2, L2_2, L3_2)
    nowTime = L0_2
    L0_2 = nowTime
    L0_2 = L0_2.min
    L0_2 = L0_2 + 1
    _ENV["定时买药time"] = L0_2
  else
    L0_2 = tonumber
    L1_2 = _ENV["UI_定时买药"]
    L0_2 = L0_2(L1_2)
    L0_2 = L0_2 + 1
    _ENV["定时买药time"] = L0_2
  end
  _ENV["现在"] = true
  while true do
    L0_2 = os
    L0_2 = L0_2.date
    L1_2 = "*t"
    L2_2 = os
    L2_2 = L2_2.time
    L2_2, L3_2 = L2_2()
    L0_2 = L0_2(L1_2, L2_2, L3_2)
    nowTime = L0_2
    L0_2 = nowTime
    L0_2 = L0_2.min
    L1_2 = _ENV["定时买药time"]
    if L0_2 == L1_2 or _ENV["现在"] then
      _ENV["现在"] = false
      L0_2 = _ENV["_游戏"]
      L0_2 = L0_2["进入2"]
      L0_2()
      L0_2 = _ENV["存物品"]
      L0_2()
      L0_2 = _ENV["_前往"]
      L0_2 = L0_2["书院"]
      L0_2()
      L0_2 = _ENV["_青龙"]
      L0_2 = L0_2["进入买药界面"]
      L0_2 = L0_2()
      if L0_2 then
        L0_2 = _ENV["买药品"]
        L0_2()
      end
      L0_2 = _ENV["_游戏"]
      L0_2 = L0_2["退出到登录界面"]
      L0_2()
      L0_2 = mSleep
      L1_2 = 120000
      L0_2(L1_2)
    end
    L0_2 = mSleep
    L1_2 = 5000
    L0_2(L1_2)
    L0_2 = toast
    L1_2 = "等待帮派刷新药品"
    L2_2 = 2
    L0_2(L1_2, L2_2)
  end
end

_ENV["定时买药任务"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  _ENV["取药页数"] = 1
  L0_2 = _ENV["检测提示框"]
  L0_2()
  while true do
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["各地仓库"]
    L1_2 = _ENV["UI_卖二药地址"]
    L0_2(L1_2)
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["仓库取药"]
    L0_2()
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["前往准确坐标"]
    L1_2 = _ENV["UI_卖二药地址"]
    L2_2 = _ENV["_卖药坐标_x"]
    L3_2 = _ENV["_卖药坐标_y"]
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["摆摊"]
    L0_2()
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["摆摊输入价格"]
    L1_2 = _ENV["_卖二药价格"]
    L0_2(L1_2)
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["摆摊上架"]
    L1_2 = _ENV["_卖二药价格"]
    L0_2(L1_2)
    while true do
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["摆摊"]
      L1_2 = L1_2["普通摆摊J"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _find
        L1_2 = Color
        L1_2 = L1_2["摆摊"]
        L1_2 = L1_2["正在出售"]
        L0_2 = L0_2(L1_2)
        if L0_2 == false then
          break
        end
      end
      L0_2 = mSleep
      L1_2 = 500
      L0_2(L1_2)
    end
    repeat
      L0_2 = _find
      L1_2 = Color
      L1_2 = L1_2["摆摊"]
      L1_2 = L1_2["收摊A"]
      L0_2 = L0_2(L1_2)
      if L0_2 then
        L0_2 = _Sleep
        L1_2 = 300
        L2_2 = 500
        L0_2(L1_2, L2_2)
      end
      L0_2 = mSleep
      L1_2 = 100
      L0_2(L1_2)
      L0_2 = _cmp_tb
      L1_2 = Color
      L1_2 = L1_2["主界面"]
      L0_2 = L0_2(L1_2)
    until L0_2
    L0_2 = _ENV["没有药了"]
    if L0_2 then
      L0_2 = dialog
      L1_2 = "完成任务"
      L2_2 = time
      L0_2(L1_2, L2_2)
      break
    end
  end
end

_ENV["摆摊卖二药"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = _ENV["UI_添加好友"]
  if L0_2 then
    L0_2 = _ENV["加好友"]
    L0_2()
  end
  _ENV["取药页数"] = 1
  while true do
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["各地仓库"]
    L1_2 = _ENV["UI_卖二药地址"]
    L0_2(L1_2)
    L0_2 = _ENV["_通用"]
    L0_2 = L0_2["仓库取药"]
    L0_2()
    L0_2 = _ENV["转移二药"]
    L0_2()
    L0_2 = _ENV["通用功能"]
    L0_2 = L0_2["关闭"]
    L0_2()
    L0_2 = _ENV["没有药了"]
    if L0_2 then
      L0_2 = _ENV["UI_转移二药"]
      if L0_2 == "每X票转移" then
        L0_2 = _ENV["_前往"]
        L0_2 = L0_2["长安城"]
        L0_2()
        break
      end
      L0_2 = dialog
      L1_2 = "完成任务"
      L2_2 = time
      L0_2(L1_2, L2_2)
      L0_2 = lua_exit
      L0_2()
      break
    end
  end
end

_ENV["转移二药总流程"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2
  L0_2 = _ENV["_通用"]
  L0_2 = L0_2["各地仓库"]
  L1_2 = _ENV["UI_卖二药地址"]
  L0_2(L1_2)
  L0_2 = _ENV["加好友"]
  L0_2()
  repeat
    _ENV["钱够继续买图"] = false
    L0_2 = _ENV["购买宝图全部流程"]
    L0_2 = L0_2()
    if L0_2 then
      _ENV["钱够继续买图"] = true
    end
    L0_2 = _ENV["_前往"]
    L0_2 = L0_2["建邺城"]
    L0_2()
    L0_2 = _ENV["转移宝图"]
    L0_2()
    L0_2 = _ENV["钱够继续买图"]
  until not L0_2
end

_ENV["买图转移"] = L0_1
L0_1 = start
L0_1()
