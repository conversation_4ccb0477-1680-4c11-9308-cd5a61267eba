local L0_1, L1_1, L2_1

function L0_1(A0_2, A1_2)
  local L2_2
  if A0_2 and A1_2 then
    qs_link = A0_2
    L2_2 = {}
    L2_2.gn = ""
    L2_2.path = ""
    L2_2.name = ""
    L2_2.value = ""
    L2_2.key = A1_2
    qs_parameter = L2_2
    L2_2 = true
    return L2_2
  end
  L2_2 = false
  return L2_2
end

_ENV["青松_初始化"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L4_2 = require
  L5_2 = "ts"
  L4_2 = L4_2(L5_2)
  L5_2 = L4_2.json
  
  function L6_2(A0_3)
    local L1_3
    if not A0_3 then
      A0_3 = ""
    end
    return A0_3
  end
  
  L7_2 = qs_link
  if L7_2 or qs_parameter then
    -- 继续执行参数设置逻辑
  else
    -- 替换 goto lbl_65，直接执行对应逻辑
    L4_2 = os
    L4_2 = L4_2.time
    L4_2 = L4_2()
    L4_2 = L4_2 - L3_2
    if 10 < L4_2 then
      L4_2 = getColour
      L5_2 = colorList
      L6_2 = "红色的飞棋"
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L3_2 = L4_2
      end
    end
    return
  end
  L7_2 = qs_parameter
  L7_2.gn = A0_2
  L7_2 = qs_parameter
  L8_2 = L6_2
  L9_2 = A1_2
  L8_2 = L8_2(L9_2)
  L7_2.path = L8_2
  L7_2 = qs_parameter
  L8_2 = L6_2
  L9_2 = A2_2
  L8_2 = L8_2(L9_2)
  L7_2.name = L8_2
  L7_2 = qs_parameter
  L8_2 = L6_2
  L9_2 = A3_2
  L8_2 = L8_2(L9_2)
  L7_2.value = L8_2
  L7_2 = {}
  L7_2.tstab = 1
  L7_2.timeOut = 90
  L7_2.urlEnCode = false
  L8_2 = httpPost
  L9_2 = qs_link
  L10_2 = L5_2.encode
  L11_2 = qs_parameter
  L10_2 = L10_2(L11_2)
  L11_2 = L7_2
  L8_2 = L8_2(L9_2, L10_2, L11_2)
  str = L8_2
  L8_2 = str
  if L8_2 then
    L8_2 = pcall
    L9_2 = L5_2.decode
    L10_2 = str
    L8_2, L9_2 = L8_2(L9_2, L10_2)
    if L8_2 then
      L10_2 = L9_2.code
      if L10_2 == "200" then
        L10_2 = true
        L11_2 = L9_2.msg
        return L10_2, L11_2
      end
      L10_2 = false
      L11_2 = L9_2.cw
      return L10_2, L11_2
    end
    L10_2 = false
    L11_2 = "操作失败，返回内容异常"
    return L10_2, L11_2
  end
  L8_2 = false
  L9_2 = "操作失败，请检测请求链接是否正确"
  do return L8_2, L9_2 end
  ::lbl_65::
  L7_2 = false
  L8_2 = "操作失败，未运行函数，青松_初始化"
  return L7_2, L8_2
end

_ENV["青松_对象操作"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = math
  L1_2 = L1_2.floor
  L2_2 = A0_2 / 10
  L1_2 = L1_2(L2_2)
  L2_2 = math
  L2_2 = L2_2.floor
  L3_2 = os
  L3_2 = L3_2.date
  L4_2 = "%M"
  L5_2 = os
  L5_2 = L5_2.time
  L5_2 = L5_2()
  L3_2 = L3_2(L4_2, L5_2)
  L3_2 = L3_2 / 10
  L2_2 = L2_2(L3_2)
  L3_2 = math
  L3_2 = L3_2.abs
  L4_2 = L1_2 - L2_2
  L3_2 = L3_2(L4_2)
  if 0 < L3_2 then
    L4_2 = false
    return L4_2
  else
    L4_2 = true
    return L4_2
  end
end

_ENV["计算价格时间"] = L0_1
L0_1 = _ENV["青松_初始化"]
L1_1 = "http://106.119.164.35:2088"
L2_1 = "B1BgztgeqPgVFVrFhBhV"
L0_1(L1_1, L2_1)

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = 0
  L2_2 = pairs
  L3_2 = A0_2
  L2_2, L3_2, L4_2 = L2_2(L3_2)
  for L5_2, L6_2 in L2_2, L3_2, L4_2 do
    L1_2 = L1_2 + 1
  end
  if 0 < L1_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = true
  return L2_2
end

_ENV["判断表数量"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2)
  local L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2
  L10_2 = printLog
  L11_2 = "使用长安城飞行旗"
  L10_2(L11_2)
  L10_2 = false
  L11_2 = 300
  L12_2 = A6_2
  L13_2 = A7_2
  L14_2 = A8_2
  L15_2 = A9_2 or L15_2
  if not A9_2 then
    L15_2 = 1
  end
  L16_2 = 2
  L17_2 = 3
  L18_2 = 4
  _ENV["识别不到飞行旗"] = 0
  while true do
    if not L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = _ENV["UI_qizi_长安"]
        if L16_2 == "红旗" then
          L16_2 = redFlightFlag
          L17_2 = A0_2
          L18_2 = A1_2
          L19_2 = A2_2
          L20_2 = A3_2
          L21_2 = A4_2
          L22_2 = A5_2
          L23_2 = L12_2
          L24_2 = L13_2
          L25_2 = L14_2
          L26_2 = L15_2
          L27_2 = 1681
          L28_2 = 141
          L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
          L10_2 = L16_2
        else
          L16_2 = _ENV["长安旗"]
          if L16_2 == "蓝色" then
            L16_2 = blueFlightFlag
            L17_2 = A0_2
            L18_2 = A1_2
            L19_2 = A2_2
            L20_2 = A3_2
            L21_2 = A4_2
            L22_2 = A5_2
            L23_2 = L12_2
            L24_2 = L13_2
            L25_2 = L14_2
            L26_2 = L15_2
            L27_2 = 1681
            L28_2 = 141
            L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
            L10_2 = L16_2
          else
            L16_2 = _ENV["长安旗"]
            if L16_2 == "黄色" then
              L16_2 = yellowFlightFlag
              L17_2 = A0_2
              L18_2 = A1_2
              L19_2 = A2_2
              L20_2 = A3_2
              L21_2 = A4_2
              L22_2 = A5_2
              L23_2 = L12_2
              L24_2 = L13_2
              L25_2 = L14_2
              L26_2 = L15_2
              L27_2 = 1681
              L28_2 = 141
              L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
              L10_2 = L16_2
            else
              L16_2 = _ENV["长安旗"]
              if L16_2 == "绿色" then
                L16_2 = greenFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1681
                L28_2 = 141
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              else
                L16_2 = whiteFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1681
                L28_2 = 141
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              end
            end
          end
        end
    end
    else
      L16_2 = randomClick
      L17_2 = 0
      L18_2 = L11_2
      L19_2 = 1666
      L20_2 = 986
      L21_2 = 1731
      L22_2 = 1062
      L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
    end
    if L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = randomTap
        L17_2 = 1600
        L18_2 = 86
        L19_2 = 10
        L16_2(L17_2, L18_2, L19_2)
        L16_2 = mSleep
        L17_2 = math
        L17_2 = L17_2.random
        L18_2 = 500
        L19_2 = 1000
        L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2 = L17_2(L18_2, L19_2)
        L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
      else
        return
      end
    end
  end
end

useCAFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2)
  local L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2
  L10_2 = false
  L11_2 = printLog
  L12_2 = "使用朱紫国飞行旗"
  L11_2(L12_2)
  L11_2 = 300
  L12_2 = A6_2
  L13_2 = A7_2
  L14_2 = A8_2
  L15_2 = A9_2 or L15_2
  if not A9_2 then
    L15_2 = 1
  end
  L16_2 = 2
  L17_2 = 3
  L18_2 = 4
  _ENV["识别不到飞行旗"] = 0
  while true do
    if not L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = _ENV["朱紫旗"]
        if L16_2 == "红色" then
          L16_2 = redFlightFlag
          L17_2 = A0_2
          L18_2 = A1_2
          L19_2 = A2_2
          L20_2 = A3_2
          L21_2 = A4_2
          L22_2 = A5_2
          L23_2 = L12_2
          L24_2 = L13_2
          L25_2 = L14_2
          L26_2 = L15_2
          L27_2 = 1561
          L28_2 = 130
          L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
          L10_2 = L16_2
        else
          L16_2 = _ENV["朱紫旗"]
          if L16_2 == "蓝色" then
            L16_2 = blueFlightFlag
            L17_2 = A0_2
            L18_2 = A1_2
            L19_2 = A2_2
            L20_2 = A3_2
            L21_2 = A4_2
            L22_2 = A5_2
            L23_2 = L12_2
            L24_2 = L13_2
            L25_2 = L14_2
            L26_2 = L15_2
            L27_2 = 1561
            L28_2 = 130
            L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
            L10_2 = L16_2
          else
            L16_2 = _ENV["朱紫旗"]
            if L16_2 == "黄色" then
              L16_2 = yellowFlightFlag
              L17_2 = A0_2
              L18_2 = A1_2
              L19_2 = A2_2
              L20_2 = A3_2
              L21_2 = A4_2
              L22_2 = A5_2
              L23_2 = L12_2
              L24_2 = L13_2
              L25_2 = L14_2
              L26_2 = L15_2
              L27_2 = 1561
              L28_2 = 130
              L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
              L10_2 = L16_2
            else
              L16_2 = _ENV["朱紫旗"]
              if L16_2 == "绿色" then
                L16_2 = greenFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1561
                L28_2 = 130
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              else
                L16_2 = whiteFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1561
                L28_2 = 130
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              end
            end
          end
        end
    end
    else
      L16_2 = randomClick
      L17_2 = 0
      L18_2 = L11_2
      L19_2 = 1666
      L20_2 = 986
      L21_2 = 1731
      L22_2 = 1062
      L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
    end
    if L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = randomTap
        L17_2 = 1600
        L18_2 = 86
        L19_2 = 10
        L16_2(L17_2, L18_2, L19_2)
        L16_2 = mSleep
        L17_2 = math
        L17_2 = L17_2.random
        L18_2 = 500
        L19_2 = 1000
        L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2 = L17_2(L18_2, L19_2)
        L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
      else
        return
      end
    end
  end
end

useZZGFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2)
  local L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2
  L10_2 = printLog
  L11_2 = "使用傲来国飞行旗"
  L10_2(L11_2)
  L10_2 = false
  L11_2 = 300
  L12_2 = A6_2
  L13_2 = A7_2
  L14_2 = A8_2
  L15_2 = A9_2 or L15_2
  if not A9_2 then
    L15_2 = 1
  end
  L16_2 = 2
  L17_2 = 3
  L18_2 = 4
  _ENV["识别不到飞行旗"] = 0
  while true do
    if not L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = _ENV["傲来旗"]
        if L16_2 == "红色" then
          L16_2 = redFlightFlag
          L17_2 = A0_2
          L18_2 = A1_2
          L19_2 = A2_2
          L20_2 = A3_2
          L21_2 = A4_2
          L22_2 = A5_2
          L23_2 = L12_2
          L24_2 = L13_2
          L25_2 = L14_2
          L26_2 = L15_2
          L27_2 = 1513
          L28_2 = 134
          L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
          L10_2 = L16_2
        else
          L16_2 = _ENV["傲来旗"]
          if L16_2 == "蓝色" then
            L16_2 = blueFlightFlag
            L17_2 = A0_2
            L18_2 = A1_2
            L19_2 = A2_2
            L20_2 = A3_2
            L21_2 = A4_2
            L22_2 = A5_2
            L23_2 = L12_2
            L24_2 = L13_2
            L25_2 = L14_2
            L26_2 = L15_2
            L27_2 = 1513
            L28_2 = 134
            L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
            L10_2 = L16_2
          else
            L16_2 = _ENV["傲来旗"]
            if L16_2 == "黄色" then
              L16_2 = yellowFlightFlag
              L17_2 = A0_2
              L18_2 = A1_2
              L19_2 = A2_2
              L20_2 = A3_2
              L21_2 = A4_2
              L22_2 = A5_2
              L23_2 = L12_2
              L24_2 = L13_2
              L25_2 = L14_2
              L26_2 = L15_2
              L27_2 = 1513
              L28_2 = 134
              L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
              L10_2 = L16_2
            else
              L16_2 = _ENV["傲来旗"]
              if L16_2 == "绿色" then
                L16_2 = greenFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1513
                L28_2 = 134
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              else
                L16_2 = whiteFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1513
                L28_2 = 134
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              end
            end
          end
        end
    end
    else
      L16_2 = randomClick
      L17_2 = 0
      L18_2 = L11_2
      L19_2 = 1666
      L20_2 = 986
      L21_2 = 1731
      L22_2 = 1062
      L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
    end
    if L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = randomTap
        L17_2 = 1600
        L18_2 = 86
        L19_2 = 10
        L16_2(L17_2, L18_2, L19_2)
        L16_2 = mSleep
        L17_2 = math
        L17_2 = L17_2.random
        L18_2 = 500
        L19_2 = 1000
        L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2 = L17_2(L18_2, L19_2)
        L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
      else
        return
      end
    end
  end
end

useALGFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2)
  local L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2
  L10_2 = printLog
  L11_2 = "使用长寿村飞行旗"
  L10_2(L11_2)
  L10_2 = false
  L11_2 = 300
  L12_2 = A6_2
  L13_2 = A7_2
  L14_2 = A8_2
  L15_2 = A9_2 or L15_2
  if not A9_2 then
    L15_2 = 1
  end
  L16_2 = 2
  L17_2 = 3
  L18_2 = 4
  _ENV["识别不到飞行旗"] = 0
  while true do
    if not L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = _ENV["长寿旗"]
        if L16_2 == "红色" then
          L16_2 = redFlightFlag
          L17_2 = A0_2
          L18_2 = A1_2
          L19_2 = A2_2
          L20_2 = A3_2
          L21_2 = A4_2
          L22_2 = A5_2
          L23_2 = L12_2
          L24_2 = L13_2
          L25_2 = L14_2
          L26_2 = L15_2
          L27_2 = 1267
          L28_2 = 123
          L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
          L10_2 = L16_2
        else
          L16_2 = _ENV["长寿旗"]
          if L16_2 == "蓝色" then
            L16_2 = blueFlightFlag
            L17_2 = A0_2
            L18_2 = A1_2
            L19_2 = A2_2
            L20_2 = A3_2
            L21_2 = A4_2
            L22_2 = A5_2
            L23_2 = L12_2
            L24_2 = L13_2
            L25_2 = L14_2
            L26_2 = L15_2
            L27_2 = 1267
            L28_2 = 123
            L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
            L10_2 = L16_2
          else
            L16_2 = _ENV["长寿旗"]
            if L16_2 == "黄色" then
              L16_2 = yellowFlightFlag
              L17_2 = A0_2
              L18_2 = A1_2
              L19_2 = A2_2
              L20_2 = A3_2
              L21_2 = A4_2
              L22_2 = A5_2
              L23_2 = L12_2
              L24_2 = L13_2
              L25_2 = L14_2
              L26_2 = L15_2
              L27_2 = 1267
              L28_2 = 123
              L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
              L10_2 = L16_2
            else
              L16_2 = _ENV["长寿旗"]
              if L16_2 == "绿色" then
                L16_2 = greenFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1267
                L28_2 = 123
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              else
                L16_2 = whiteFlightFlag
                L17_2 = A0_2
                L18_2 = A1_2
                L19_2 = A2_2
                L20_2 = A3_2
                L21_2 = A4_2
                L22_2 = A5_2
                L23_2 = L12_2
                L24_2 = L13_2
                L25_2 = L14_2
                L26_2 = L15_2
                L27_2 = 1267
                L28_2 = 123
                L16_2 = L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
                L10_2 = L16_2
              end
            end
          end
        end
    end
    else
      L16_2 = randomClick
      L17_2 = 0
      L18_2 = L11_2
      L19_2 = 1666
      L20_2 = 986
      L21_2 = 1731
      L22_2 = 1062
      L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
    end
    if L10_2 then
      L16_2 = getColour
      L17_2 = colorList
      L18_2 = "打开背包"
      L16_2 = L16_2(L17_2, L18_2)
      if L16_2 then
        L16_2 = randomTap
        L17_2 = 1600
        L18_2 = 86
        L19_2 = 10
        L16_2(L17_2, L18_2, L19_2)
        L16_2 = mSleep
        L17_2 = math
        L17_2 = L17_2.random
        L18_2 = 500
        L19_2 = 1000
        L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2 = L17_2(L18_2, L19_2)
        L16_2(L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2)
      else
        return
      end
    end
  end
end

useCSFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2, A10_2, A11_2)
  local L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L12_2 = true
  L13_2 = false
  while true do
    L14_2 = getColour
    L15_2 = colorList
    L16_2 = "打开背包"
    L14_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      while true do
        L14_2 = getColour
        L15_2 = colorList
        L16_2 = "子女"
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = printLog
          L15_2 = "识别到点开子女"
          L14_2(L15_2)
          L14_2 = randomClick
          L15_2 = 0
          L16_2 = 800
          L17_2 = 1657
          L18_2 = 192
          L19_2 = 1709
          L20_2 = 322
          L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L14_2 = getWordStock
          L15_2 = WordStock
          L16_2 = "点击使用"
          L17_2 = 600
          L18_2 = 550
          L19_2 = 720
          L20_2 = 780
          L21_2 = 85
          L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          if L14_2 then
            L17_2 = determineContinue
            L17_2 = L17_2()
            if L17_2 then
              L17_2 = os
              L17_2 = L17_2.time
              L17_2 = L17_2()
              while true do
                L18_2 = findMultiColorInRegionFuzzy
                L19_2 = 15756852
                L20_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L21_2 = 90
                L22_2 = A0_2
                L23_2 = A1_2
                L24_2 = A2_2
                L25_2 = A3_2
                L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                if -1 < L18_2 then
                  L20_2 = randomClick
                  L21_2 = 2
                  L22_2 = 300
                  L23_2 = L18_2
                  L24_2 = L19_2
                  L20_2(L21_2, L22_2, L23_2, L24_2)
                  while true do
                    L20_2 = getColour
                    L21_2 = colorList
                    L22_2 = "打开背包"
                    L20_2 = L20_2(L21_2, L22_2)
                    if L20_2 then
                      L20_2 = randomTap
                      L21_2 = 1600
                      L22_2 = 86
                      L23_2 = 10
                      L20_2(L21_2, L22_2, L23_2)
                      L20_2 = mSleep
                      L21_2 = math
                      L21_2 = L21_2.random
                      L22_2 = 500
                      L23_2 = 800
                      L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                      L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                      L20_2 = true
                      return L20_2
                    else
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L18_2
                      L24_2 = L19_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                else
                  L20_2 = os
                  L20_2 = L20_2.time
                  L20_2 = L20_2()
                  L20_2 = L20_2 - L17_2
                  if 5 < L20_2 and L13_2 then
                    L20_2 = randomClick
                    L21_2 = 2
                    L22_2 = 300
                    L23_2 = A10_2
                    L24_2 = A11_2
                    L20_2(L21_2, L22_2, L23_2, L24_2)
                    L20_2 = global
                    L20_2 = L20_2["使用飞行符"]
                    L21_2 = L18_2
                    L22_2 = L19_2
                    return L20_2(L21_2, L22_2)
                  else
                    L20_2 = os
                    L20_2 = L20_2.time
                    L20_2 = L20_2()
                    L20_2 = L20_2 - L17_2
                    if 3 < L20_2 and not L13_2 then
                      L20_2 = A6_2
                      L21_2 = A7_2
                      L22_2 = A8_2
                      A3_2 = A9_2
                      A2_2 = L22_2
                      A1_2 = L21_2
                      A0_2 = L20_2
                      L13_2 = true
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L17_2 = L20_2
                    elseif L12_2 then
                      L12_2 = false
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L15_2
                      L24_2 = L16_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                end
              end
          end
          else
            L17_2 = getColour
            L18_2 = colorList
            L19_2 = "蓝色飞行棋"
            L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L20_2 = randomClick
              L21_2 = 2
              L22_2 = 300
              L23_2 = L18_2
              L24_2 = L19_2
              L20_2(L21_2, L22_2, L23_2, L24_2)
            else
              L20_2 = moveBlueFlightFlag
              L20_2 = L20_2()
              if L20_2 then
                Bool = true
                L20_2 = printLog
                L21_2 = "没发现背包飞行旗"
                L20_2(L21_2)
                L20_2 = _ENV["识别不到飞行旗"]
                if 5 <= L20_2 then
                  L20_2 = dialog
                  L21_2 = "找不到飞行旗"
                  L20_2(L21_2)
                  L20_2 = luaExit
                  L20_2()
                else
                  L20_2 = _ENV["识别不到飞行旗"]
                  L20_2 = L20_2 + 1
                  _ENV["识别不到飞行旗"] = L20_2
                  L20_2 = randomTap
                  L21_2 = 1600
                  L22_2 = 86
                  L23_2 = 10
                  L20_2(L21_2, L22_2, L23_2)
                  L20_2 = mSleep
                  L21_2 = math
                  L21_2 = L21_2.random
                  L22_2 = 500
                  L23_2 = 800
                  L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                  L20_2 = false
                  return L20_2
                end
              end
            end
          end
        end
      end
    else
      L14_2 = printLog
      L15_2 = "打开背包"
      L14_2(L15_2)
      L14_2 = randomClick
      L15_2 = 2
      L16_2 = 300
      L17_2 = 1695
      L18_2 = 985
      L19_2 = 10
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2)
    end
  end
end

blueFlightFlag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  while true do
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "不是行囊"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      while true do
        L0_2 = getWordStock
        L1_2 = WordStock
        L2_2 = "移动道具"
        L3_2 = 294
        L4_2 = 560
        L5_2 = 517
        L6_2 = 729
        L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
        if L0_2 then
          L3_2 = determineContinue
          L3_2 = L3_2()
          if L3_2 then
            L3_2 = printLog
            L4_2 = "点击移动物品"
            L3_2(L4_2)
            L3_2 = randomClick
            L4_2 = 2
            L5_2 = 400
            L6_2 = L1_2
            L7_2 = L2_2
            L8_2 = 10
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 1000
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 300
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = os
            L3_2 = L3_2.time
            L3_2 = L3_2()
            while true do
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "蓝色飞行棋"
              L4_2 = L4_2(L5_2, L6_2)
              if L4_2 then
                L4_2 = false
                return L4_2
              else
                L4_2 = os
                L4_2 = L4_2.time
                L4_2 = L4_2()
                L4_2 = L4_2 - L3_2
                if 10 < L4_2 then
                  L4_2 = getColour
                  L5_2 = colorList
                  L6_2 = "蓝色飞行棋"
                  L4_2 = L4_2(L5_2, L6_2)
                  if not L4_2 then
                    L4_2 = moveBlueFlightFlag
                    return L4_2()
                  end
                end
              end
            end
        end
        else
          L3_2 = getColour
          L4_2 = colorList
          L5_2 = "蓝色飞行棋"
          L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L6_2 = randomClick
            L7_2 = 2
            L8_2 = 300
            L9_2 = L4_2
            L10_2 = L5_2
            L6_2(L7_2, L8_2, L9_2, L10_2)
          else
            L6_2 = randomClick
            L7_2 = 0
            L8_2 = 300
            L9_2 = 885
            L10_2 = 177
            L11_2 = 1078
            L12_2 = 233
            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
            L6_2 = true
            return L6_2
          end
        end
      end
    else
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 300
      L3_2 = 1140
      L4_2 = 174
      L5_2 = 1335
      L6_2 = 234
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    end
  end
end

moveBlueFlightFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2, A10_2, A11_2)
  local L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2
  L12_2 = true
  L13_2 = false
  while true do
    L14_2 = getColour
    L15_2 = colorList
    L16_2 = "打开背包"
    L14_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      while true do
        L14_2 = getColour
        L15_2 = colorList
        L16_2 = "子女"
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = printLog
          L15_2 = "识别到点开子女"
          L14_2(L15_2)
          L14_2 = randomClick
          L15_2 = 0
          L16_2 = 800
          L17_2 = 1657
          L18_2 = 192
          L19_2 = 1709
          L20_2 = 322
          L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L14_2 = getWordStock
          L15_2 = WordStock
          L16_2 = "点击使用"
          L17_2 = 600
          L18_2 = 550
          L19_2 = 720
          L20_2 = 780
          L21_2 = 85
          L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          if L14_2 then
            L17_2 = determineContinue
            L17_2 = L17_2()
            if L17_2 then
              L17_2 = os
              L17_2 = L17_2.time
              L17_2 = L17_2()
              while true do
                L18_2 = findMultiColorInRegionFuzzy
                L19_2 = 15756852
                L20_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L21_2 = 90
                L22_2 = A0_2
                L23_2 = A1_2
                L24_2 = A2_2
                L25_2 = A3_2
                L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                if -1 < L18_2 then
                  L20_2 = randomClick
                  L21_2 = 2
                  L22_2 = 300
                  L23_2 = L18_2
                  L24_2 = L19_2
                  L20_2(L21_2, L22_2, L23_2, L24_2)
                  L20_2 = printLog
                  L21_2 = "前往飞行旗坐标点"
                  L20_2(L21_2)
                  while true do
                    L20_2 = getColour
                    L21_2 = colorList
                    L22_2 = "打开背包"
                    L20_2 = L20_2(L21_2, L22_2)
                    if L20_2 then
                      L20_2 = randomTap
                      L21_2 = 1600
                      L22_2 = 86
                      L23_2 = 10
                      L20_2(L21_2, L22_2, L23_2)
                      L20_2 = mSleep
                      L21_2 = math
                      L21_2 = L21_2.random
                      L22_2 = 500
                      L23_2 = 800
                      L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2 = L21_2(L22_2, L23_2)
                      L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                      L20_2 = true
                      return L20_2
                    else
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L18_2
                      L24_2 = L19_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                else
                  L20_2 = os
                  L20_2 = L20_2.time
                  L20_2 = L20_2()
                  L20_2 = L20_2 - L17_2
                  if 5 < L20_2 and L13_2 then
                    L20_2 = randomClick
                    L21_2 = 2
                    L22_2 = 300
                    L23_2 = A10_2
                    L24_2 = A11_2
                    L20_2(L21_2, L22_2, L23_2, L24_2)
                    L20_2 = global
                    L20_2 = L20_2["使用飞行符"]
                    L21_2 = A4_2
                    L22_2 = A5_2
                    return L20_2(L21_2, L22_2)
                  else
                    L20_2 = os
                    L20_2 = L20_2.time
                    L20_2 = L20_2()
                    L20_2 = L20_2 - L17_2
                    if 3 < L20_2 and not L13_2 then
                      L20_2 = A6_2
                      L21_2 = A7_2
                      L22_2 = A8_2
                      A3_2 = A9_2
                      A2_2 = L22_2
                      A1_2 = L21_2
                      A0_2 = L20_2
                      L13_2 = true
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L17_2 = L20_2
                    elseif L12_2 then
                      L12_2 = false
                      L20_2 = printLog
                      L21_2 = "点击使用飞行旗"
                      L20_2(L21_2)
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L15_2
                      L24_2 = L16_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                end
              end
          end
          else
            L17_2 = getColour
            L18_2 = colorList
            L19_2 = "红色的飞棋"
            L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2)
            L20_2 = getColour
            L21_2 = colorList
            L22_2 = "红色飞行棋"
            L20_2, L21_2, L22_2 = L20_2(L21_2, L22_2)
            L23_2 = printLog
            L24_2 = "点击红色飞行旗"
            L23_2(L24_2)
            if L17_2 then
              L23_2 = randomClick
              L24_2 = 2
              L25_2 = 300
              L26_2 = L18_2
              L27_2 = L19_2
              L23_2(L24_2, L25_2, L26_2, L27_2)
            elseif L20_2 then
              L23_2 = randomClick
              L24_2 = 2
              L25_2 = 300
              L26_2 = L21_2
              L27_2 = L22_2
              L23_2(L24_2, L25_2, L26_2, L27_2)
            else
              L23_2 = moveRedFlightFlag
              L23_2 = L23_2()
              if L23_2 then
                Bool = true
                L23_2 = printLog
                L24_2 = "没发现背包飞行旗"
                L23_2(L24_2)
                L23_2 = _ENV["识别不到飞行旗"]
                if 5 <= L23_2 then
                  L23_2 = dialog
                  L24_2 = "找不到飞行旗"
                  L23_2(L24_2)
                  L23_2 = luaExit
                  L23_2()
                else
                  L23_2 = _ENV["识别不到飞行旗"]
                  L23_2 = L23_2 + 1
                  _ENV["识别不到飞行旗"] = L23_2
                  L23_2 = randomTap
                  L24_2 = 1600
                  L25_2 = 86
                  L26_2 = 10
                  L23_2(L24_2, L25_2, L26_2)
                  L23_2 = mSleep
                  L24_2 = math
                  L24_2 = L24_2.random
                  L25_2 = 500
                  L26_2 = 800
                  L24_2, L25_2, L26_2, L27_2 = L24_2(L25_2, L26_2)
                  L23_2(L24_2, L25_2, L26_2, L27_2)
                  L23_2 = false
                  return L23_2
                end
              end
            end
          end
        end
      end
    else
      L14_2 = printLog
      L15_2 = "打开背包"
      L14_2(L15_2)
      L14_2 = randomClick
      L15_2 = 2
      L16_2 = 300
      L17_2 = 1695
      L18_2 = 985
      L19_2 = 10
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2)
    end
  end
end

redFlightFlag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  while true do
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "不是行囊"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      while true do
        L0_2 = getWordStock
        L1_2 = WordStock
        L2_2 = "移动道具"
        L3_2 = 294
        L4_2 = 560
        L5_2 = 517
        L6_2 = 729
        L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
        if L0_2 then
          L3_2 = determineContinue
          L3_2 = L3_2()
          if L3_2 then
            L3_2 = printLog
            L4_2 = "点击移动物品"
            L3_2(L4_2)
            L3_2 = randomClick
            L4_2 = 2
            L5_2 = 400
            L6_2 = L1_2
            L7_2 = L2_2
            L8_2 = 10
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 1000
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 300
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = os
            L3_2 = L3_2.time
            L3_2 = L3_2()
            while true do
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "红色的飞棋"
              L4_2 = L4_2(L5_2, L6_2)
              if not L4_2 then
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "红色飞行棋"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  L4_2 = false
                  return L4_2
                else
                  -- 替换 goto lbl_65，直接执行对应逻辑
                  L4_2 = os
                  L4_2 = L4_2.time
                  L4_2 = L4_2()
                  L4_2 = L4_2 - L3_2
                  if 10 < L4_2 then
                    L4_2 = getColour
                    L5_2 = colorList
                    L6_2 = "红色的飞棋"
                    L4_2 = L4_2(L5_2, L6_2)
                    if L4_2 then
                      L4_2 = os
                      L4_2 = L4_2.time
                      L4_2 = L4_2()
                      L3_2 = L4_2
                    end
                  end
                  return
                end
              end
              -- 移除 goto lbl_92 和 lbl_65 标签，这些逻辑已经在前面处理过了
              L4_2 = os
              L4_2 = L4_2.time
              L4_2 = L4_2()
              L4_2 = L4_2 - L3_2
              if 10 < L4_2 then
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "红色的飞棋"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  -- 继续执行后续逻辑
                else
                  -- 替换 goto lbl_89，直接执行对应逻辑
                  L4_2 = moveRedFlightFlag
                  return L4_2()
                end
              end
              L4_2 = os
              L4_2 = L4_2.time
              L4_2 = L4_2()
              L4_2 = L4_2 - L3_2
              if 10 < L4_2 then
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "红色飞行棋"
                L4_2 = L4_2(L5_2, L6_2)
                -- 移除 lbl_89 标签
                if not L4_2 then
                  L4_2 = moveRedFlightFlag
                  return L4_2()
                end
              end
              ::lbl_92::
            end
        end
        else
          L3_2 = getColour
          L4_2 = colorList
          L5_2 = "红色的飞棋"
          L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
          L6_2 = getColour
          L7_2 = colorList
          L8_2 = "红色飞行棋"
          L6_2, L7_2, L8_2 = L6_2(L7_2, L8_2)
          if L3_2 then
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 300
            L12_2 = L4_2
            L13_2 = L5_2
            L9_2(L10_2, L11_2, L12_2, L13_2)
            isBoolean = false
          elseif L6_2 then
            L9_2 = randomClick
            L10_2 = 2
            L11_2 = 300
            L12_2 = L7_2
            L13_2 = L8_2
            L9_2(L10_2, L11_2, L12_2, L13_2)
            isBoolean = false
          else
            L9_2 = isBoolean
            if L9_2 then
              L9_2 = dialog
              L10_2 = "飞行棋使用完请补充。。"
              L9_2(L10_2)
            else
              L9_2 = randomClick
              L10_2 = 0
              L11_2 = 300
              L12_2 = 885
              L13_2 = 177
              L14_2 = 1078
              L15_2 = 233
              L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
              L9_2 = true
              return L9_2
            end
          end
        end
      end
    else
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 300
      L3_2 = 1140
      L4_2 = 174
      L5_2 = 1335
      L6_2 = 234
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    end
  end
end

moveRedFlightFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2, A10_2, A11_2)
  local L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2
  L12_2 = true
  L13_2 = false
  while true do
    L14_2 = getColour
    L15_2 = colorList
    L16_2 = "打开背包"
    L14_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      while true do
        L14_2 = getColour
        L15_2 = colorList
        L16_2 = "子女"
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = printLog
          L15_2 = "识别到点开子女"
          L14_2(L15_2)
          L14_2 = randomClick
          L15_2 = 0
          L16_2 = 800
          L17_2 = 1657
          L18_2 = 192
          L19_2 = 1709
          L20_2 = 322
          L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L14_2 = getWordStock
          L15_2 = WordStock
          L16_2 = "点击使用"
          L17_2 = 600
          L18_2 = 550
          L19_2 = 720
          L20_2 = 780
          L21_2 = 85
          L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          if L14_2 then
            L17_2 = determineContinue
            L17_2 = L17_2()
            if L17_2 then
              L17_2 = os
              L17_2 = L17_2.time
              L17_2 = L17_2()
              L18_2 = A4_2
              L19_2 = A5_2
              while true do
                L20_2 = findMultiColorInRegionFuzzy
                L21_2 = 15756852
                L22_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L23_2 = 90
                L24_2 = A0_2
                L25_2 = A1_2
                L26_2 = A2_2
                L27_2 = A3_2
                L20_2, L21_2 = L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                if -1 < L20_2 then
                  L22_2 = randomClick
                  L23_2 = 2
                  L24_2 = 300
                  L25_2 = L20_2
                  L26_2 = L21_2
                  L22_2(L23_2, L24_2, L25_2, L26_2)
                  while true do
                    L22_2 = getColour
                    L23_2 = colorList
                    L24_2 = "打开背包"
                    L22_2 = L22_2(L23_2, L24_2)
                    if L22_2 then
                      L22_2 = randomTap
                      L23_2 = 1600
                      L24_2 = 86
                      L25_2 = 10
                      L22_2(L23_2, L24_2, L25_2)
                      L22_2 = mSleep
                      L23_2 = math
                      L23_2 = L23_2.random
                      L24_2 = 500
                      L25_2 = 800
                      L23_2, L24_2, L25_2, L26_2, L27_2 = L23_2(L24_2, L25_2)
                      L22_2(L23_2, L24_2, L25_2, L26_2, L27_2)
                      L22_2 = true
                      return L22_2
                    else
                      L22_2 = randomClick
                      L23_2 = 2
                      L24_2 = 300
                      L25_2 = L20_2
                      L26_2 = L21_2
                      L22_2(L23_2, L24_2, L25_2, L26_2)
                    end
                  end
                else
                  L22_2 = os
                  L22_2 = L22_2.time
                  L22_2 = L22_2()
                  L22_2 = L22_2 - L17_2
                  if 3 < L22_2 and L13_2 then
                    L22_2 = randomClick
                    L23_2 = 5
                    L24_2 = 300
                    L25_2 = A10_2
                    L26_2 = A11_2
                    L22_2(L23_2, L24_2, L25_2, L26_2)
                    L22_2 = global
                    L22_2 = L22_2["使用飞行符"]
                    L23_2 = L18_2
                    L24_2 = L19_2
                    return L22_2(L23_2, L24_2)
                  else
                    L22_2 = os
                    L22_2 = L22_2.time
                    L22_2 = L22_2()
                    L22_2 = L22_2 - L17_2
                    if 3 < L22_2 and not L13_2 then
                      L22_2 = A6_2
                      L23_2 = A7_2
                      L24_2 = A8_2
                      A3_2 = A9_2
                      A2_2 = L24_2
                      A1_2 = L23_2
                      A0_2 = L22_2
                      L13_2 = true
                      L22_2 = os
                      L22_2 = L22_2.time
                      L22_2 = L22_2()
                      L17_2 = L22_2
                    elseif L12_2 then
                      L12_2 = false
                      L22_2 = randomClick
                      L23_2 = 2
                      L24_2 = 300
                      L25_2 = L15_2
                      L26_2 = L16_2
                      L22_2(L23_2, L24_2, L25_2, L26_2)
                    end
                  end
                end
              end
          end
          else
            L17_2 = getColour
            L18_2 = colorList
            L19_2 = "黄色飞行棋"
            L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L20_2 = randomClick
              L21_2 = 2
              L22_2 = 300
              L23_2 = L18_2
              L24_2 = L19_2
              L20_2(L21_2, L22_2, L23_2, L24_2)
            else
              L20_2 = moveYellowFlightFlag
              L20_2 = L20_2()
              if L20_2 then
                Bool = true
                L20_2 = printLog
                L21_2 = "没发现背包飞行旗"
                L20_2(L21_2)
                L20_2 = _ENV["识别不到飞行旗"]
                if 5 <= L20_2 then
                  L20_2 = dialog
                  L21_2 = "找不到飞行旗"
                  L20_2(L21_2)
                  L20_2 = luaExit
                  L20_2()
                else
                  L20_2 = _ENV["识别不到飞行旗"]
                  L20_2 = L20_2 + 1
                  _ENV["识别不到飞行旗"] = L20_2
                  L20_2 = randomTap
                  L21_2 = 1600
                  L22_2 = 86
                  L23_2 = 10
                  L20_2(L21_2, L22_2, L23_2)
                  L20_2 = mSleep
                  L21_2 = math
                  L21_2 = L21_2.random
                  L22_2 = 500
                  L23_2 = 800
                  L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2 = L21_2(L22_2, L23_2)
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2)
                  L20_2 = false
                  return L20_2
                end
              end
            end
          end
        end
      end
    else
      L14_2 = printLog
      L15_2 = "打开背包"
      L14_2(L15_2)
      L14_2 = randomClick
      L15_2 = 2
      L16_2 = 300
      L17_2 = 1695
      L18_2 = 985
      L19_2 = 10
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2)
    end
  end
end

yellowFlightFlag = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  while true do
    L2_2 = getColour
    L3_2 = colorList
    L4_2 = "不是行囊"
    L2_2 = L2_2(L3_2, L4_2)
    if L2_2 then
      while true do
        L2_2 = getWordStock
        L3_2 = WordStock
        L4_2 = "移动道具"
        L5_2 = 294
        L6_2 = 560
        L7_2 = 517
        L8_2 = 729
        L2_2, L3_2, L4_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
        if L2_2 then
          L5_2 = determineContinue
          L5_2 = L5_2()
          if L5_2 then
            L5_2 = printLog
            L6_2 = "点击移动物品"
            L5_2(L6_2)
            L5_2 = randomClick
            L6_2 = 2
            L7_2 = 400
            L8_2 = L3_2
            L9_2 = L4_2
            L10_2 = 10
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
            L5_2 = randomClick
            L6_2 = 0
            L7_2 = 1000
            L8_2 = 885
            L9_2 = 177
            L10_2 = 1078
            L11_2 = 233
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            L5_2 = randomClick
            L6_2 = 0
            L7_2 = 300
            L8_2 = 885
            L9_2 = 177
            L10_2 = 1078
            L11_2 = 233
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
            L5_2 = os
            L5_2 = L5_2.time
            L5_2 = L5_2()
            while true do
              L6_2 = getColour
              L7_2 = colorList
              L8_2 = "黄色飞行棋"
              L6_2 = L6_2(L7_2, L8_2)
              if L6_2 then
                L6_2 = false
                return L6_2
              else
                L6_2 = os
                L6_2 = L6_2.time
                L6_2 = L6_2()
                L6_2 = L6_2 - L5_2
                if 10 < L6_2 then
                  L6_2 = getColour
                  L7_2 = colorList
                  L8_2 = "黄色飞行棋"
                  L6_2 = L6_2(L7_2, L8_2)
                  if not L6_2 then
                    L6_2 = moveYellowFlightFlag
                    L7_2 = A0_2
                    L8_2 = A1_2
                    return L6_2(L7_2, L8_2)
                  end
                end
              end
            end
        end
        else
          L5_2 = getColour
          L6_2 = colorList
          L7_2 = "黄色飞行棋"
          L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            L8_2 = randomClick
            L9_2 = 2
            L10_2 = 300
            L11_2 = L6_2
            L12_2 = L7_2
            L8_2(L9_2, L10_2, L11_2, L12_2)
          else
            L8_2 = randomClick
            L9_2 = 0
            L10_2 = 300
            L11_2 = 885
            L12_2 = 177
            L13_2 = 1078
            L14_2 = 233
            L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
            L8_2 = true
            return L8_2
          end
        end
      end
    else
      L2_2 = randomClick
      L3_2 = 0
      L4_2 = 300
      L5_2 = 1140
      L6_2 = 174
      L7_2 = 1335
      L8_2 = 234
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
    end
  end
end

moveYellowFlightFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2, A10_2, A11_2)
  local L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L12_2 = true
  L13_2 = false
  while true do
    L14_2 = getColour
    L15_2 = colorList
    L16_2 = "打开背包"
    L14_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      while true do
        L14_2 = getColour
        L15_2 = colorList
        L16_2 = "子女"
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = printLog
          L15_2 = "识别到点开子女"
          L14_2(L15_2)
          L14_2 = randomClick
          L15_2 = 0
          L16_2 = 800
          L17_2 = 1657
          L18_2 = 192
          L19_2 = 1709
          L20_2 = 322
          L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L14_2 = getWordStock
          L15_2 = WordStock
          L16_2 = "点击使用"
          L17_2 = 600
          L18_2 = 550
          L19_2 = 720
          L20_2 = 780
          L21_2 = 85
          L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          if L14_2 then
            L17_2 = determineContinue
            L17_2 = L17_2()
            if L17_2 then
              L17_2 = os
              L17_2 = L17_2.time
              L17_2 = L17_2()
              while true do
                L18_2 = findMultiColorInRegionFuzzy
                L19_2 = 15756852
                L20_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L21_2 = 90
                L22_2 = A0_2
                L23_2 = A1_2
                L24_2 = A2_2
                L25_2 = A3_2
                L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                if -1 < L18_2 then
                  L20_2 = randomClick
                  L21_2 = 2
                  L22_2 = 300
                  L23_2 = L18_2
                  L24_2 = L19_2
                  L20_2(L21_2, L22_2, L23_2, L24_2)
                  while true do
                    L20_2 = getColour
                    L21_2 = colorList
                    L22_2 = "打开背包"
                    L20_2 = L20_2(L21_2, L22_2)
                    if L20_2 then
                      L20_2 = randomTap
                      L21_2 = 1600
                      L22_2 = 86
                      L23_2 = 10
                      L20_2(L21_2, L22_2, L23_2)
                      L20_2 = mSleep
                      L21_2 = math
                      L21_2 = L21_2.random
                      L22_2 = 500
                      L23_2 = 800
                      L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                      L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                      L20_2 = true
                      return L20_2
                    else
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L18_2
                      L24_2 = L19_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                else
                  L20_2 = os
                  L20_2 = L20_2.time
                  L20_2 = L20_2()
                  L20_2 = L20_2 - L17_2
                  if 5 < L20_2 and L13_2 then
                    L20_2 = randomClick
                    L21_2 = 2
                    L22_2 = 300
                    L23_2 = A10_2
                    L24_2 = A11_2
                    L20_2(L21_2, L22_2, L23_2, L24_2)
                    L20_2 = global
                    L20_2 = L20_2["使用飞行符"]
                    L21_2 = L18_2
                    L22_2 = L19_2
                    return L20_2(L21_2, L22_2)
                  else
                    L20_2 = os
                    L20_2 = L20_2.time
                    L20_2 = L20_2()
                    L20_2 = L20_2 - L17_2
                    if 3 < L20_2 and not L13_2 then
                      L20_2 = A6_2
                      L21_2 = A7_2
                      L22_2 = A8_2
                      A3_2 = A9_2
                      A2_2 = L22_2
                      A1_2 = L21_2
                      A0_2 = L20_2
                      L13_2 = true
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L17_2 = L20_2
                    elseif L12_2 then
                      L12_2 = false
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L15_2
                      L24_2 = L16_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                end
              end
          end
          else
            L17_2 = getColour
            L18_2 = colorList
            L19_2 = "绿色飞行棋"
            L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L20_2 = randomClick
              L21_2 = 2
              L22_2 = 300
              L23_2 = L18_2
              L24_2 = L19_2
              L20_2(L21_2, L22_2, L23_2, L24_2)
            else
              L20_2 = moveGreenFlightFlag
              L21_2 = A4_2
              L22_2 = A5_2
              L20_2 = L20_2(L21_2, L22_2)
              if L20_2 then
                Bool = true
                L20_2 = printLog
                L21_2 = "没发现背包飞行旗"
                L20_2(L21_2)
                L20_2 = _ENV["识别不到飞行旗"]
                if 5 <= L20_2 then
                  L20_2 = dialog
                  L21_2 = "找不到飞行旗"
                  L20_2(L21_2)
                  L20_2 = luaExit
                  L20_2()
                else
                  L20_2 = _ENV["识别不到飞行旗"]
                  L20_2 = L20_2 + 1
                  _ENV["识别不到飞行旗"] = L20_2
                  L20_2 = randomTap
                  L21_2 = 1600
                  L22_2 = 86
                  L23_2 = 10
                  L20_2(L21_2, L22_2, L23_2)
                  L20_2 = mSleep
                  L21_2 = math
                  L21_2 = L21_2.random
                  L22_2 = 500
                  L23_2 = 800
                  L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                  L20_2 = false
                  return L20_2
                end
              end
            end
          end
        end
      end
    else
      L14_2 = printLog
      L15_2 = "打开背包"
      L14_2(L15_2)
      L14_2 = randomClick
      L15_2 = 2
      L16_2 = 300
      L17_2 = 1695
      L18_2 = 985
      L19_2 = 10
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2)
    end
  end
end

greenFlightFlag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  while true do
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "不是行囊"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      while true do
        L0_2 = getWordStock
        L1_2 = WordStock
        L2_2 = "移动道具"
        L3_2 = 294
        L4_2 = 560
        L5_2 = 517
        L6_2 = 729
        L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
        if L0_2 then
          L3_2 = determineContinue
          L3_2 = L3_2()
          if L3_2 then
            L3_2 = printLog
            L4_2 = "点击移动物品"
            L3_2(L4_2)
            L3_2 = randomClick
            L4_2 = 2
            L5_2 = 400
            L6_2 = L1_2
            L7_2 = L2_2
            L8_2 = 10
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 1000
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 300
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = os
            L3_2 = L3_2.time
            L3_2 = L3_2()
            while true do
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "绿色飞行棋"
              L4_2 = L4_2(L5_2, L6_2)
              if L4_2 then
                L4_2 = false
                return L4_2
              else
                L4_2 = os
                L4_2 = L4_2.time
                L4_2 = L4_2()
                L4_2 = L4_2 - L3_2
                if 10 < L4_2 then
                  L4_2 = getColour
                  L5_2 = colorList
                  L6_2 = "绿色飞行棋"
                  L4_2 = L4_2(L5_2, L6_2)
                  if not L4_2 then
                    L4_2 = moveGreenFlightFlag
                    return L4_2()
                  end
                end
              end
            end
        end
        else
          L3_2 = getColour
          L4_2 = colorList
          L5_2 = "绿色飞行棋"
          L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L6_2 = randomClick
            L7_2 = 2
            L8_2 = 300
            L9_2 = L4_2
            L10_2 = L5_2
            L6_2(L7_2, L8_2, L9_2, L10_2)
          else
            L6_2 = randomClick
            L7_2 = 0
            L8_2 = 300
            L9_2 = 885
            L10_2 = 177
            L11_2 = 1078
            L12_2 = 233
            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
            L6_2 = true
            return L6_2
          end
        end
      end
    else
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 300
      L3_2 = 1140
      L4_2 = 174
      L5_2 = 1335
      L6_2 = 234
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    end
  end
end

moveGreenFlightFlag = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2, A8_2, A9_2, A10_2, A11_2)
  local L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2
  L12_2 = true
  L13_2 = false
  while true do
    L14_2 = getColour
    L15_2 = colorList
    L16_2 = "打开背包"
    L14_2 = L14_2(L15_2, L16_2)
    if L14_2 then
      while true do
        L14_2 = getColour
        L15_2 = colorList
        L16_2 = "子女"
        L14_2 = L14_2(L15_2, L16_2)
        if L14_2 then
          L14_2 = printLog
          L15_2 = "识别到点开子女"
          L14_2(L15_2)
          L14_2 = randomClick
          L15_2 = 0
          L16_2 = 800
          L17_2 = 1657
          L18_2 = 192
          L19_2 = 1709
          L20_2 = 322
          L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2)
        else
          L14_2 = getWordStock
          L15_2 = WordStock
          L16_2 = "点击使用"
          L17_2 = 600
          L18_2 = 550
          L19_2 = 720
          L20_2 = 780
          L21_2 = 85
          L14_2, L15_2, L16_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
          if L14_2 then
            L17_2 = determineContinue
            L17_2 = L17_2()
            if L17_2 then
              L17_2 = os
              L17_2 = L17_2.time
              L17_2 = L17_2()
              while true do
                L18_2 = findMultiColorInRegionFuzzy
                L19_2 = 15756852
                L20_2 = "-14|10|0xf07b3f,-15|17|0xdf3f1c,-14|20|0xcf3818,1|15|0xdc3a18,12|18|0xd4401c,-10|29|0xc52e12,-17|25|0xc02c10,-15|30|0xf05622,-7|34|0xf5a56e"
                L21_2 = 90
                L22_2 = A0_2
                L23_2 = A1_2
                L24_2 = A2_2
                L25_2 = A3_2
                L18_2, L19_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2)
                if -1 < L18_2 then
                  L20_2 = randomClick
                  L21_2 = 2
                  L22_2 = 300
                  L23_2 = L18_2
                  L24_2 = L19_2
                  L20_2(L21_2, L22_2, L23_2, L24_2)
                  while true do
                    L20_2 = getColour
                    L21_2 = colorList
                    L22_2 = "打开背包"
                    L20_2 = L20_2(L21_2, L22_2)
                    if L20_2 then
                      L20_2 = randomTap
                      L21_2 = 1600
                      L22_2 = 86
                      L23_2 = 10
                      L20_2(L21_2, L22_2, L23_2)
                      L20_2 = mSleep
                      L21_2 = math
                      L21_2 = L21_2.random
                      L22_2 = 500
                      L23_2 = 800
                      L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                      L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                      L20_2 = true
                      return L20_2
                    else
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L18_2
                      L24_2 = L19_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                else
                  L20_2 = os
                  L20_2 = L20_2.time
                  L20_2 = L20_2()
                  L20_2 = L20_2 - L17_2
                  if 5 < L20_2 and L13_2 then
                    L20_2 = randomClick
                    L21_2 = 2
                    L22_2 = 300
                    L23_2 = A10_2
                    L24_2 = A11_2
                    L20_2(L21_2, L22_2, L23_2, L24_2)
                    L20_2 = global
                    L20_2 = L20_2["使用飞行符"]
                    L21_2 = L18_2
                    L22_2 = L19_2
                    return L20_2(L21_2, L22_2)
                  else
                    L20_2 = os
                    L20_2 = L20_2.time
                    L20_2 = L20_2()
                    L20_2 = L20_2 - L17_2
                    if 3 < L20_2 and not L13_2 then
                      L13_2 = true
                      L20_2 = A6_2
                      L21_2 = A7_2
                      L22_2 = A8_2
                      A3_2 = A9_2
                      A2_2 = L22_2
                      A1_2 = L21_2
                      A0_2 = L20_2
                      L20_2 = os
                      L20_2 = L20_2.time
                      L20_2 = L20_2()
                      L17_2 = L20_2
                    elseif L12_2 then
                      L12_2 = false
                      L20_2 = randomClick
                      L21_2 = 2
                      L22_2 = 300
                      L23_2 = L15_2
                      L24_2 = L16_2
                      L20_2(L21_2, L22_2, L23_2, L24_2)
                    end
                  end
                end
              end
          end
          else
            L17_2 = getColour
            L18_2 = colorList
            L19_2 = "白色飞行棋"
            L17_2, L18_2, L19_2 = L17_2(L18_2, L19_2)
            if L17_2 then
              L20_2 = randomClick
              L21_2 = 2
              L22_2 = 300
              L23_2 = L18_2
              L24_2 = L19_2
              L20_2(L21_2, L22_2, L23_2, L24_2)
            else
              L20_2 = moveWhiteFlightFlag
              L20_2 = L20_2()
              if L20_2 then
                Bool = true
                L20_2 = printLog
                L21_2 = "没发现背包飞行旗"
                L20_2(L21_2)
                L20_2 = _ENV["识别不到飞行旗"]
                if 5 <= L20_2 then
                  L20_2 = dialog
                  L21_2 = "找不到飞行旗"
                  L20_2(L21_2)
                  L20_2 = luaExit
                  L20_2()
                else
                  L20_2 = _ENV["识别不到飞行旗"]
                  L20_2 = L20_2 + 1
                  _ENV["识别不到飞行旗"] = L20_2
                  L20_2 = randomTap
                  L21_2 = 1600
                  L22_2 = 86
                  L23_2 = 10
                  L20_2(L21_2, L22_2, L23_2)
                  L20_2 = mSleep
                  L21_2 = math
                  L21_2 = L21_2.random
                  L22_2 = 500
                  L23_2 = 800
                  L21_2, L22_2, L23_2, L24_2, L25_2 = L21_2(L22_2, L23_2)
                  L20_2(L21_2, L22_2, L23_2, L24_2, L25_2)
                  L20_2 = false
                  return L20_2
                end
              end
            end
          end
        end
      end
    else
      L14_2 = printLog
      L15_2 = "打开背包"
      L14_2(L15_2)
      L14_2 = randomClick
      L15_2 = 2
      L16_2 = 300
      L17_2 = 1695
      L18_2 = 985
      L19_2 = 10
      L14_2(L15_2, L16_2, L17_2, L18_2, L19_2)
    end
  end
end

whiteFlightFlag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  while true do
    L0_2 = getColour
    L1_2 = colorList
    L2_2 = "不是行囊"
    L0_2 = L0_2(L1_2, L2_2)
    if L0_2 then
      while true do
        L0_2 = getWordStock
        L1_2 = WordStock
        L2_2 = "移动道具"
        L3_2 = 294
        L4_2 = 560
        L5_2 = 517
        L6_2 = 729
        L0_2, L1_2, L2_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
        if L0_2 then
          L3_2 = determineContinue
          L3_2 = L3_2()
          if L3_2 then
            L3_2 = printLog
            L4_2 = "点击移动物品"
            L3_2(L4_2)
            L3_2 = randomClick
            L4_2 = 2
            L5_2 = 400
            L6_2 = L1_2
            L7_2 = L2_2
            L8_2 = 10
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 1000
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 300
            L6_2 = 885
            L7_2 = 177
            L8_2 = 1078
            L9_2 = 233
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            L3_2 = os
            L3_2 = L3_2.time
            L3_2 = L3_2()
            while true do
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "白色飞行棋"
              L4_2 = L4_2(L5_2, L6_2)
              if L4_2 then
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "不是行囊"
                L4_2 = L4_2(L5_2, L6_2)
                if not L4_2 then
                  L4_2 = false
                  return L4_2
              end
              else
                L4_2 = os
                L4_2 = L4_2.time
                L4_2 = L4_2()
                L4_2 = L4_2 - L3_2
                if 10 < L4_2 then
                  L4_2 = getColour
                  L5_2 = colorList
                  L6_2 = "白色飞行棋"
                  L4_2 = L4_2(L5_2, L6_2)
                  if not L4_2 then
                    L4_2 = moveWhiteFlightFlag
                    return L4_2()
                end
                else
                  L4_2 = getColour
                  L5_2 = colorList
                  L6_2 = "不是行囊"
                  L4_2 = L4_2(L5_2, L6_2)
                  if L4_2 then
                    L4_2 = randomClick
                    L5_2 = 0
                    L6_2 = 300
                    L7_2 = 885
                    L8_2 = 177
                    L9_2 = 1078
                    L10_2 = 233
                    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
                  end
                end
              end
            end
        end
        else
          L3_2 = getColour
          L4_2 = colorList
          L5_2 = "白色飞行棋"
          L3_2, L4_2, L5_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L6_2 = randomClick
            L7_2 = 2
            L8_2 = 300
            L9_2 = L4_2
            L10_2 = L5_2
            L6_2(L7_2, L8_2, L9_2, L10_2)
          else
            L6_2 = randomClick
            L7_2 = 0
            L8_2 = 300
            L9_2 = 885
            L10_2 = 177
            L11_2 = 1078
            L12_2 = 233
            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
            L6_2 = true
            return L6_2
          end
        end
      end
    else
      L0_2 = randomClick
      L1_2 = 0
      L2_2 = 300
      L3_2 = 1140
      L4_2 = 174
      L5_2 = 1335
      L6_2 = 234
      L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
    end
  end
end

moveWhiteFlightFlag = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L0_2 = {}
  L1_2 = {}
  L2_2 = 9868931
  L3_2 = "-10|20|0xd7be2f,-23|-3|0xe6e9dc,-1|-6|0xced2bf,-12|18|0xe4d87f,0|14|0xb69135,8|-6|0xd0c595,-16|-4|0xe2e4d3,-19|11|0xcaceac,-15|21|0xe0c123"
  L4_2 = 90
  L5_2 = 301
  L6_2 = 281
  L7_2 = 432
  L8_2 = 402
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  L1_2[4] = L5_2
  L1_2[5] = L6_2
  L1_2[6] = L7_2
  L1_2[7] = L8_2
  L2_2 = {}
  L3_2 = 15785984
  L4_2 = "31|-14|0xc034d8,37|-21|0xcca317,-5|-23|0xa26f3b,-5|-8|0xc3ab00,13|3|0xc132de,16|-19|0xbea504,15|-29|0xad6a5e,3|-4|0xe0c505,1|13|0x8b7a16"
  L5_2 = 90
  L6_2 = 303
  L7_2 = 280
  L8_2 = 426
  L9_2 = 406
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L2_2[5] = L7_2
  L2_2[6] = L8_2
  L2_2[7] = L9_2
  L3_2 = {}
  L4_2 = 2001152
  L5_2 = "15|-6|0x847100,11|12|0x5bab02,-1|20|0x41e002,-10|22|0x31c500,-16|-1|0x33b200,-21|-7|0x7d9107,-11|29|0x446800,-4|24|0xdcb816,19|16|0x3cb800"
  L6_2 = 90
  L7_2 = 299
  L8_2 = 276
  L9_2 = 427
  L10_2 = 404
  L3_2[1] = L4_2
  L3_2[2] = L5_2
  L3_2[3] = L6_2
  L3_2[4] = L7_2
  L3_2[5] = L8_2
  L3_2[6] = L9_2
  L3_2[7] = L10_2
  L4_2 = {}
  L5_2 = 15602960
  L6_2 = "-4|-22|0xc4210e,-3|-30|0xb0280b,19|-29|0xdb2e09,24|-23|0xaa5700,18|-8|0xe20c10,4|-12|0x230403,-2|-3|0xb60608,-9|8|0xa82005,-3|25|0x787580"
  L7_2 = 90
  L8_2 = 300
  L9_2 = 278
  L10_2 = 428
  L11_2 = 405
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L5_2 = {}
  L6_2 = 16310
  L7_2 = "-19|13|0x615d4b,-27|-9|0x0039a3,2|-19|0x4f544c,-8|-5|0x01348e,-20|10|0x0041b9,-29|4|0x003497,-25|-11|0x003ca9,-19|18|0x3b443e,-11|4|0x003184"
  L8_2 = 90
  L9_2 = 301
  L10_2 = 279
  L11_2 = 428
  L12_2 = 404
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L5_2[4] = L9_2
  L5_2[5] = L10_2
  L5_2[6] = L11_2
  L5_2[7] = L12_2
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L0_2[5] = L5_2
  L1_2 = 1
  L2_2 = #L0_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = findMultiColorInRegionFuzzy
    L6_2 = L0_2[L4_2]
    L6_2 = L6_2[1]
    L7_2 = L0_2[L4_2]
    L7_2 = L7_2[2]
    L8_2 = L0_2[L4_2]
    L8_2 = L8_2[3]
    L9_2 = L0_2[L4_2]
    L9_2 = L9_2[4]
    L10_2 = L0_2[L4_2]
    L10_2 = L10_2[5]
    L11_2 = L0_2[L4_2]
    L11_2 = L11_2[6]
    L12_2 = L0_2[L4_2]
    L12_2 = L12_2[7]
    L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    if -1 < L5_2 then
      L7_2 = true
      L8_2 = L5_2
      L9_2 = L6_2
      return L7_2, L8_2, L9_2
    end
  end
  L1_2 = false
  L2_2 = -1
  L3_2 = -1
  return L1_2, L2_2, L3_2
end

determineContinue = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  if A1_2 == "巡逻" then
    L2_2 = printLog
    L3_2 = "门派巡逻"
    L2_2(L3_2)
  else
    L2_2 = printLog
    L3_2 = "前往门派传送NPC"
    L2_2(L3_2)
  end
  L2_2 = A1_2 or L2_2
  if not A1_2 then
    L2_2 = ""
  end
  L3_2 = {}
  L4_2 = {}
  L5_2 = 1
  L6_2 = 411
  L7_2 = 886
  L8_2 = "125"
  L9_2 = "134"
  L10_2 = 758
  L11_2 = 411
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["方寸山"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 1478
  L7_2 = 854
  L8_2 = "51"
  L9_2 = "77"
  L10_2 = 640
  L11_2 = 390
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["神木林"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 475
  L7_2 = 963
  L8_2 = "61"
  L9_2 = "31"
  L10_2 = 1036
  L11_2 = 621
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["化生寺"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 1564
  L7_2 = 899
  L8_2 = "78"
  L9_2 = "46"
  L10_2 = 765
  L11_2 = 607
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["大唐官府"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 1535
  L7_2 = 886
  L8_2 = "151"
  L9_2 = "60"
  L10_2 = 1278
  L11_2 = 426
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["天宫"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 357
  L7_2 = 886
  L8_2 = "113"
  L9_2 = "62"
  L10_2 = 954
  L11_2 = 784
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["龙宫"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 1872
  L7_2 = 1045
  L8_2 = "10"
  L9_2 = "60"
  L10_2 = 737
  L11_2 = 789
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["普陀山"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 496
  L7_2 = 938
  L8_2 = "55"
  L9_2 = "35"
  L10_2 = 1036
  L11_2 = 652
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["五庄观"] = L4_2
  L4_2 = {}
  L5_2 = 0
  L6_2 = 0
  L7_2 = 0
  L8_2 = "70"
  L9_2 = "91"
  L10_2 = 1149
  L11_2 = 691
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["花果山"] = L4_2
  L4_2 = {}
  L5_2 = 0
  L6_2 = 0
  L7_2 = 0
  L8_2 = "46"
  L9_2 = "98"
  L10_2 = 840
  L11_2 = 495
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["凌波城"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 1362
  L7_2 = 903
  L8_2 = "32"
  L9_2 = "66"
  L10_2 = 847
  L11_2 = 898
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["地府"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 224
  L7_2 = 838
  L8_2 = "60"
  L9_2 = "52"
  L10_2 = 1208
  L11_2 = 384
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["无底洞"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 326
  L7_2 = 886
  L8_2 = "115"
  L9_2 = "23"
  L10_2 = 985
  L11_2 = 341
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["狮驼岭"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 539
  L7_2 = 882
  L8_2 = "187"
  L9_2 = "126"
  L10_2 = 1173
  L11_2 = 451
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["盘丝洞"] = L4_2
  L4_2 = {}
  L5_2 = 1
  L6_2 = 384
  L7_2 = 807
  L8_2 = "90"
  L9_2 = "70"
  L10_2 = 759
  L11_2 = 434
  L4_2[1] = L5_2
  L4_2[2] = L6_2
  L4_2[3] = L7_2
  L4_2[4] = L8_2
  L4_2[5] = L9_2
  L4_2[6] = L10_2
  L4_2[7] = L11_2
  L3_2["魔王寨"] = L4_2
  if A0_2 == "普陀山" or A0_2 == "狮驼岭" or A0_2 == "盘丝洞" or A0_2 == "魔王寨" then
    L4_2 = _ENV["通用功能"]
    L4_2 = L4_2["关闭"]
    L4_2()
    L4_2 = _ENV["_使用"]
    L4_2 = L4_2["摄魂香2"]
    L5_2 = true
    L4_2(L5_2)
  end
  while true do
    L4_2 = getColour
    L5_2 = colorList
    L6_2 = "打开背包"
    L4_2 = L4_2(L5_2, L6_2)
    if L4_2 then
      L4_2 = randomTap
      L5_2 = 1604
      L6_2 = 84
      L4_2(L5_2, L6_2)
      L4_2 = mSleep
      L5_2 = math
      L5_2 = L5_2.random
      L6_2 = 200
      L7_2 = 500
      L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
    else
      L4_2 = getColour
      L5_2 = colorList
      L6_2 = "关闭对话框"
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = randomClick
        L5_2 = 2
        L6_2 = 200
        L7_2 = 1848
        L8_2 = 71
        L4_2(L5_2, L6_2, L7_2, L8_2)
      else
        L4_2 = getWordStock
        L5_2 = WordStock
        L6_2 = "长安城"
        L7_2 = 154
        L8_2 = 37
        L9_2 = 308
        L10_2 = 78
        L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
        if not L4_2 then
          L4_2 = getNewYearView
          L5_2 = "长安城"
          L4_2 = L4_2(L5_2)
          if L4_2 then
            return
          else
            -- 替换 goto lbl_227，直接执行对应逻辑
            L4_2 = getColour
            L5_2 = SmMapData
            L5_2 = L5_2[A0_2]
            L6_2 = 1
            L4_2 = L4_2(L5_2, L6_2)
            if L4_2 then
              L4_2 = randomClick
              L5_2 = 0
              L6_2 = 1000
              L7_2 = SmMapData
              L7_2 = L7_2[A0_2]
              L7_2 = L7_2[1]
              L7_2 = L7_2[1]
              L8_2 = SmMapData
              L8_2 = L8_2[A0_2]
              L8_2 = L8_2[1]
              L8_2 = L8_2[2]
              L4_2(L5_2, L6_2, L7_2, L8_2)
            end
            return
          end
        end
        -- 移除 goto lbl_246 和 lbl_227 标签，这些逻辑已经在前面处理过了
        L4_2 = getColour
        L5_2 = SmMapData
        L5_2 = L5_2[A0_2]
        L6_2 = 1
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          L4_2 = randomClick
          L5_2 = 2
          L6_2 = 1300
          L7_2 = SmMapData
          L7_2 = L7_2[A0_2]
          L7_2 = L7_2[6]
          L8_2 = SmMapData
          L8_2 = L8_2[A0_2]
          L8_2 = L8_2[7]
          L4_2(L5_2, L6_2, L7_2, L8_2)
        else
          break
        end
      end
    end
    ::lbl_246::
  end
  L4_2 = os
  L4_2 = L4_2.time
  L4_2 = L4_2()
  _ENV["卡点时间"] = L4_2
  L4_2 = L3_2[A0_2]
  L4_2 = L4_2[1]
  if L4_2 ~= 0 or L2_2 == "巡逻" then
    while true do
      L4_2 = _ENV["当前门派"]
      L5_2 = A0_2
      L4_2 = L4_2(L5_2)
      if L4_2 then
        L4_2 = mSleep
        L5_2 = math
        L5_2 = L5_2.random
        L6_2 = 2000
        L7_2 = 2500
        L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L5_2(L6_2, L7_2)
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        if L2_2 == "巡逻" then
          return
        else
          break
        end
      else
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L5_2 = _ENV["卡点时间"]
        L4_2 = L4_2 - L5_2
        if 300 < L4_2 then
          L4_2 = _ENV["卡点处理"]
          return L4_2()
        else
          L4_2 = getWordStock
          L5_2 = WordStock
          L6_2 = "长安城"
          L7_2 = 154
          L8_2 = 37
          L9_2 = 308
          L10_2 = 78
          L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
          if not L4_2 then
            L4_2 = getNewYearView
            L5_2 = "长安城"
            L4_2 = L4_2(L5_2)
            if L4_2 then
              return
            else
              -- 替换 goto lbl_303，直接执行对应逻辑
              L4_2 = getColour
              L5_2 = colorList
              L6_2 = "关闭对话框"
              L4_2 = L4_2(L5_2, L6_2)
              if L4_2 then
                L4_2 = randomClick
                L5_2 = 2
                L6_2 = 200
                L7_2 = 1848
                L8_2 = 71
                L4_2(L5_2, L6_2, L7_2, L8_2)
              end
              return
            end
          end
          -- 移除 goto lbl_339 和 lbl_303 标签，这些逻辑已经在前面处理过了
          L4_2 = getColour
          L5_2 = colorList
          L6_2 = "关闭对话框"
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 then
            L4_2 = randomClick
            L5_2 = 2
            L6_2 = 500
            L7_2 = 1873
            L8_2 = 797
            L4_2(L5_2, L6_2, L7_2, L8_2)
          else
            L4_2 = _ENV["判断收缩充值"]
            L4_2()
            L4_2 = randomClick
            L5_2 = 2
            L6_2 = 300
            L7_2 = L3_2[A0_2]
            L7_2 = L7_2[2]
            L8_2 = L3_2[A0_2]
            L8_2 = L8_2[3]
            L4_2(L5_2, L6_2, L7_2, L8_2)
            L4_2 = 1
            L5_2 = 20
            L6_2 = 1
            for L7_2 = L4_2, L5_2, L6_2 do
              L8_2 = _ENV["当前门派"]
              L9_2 = A0_2
              L8_2 = L8_2(L9_2)
              if L8_2 then
                break
              end
              L8_2 = mSleep
              L9_2 = 100
              L8_2(L9_2)
            end
          end
        end
      end
      ::lbl_339::
    end
  end
  if A0_2 == "化生寺" then
    L4_2 = true
    while true do
      L5_2 = getColour
      L6_2 = SmMapData
      L6_2 = L6_2["化生寺"]
      L7_2 = 1
      L5_2 = L5_2(L6_2, L7_2)
      if L5_2 then
        L4_2 = false
        L5_2 = randomClick
        L6_2 = 1
        L7_2 = 300
        L8_2 = 1063
        L9_2 = 700
        L5_2(L6_2, L7_2, L8_2, L9_2)
        L5_2 = randomClick
        L6_2 = 2
        L7_2 = 300
        L8_2 = 1574
        L9_2 = 137
        L5_2(L6_2, L7_2, L8_2, L9_2)
      else
        L5_2 = os
        L5_2 = L5_2.time
        L5_2 = L5_2()
        L6_2 = _ENV["卡点时间"]
        L5_2 = L5_2 - L6_2
        if 300 < L5_2 then
          L5_2 = _ENV["卡点处理"]
          return L5_2()
        else
          L5_2 = monitorAddress
          L6_2 = "61"
          L7_2 = "31"
          L5_2 = L5_2(L6_2, L7_2)
          if L5_2 then
            break
          end
          if L4_2 then
            L5_2 = randomClick
            L6_2 = 2
            L7_2 = 300
            L8_2 = 217
            L9_2 = 54
            L10_2 = 20
            L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
          end
        end
      end
    end
  end
  if A0_2 == "花果山" then
    L4_2 = true
    L5_2 = os
    L5_2 = L5_2.time
    L5_2 = L5_2()
    while true do
      L6_2 = getColour
      L7_2 = SmMapData
      L7_2 = L7_2["花果山"]
      L8_2 = 1
      L6_2 = L6_2(L7_2, L8_2)
      if L6_2 then
        L4_2 = false
        L6_2 = _ENV["筛选幻境花果山npc"]
        L6_2()
        L6_2 = randomClick
        L7_2 = 1
        L8_2 = 300
        L9_2 = 1095
        L10_2 = 385
        L6_2(L7_2, L8_2, L9_2, L10_2)
        L6_2 = _ENV["通用功能"]
        L6_2 = L6_2["叉叉"]
        L6_2()
      else
        L6_2 = os
        L6_2 = L6_2.time
        L6_2 = L6_2()
        L7_2 = _ENV["卡点时间"]
        L6_2 = L6_2 - L7_2
        if 300 < L6_2 then
          L6_2 = _ENV["卡点处理"]
          return L6_2()
        else
          L6_2 = os
          L6_2 = L6_2.time
          L6_2 = L6_2()
          L6_2 = L6_2 - L5_2
          if 20 < L6_2 then
            L6_2 = os
            L6_2 = L6_2.time
            L6_2 = L6_2()
            L5_2 = L6_2
            L4_2 = true
          else
            L6_2 = _ENV["monitorAddress改"]
            L7_2 = "70"
            L8_2 = "91"
            L6_2 = L6_2(L7_2, L8_2)
            if L6_2 then
              L6_2 = _ENV["_功能"]
              L6_2 = L6_2["屏蔽"]
              L7_2 = 3
              L6_2(L7_2)
              break
            elseif L4_2 then
              L6_2 = randomClick
              L7_2 = 2
              L8_2 = 300
              L9_2 = 217
              L10_2 = 54
              L11_2 = 20
              L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
            end
          end
        end
      end
      L6_2 = _cmp_tb
      L7_2 = Color
      L7_2 = L7_2["地图"]
      L7_2 = L7_2["到达长安城"]
      L6_2 = L6_2(L7_2)
      if L6_2 then
        break
      end
    end
  end
  L4_2 = true
  L5_2 = 0
  L6_2 = true
  while true do
    if L4_2 then
      L7_2 = _ENV["当前门派"]
      L8_2 = A0_2
      L7_2 = L7_2(L8_2)
      if L7_2 then
        L7_2 = _ENV["monitorAddress改"]
        L8_2 = L3_2[A0_2]
        L8_2 = L8_2[4]
        L9_2 = L3_2[A0_2]
        L9_2 = L9_2[5]
        L7_2 = L7_2(L8_2, L9_2)
        if L7_2 then
          L7_2 = printLog
          L8_2 = "到达"
          L9_2 = A0_2
          L10_2 = "："
          L11_2 = L3_2[A0_2]
          L11_2 = L11_2[4]
          L12_2 = ","
          L13_2 = L3_2[A0_2]
          L13_2 = L13_2[5]
          L14_2 = "传送长安城."
          L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
          L7_2(L8_2)
          L7_2 = L3_2[A0_2]
          L7_2 = L7_2[1]
          if L7_2 == 0 then
          end
          L7_2 = moveJudge
          L7_2()
          L7_2 = randomClick
          L8_2 = 2
          L9_2 = 300
          L10_2 = L3_2[A0_2]
          L10_2 = L10_2[6]
          L11_2 = L3_2[A0_2]
          L11_2 = L11_2[7]
          L7_2(L8_2, L9_2, L10_2, L11_2)
          L4_2 = false
          L7_2 = _find_cx
          L8_2 = Color
          L8_2 = L8_2["起号"]
          L8_2 = L8_2["出现对话框"]
          L9_2 = {}
          L10_2 = 20
          L11_2 = 100
          L9_2[1] = L10_2
          L9_2[2] = L11_2
          L7_2 = L7_2(L8_2, L9_2)
          if L7_2 then
          end
        else
          if L6_2 then
            L7_2 = L3_2[A0_2]
            L7_2 = L7_2[1]
            if L7_2 == 0 then
              L7_2 = _ENV["隐藏任务"]
              L7_2()
              L7_2 = printLog
              L8_2 = "前往"
              L9_2 = A0_2
              L10_2 = "："
              L11_2 = L3_2[A0_2]
              L11_2 = L11_2[4]
              L12_2 = ","
              L13_2 = L3_2[A0_2]
              L13_2 = L13_2[5]
              L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2
              L7_2(L8_2)
              L5_2 = 0
              while true do
                L7_2 = getWordStock
                L8_2 = WordStock
                L9_2 = "长安城"
                L10_2 = 154
                L11_2 = 37
                L12_2 = 308
                L13_2 = 78
                L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                if L7_2 then
                  return
                else
                  L7_2 = os
                  L7_2 = L7_2.time
                  L7_2 = L7_2()
                  L8_2 = _ENV["卡点时间"]
                  L7_2 = L7_2 - L8_2
                  if 300 < L7_2 then
                    L7_2 = _ENV["卡点处理"]
                    return L7_2()
                  else
                    L7_2 = getColour
                    L8_2 = SmMapData
                    L8_2 = L8_2[A0_2]
                    L9_2 = 1
                    L7_2 = L7_2(L8_2, L9_2)
                    if L7_2 then
                      L7_2 = getWordStock
                      L8_2 = _ENV["师门洞府"]
                      L9_2 = A0_2
                      L10_2 = 148
                      L11_2 = 35
                      L12_2 = 316
                      L13_2 = 80
                      L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                      if not L7_2 then
                        L7_2 = {}
                        L8_2 = "0f00000000f07ffffc0f1ffffff0f3ffffff0f3ef8f000f3cf0f000f3cf0e000f3870e00cf3870e0cef3870e0eff3870e0eff3870e0f3f3870e0f1f3cf0e0f0f3cf0f0f0f3ef8f9f0f3ffffff0f1fffffc0f07ffff00f00000000f00000000f00000001f01c00043f0ffff0cff0ffff0eff0ffff0eef000000fcf000000f0f000000f0f000000f0f000000f0f000001f0f1ffffff0f3fffffe0f1fffff80e000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000f03c00000f07c00003e0fc00007c0ffffc1f83fffff3f07ffffffe3fe0000fc3f00000e07e0000040fc0000000000000607001000e0f003000f0f007000f0f007000f0f007000f0f007000f0f007000f0f007000f4f007800ffffffffffffffffffffffffffff7ffffffff0f80f800f0f007000f0f007000f0f007000f0f007000f0f007000f0f007000f0f007000f0f007000f0f003000e0f000000e070000004$前往$1147$36$88"
                        L7_2[1] = L8_2
                        L8_2 = addTSOcrDictEx
                        L9_2 = L7_2
                        L8_2 = L8_2(L9_2)
                        L9_2 = tsFindText
                        L10_2 = L8_2
                        L11_2 = "前往"
                        L12_2 = 425
                        L13_2 = 5
                        L14_2 = 1300
                        L15_2 = 600
                        L16_2 = "D8DFE3 , 342F2C"
                        L17_2 = 90
                        L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                        if -1 < L9_2 then
                          L11_2 = wordStock
                          L11_2 = L11_2.inputXValue
                          L12_2 = L3_2[A0_2]
                          L12_2 = L12_2[4]
                          L13_2 = L9_2
                          L14_2 = L10_2
                          L15_2 = A0_2
                          L11_2(L12_2, L13_2, L14_2, L15_2)
                          L11_2 = wordStock
                          L11_2 = L11_2.inputYValue
                          L12_2 = L3_2[A0_2]
                          L12_2 = L12_2[5]
                          L13_2 = L9_2
                          L14_2 = L10_2
                          L15_2 = A0_2
                          L11_2(L12_2, L13_2, L14_2, L15_2)
                          L11_2 = randomClick
                          L12_2 = 2
                          L13_2 = 400
                          L14_2 = SmMapData
                          L14_2 = L14_2[A0_2]
                          L14_2 = L14_2[6]
                          L15_2 = SmMapData
                          L15_2 = L15_2[A0_2]
                          L15_2 = L15_2[7]
                          L11_2(L12_2, L13_2, L14_2, L15_2)
                          L6_2 = false
                          -- 替换 goto lbl_912，直接跳出循环
                          break
                        else
                          L11_2 = mSleep
                          L12_2 = 100
                          L11_2(L12_2)
                        end
                    end
                    else
                      L7_2 = _ENV["当前门派"]
                      L8_2 = A0_2
                      L7_2 = L7_2(L8_2)
                      if not L7_2 then
                        L7_2 = getWordStock
                        L8_2 = WordStock
                        L9_2 = "长安城"
                        L10_2 = 154
                        L11_2 = 37
                        L12_2 = 308
                        L13_2 = 78
                        L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
                        if L7_2 then
                          -- 继续执行后续逻辑
                        else
                          -- 替换 goto lbl_662，直接执行对应逻辑
                          L7_2 = _ENV["长安传送NPC"]
                          L8_2 = A0_2
                          L9_2 = L2_2
                          return L7_2(L8_2, L9_2)
                        end
                      end
                      L7_2 = _ENV["当前门派"]
                      L8_2 = A0_2
                      L7_2 = L7_2(L8_2)
                      if not L7_2 then
                        L7_2 = getNewYearView
                        L8_2 = "长安城"
                        L7_2 = L7_2(L8_2)
                        if not L7_2 then
                          -- 移除 lbl_662 标签，这部分逻辑已经在前面处理过了
                          L7_2 = _ENV["长安传送NPC"]
                          L8_2 = A0_2
                          L9_2 = L2_2
                          return L7_2(L8_2, L9_2)
                        end
                      else
                        L7_2 = randomClick
                        L8_2 = 2
                        L9_2 = 300
                        L10_2 = 217
                        L11_2 = 54
                        L7_2(L8_2, L9_2, L10_2, L11_2)
                      end
                    end
                  end
                end
              end
          end
          elseif 10 <= L5_2 then
            L7_2 = L3_2[A0_2]
            L7_2[1] = 0
            L6_2 = true
            L5_2 = 0
          else
            L7_2 = mSleep
            L8_2 = 77
            L7_2(L8_2)
            L5_2 = L5_2 + 1
          end
        end
    end
    else
      L7_2 = os
      L7_2 = L7_2.time
      L7_2 = L7_2()
      L8_2 = _ENV["卡点时间"]
      L7_2 = L7_2 - L8_2
      if 300 < L7_2 then
        L7_2 = _ENV["卡点处理"]
        return L7_2()
      else
        L7_2 = os
        L7_2 = L7_2.time
        L7_2 = L7_2()
        L8_2 = _ENV["卡点时间"]
        L7_2 = L7_2 - L8_2
        if 50 < L7_2 then
          L7_2 = _ENV["通用功能"]
          L7_2 = L7_2["关闭"]
          L7_2()
          L7_2 = _ENV["长安传送NPC"]
          L8_2 = A0_2
          L9_2 = L2_2
          return L7_2(L8_2, L9_2)
        else
          L7_2 = getColour
          L8_2 = colorList
          L9_2 = "任务点击下"
          L7_2 = L7_2(L8_2, L9_2)
          if not L7_2 then
            L7_2 = getColour
            L8_2 = colorList
            L9_2 = "关闭对话框"
            L7_2 = L7_2(L8_2, L9_2)
            if L7_2 then
              L7_2 = 1
              L8_2 = 30
              L9_2 = 1
              for L10_2 = L7_2, L8_2, L9_2 do
                L11_2 = getColour
                L12_2 = colorList
                L13_2 = "任务点击下"
                L11_2 = L11_2(L12_2, L13_2)
                if L11_2 then
                  break
                end
                if L10_2 == 30 then
                  L11_2 = randomClick
                  L12_2 = 2
                  L13_2 = 200
                  L14_2 = 1848
                  L15_2 = 71
                  L11_2(L12_2, L13_2, L14_2, L15_2)
                end
                L11_2 = mSleep
                L12_2 = 50
                L11_2(L12_2)
              end
          end
          else
            L7_2 = getColour
            L8_2 = colorList
            L9_2 = "任务点击下"
            L7_2 = L7_2(L8_2, L9_2)
            if L7_2 then
              L7_2 = randomClick
              L8_2 = 0
              L9_2 = 100
              L10_2 = 1531
              L11_2 = 500
              L12_2 = 1693
              L13_2 = 554
              L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
              L7_2 = _ENV["判断收缩充值"]
              L7_2()
              L7_2 = _cmp_tb_cx
              L8_2 = Color
              L8_2 = L8_2["地图"]
              L8_2 = L8_2["到达长安城"]
              L9_2 = {}
              L10_2 = 50
              L11_2 = 100
              L9_2[1] = L10_2
              L9_2[2] = L11_2
              L7_2 = L7_2(L8_2, L9_2)
              if L7_2 then
                break
              end
            else
              L7_2 = getWordStock
              L8_2 = WordStock
              L9_2 = "长安城"
              L10_2 = 154
              L11_2 = 37
              L12_2 = 308
              L13_2 = 78
              L7_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
              if L7_2 then
                break
              end
              L7_2 = getNewYearView
              L8_2 = "长安城"
              L7_2 = L7_2(L8_2)
              if L7_2 then
                break
              end
              L7_2 = getColour
              L8_2 = SmMapData
              L8_2 = L8_2[A0_2]
              L9_2 = 1
              L7_2 = L7_2(L8_2, L9_2)
              if L7_2 then
                L7_2 = randomClick
                L8_2 = 2
                L9_2 = 1300
                L10_2 = SmMapData
                L10_2 = L10_2[A0_2]
                L10_2 = L10_2[6]
                L11_2 = SmMapData
                L11_2 = L11_2[A0_2]
                L11_2 = L11_2[7]
                L7_2(L8_2, L9_2, L10_2, L11_2)
              else
                L7_2 = {}
                L8_2 = "00c00e00e01c00c00e01c01c00f07e03f01fffffffffeffffffffcffffffffc03c0f000001c0f000000c0e00000080c000000003000600043c00f180e3c00f3c0f3c70e3e0f3ef0e3fdf3ff8c3fff3ffcc3fff3f3fc3c0ffc1fc3c0ffc0fc7c0ffc0f8fc0ffc0f0fc0f3c0f0fc0f3c0f03c0f3c1f03c3f3c7f83eff3ef383fff3ff3c3f8f3f01c3e0f3e00c3c0f3c00e3c0f3c00f000e3c0060004380000000000000000000000000000000000000000000000000001f0000601ff8000f03ffc000f03c3c000f0383c000f0303c000f0303c00cf0303c00cf0303c00cf0303c00cf0303c00ef0303c00ef0303c00ef0303c00ef0303c00ef0303c00ef0303c03cf0703c07c7ff03fffc7ff01ffc03ff00ff800000000000000000000000000000000000000000000000000000000000000000000000000000000007fffffffeffffffffefffffffff$接引$1000$36$73"
                L9_2 = "0001e401ffe07ffe0787e0703e0703e0603e0603e0603e0603e0603e0603e0603e0603e0603e0603e0603e0e03ffe03ffe037fe01000000000000000000000000000000000000000000000fffffffffffffff$接引$236$20$33"
                L7_2[1] = L8_2
                L7_2[2] = L9_2
                L8_2 = addTSOcrDictEx
                L9_2 = L7_2
                L8_2 = L8_2(L9_2)
                L9_2 = tsFindText
                L10_2 = L8_2
                L11_2 = "接引"
                L12_2 = 355
                L13_2 = 342
                L14_2 = 1777
                L15_2 = 889
                L16_2 = "E8EDF0 , 2B2723"
                L17_2 = 83
                L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                if 0 < L9_2 then
                  L11_2 = randomClick
                  L12_2 = 2
                  L13_2 = 300
                  L14_2 = L9_2 + 100
                  L15_2 = L10_2 + 35
                  L16_2 = 20
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
                else
                  L11_2 = getColour
                  L12_2 = colorList
                  L13_2 = "任务点击下"
                  L11_2 = L11_2(L12_2, L13_2)
                  if not L11_2 then
                    L11_2 = getColour
                    L12_2 = colorList
                    L13_2 = "关闭对话框"
                    L11_2 = L11_2(L12_2, L13_2)
                    if not L11_2 then
                      L11_2 = _ENV["当前门派"]
                      L12_2 = A0_2
                      L11_2 = L11_2(L12_2)
                      if not L11_2 then
                        L11_2 = getWordStock
                        L12_2 = WordStock
                        L13_2 = "长安城"
                        L14_2 = 154
                        L15_2 = 37
                        L16_2 = 308
                        L17_2 = 78
                        L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                        if L11_2 then
                          -- 继续执行后续逻辑
                        else
                          -- 替换 goto lbl_887，直接执行对应逻辑
                          L11_2 = _ENV["长安传送NPC"]
                          L12_2 = A0_2
                          L13_2 = L2_2
                          return L11_2(L12_2, L13_2)
                        end
                      end
                    end
                  end
                  L11_2 = getColour
                  L12_2 = colorList
                  L13_2 = "任务点击下"
                  L11_2 = L11_2(L12_2, L13_2)
                  if not L11_2 then
                    L11_2 = getColour
                    L12_2 = colorList
                    L13_2 = "关闭对话框"
                    L11_2 = L11_2(L12_2, L13_2)
                    if not L11_2 then
                      L11_2 = _ENV["当前门派"]
                      L12_2 = A0_2
                      L11_2 = L11_2(L12_2)
                      if not L11_2 then
                        L11_2 = getNewYearView
                        L12_2 = "长安城"
                        L11_2 = L11_2(L12_2)
                        if not L11_2 then
                          -- 移除 lbl_887 标签，这部分逻辑已经在前面处理过了
                          L11_2 = _ENV["长安传送NPC"]
                          L12_2 = A0_2
                          L13_2 = L2_2
                          return L11_2(L12_2, L13_2)
                        end
                    end
                  end
                  else
                    L11_2 = _ENV["押镖屏蔽"]
                    L11_2()
                    L11_2 = printLog
                    L12_2 = "寻找门派接引："
                    L13_2 = L5_2
                    L12_2 = L12_2 .. L13_2
                    L11_2(L12_2)
                    if 3 <= L5_2 then
                      L4_2 = true
                      L11_2 = L3_2[A0_2]
                      L11_2[1] = 0
                      L6_2 = true
                      L5_2 = 0
                    else
                      L5_2 = L5_2 + 1
                      L11_2 = mSleep
                      L12_2 = 77
                      L11_2(L12_2)
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
    -- 移除 lbl_912 标签
  end
end

_ENV["长安传送NPC"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  while true do
    L3_2 = getWordStock
    L4_2 = WordStock
    L5_2 = A0_2
    L6_2 = 154
    L7_2 = 37
    L8_2 = 308
    L9_2 = 78
    L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
    if not L3_2 then
      L3_2 = getNewYearView
      L4_2 = A0_2
      L3_2 = L3_2(L4_2)
      if L3_2 then
        L3_2 = true
        return L3_2
      else
        -- 替换 goto lbl_19，直接执行对应逻辑
        L3_2 = getColour
        L4_2 = colorList
        L5_2 = "飞行符界面"
        L3_2 = L3_2(L4_2, L5_2)
        if L3_2 then
          L3_2 = randomClick
          L4_2 = 2
          L5_2 = 200
          L6_2 = 1848
          L7_2 = 71
          L3_2(L4_2, L5_2, L6_2, L7_2)
        end
        L3_2 = false
        return L3_2
      end
    end
    -- 移除 goto lbl_44 和 lbl_19 标签，这些逻辑已经在前面处理过了
    L3_2 = getColour
    L4_2 = colorList
    L5_2 = "飞行符界面"
    L3_2 = L3_2(L4_2, L5_2)
    if L3_2 then
      L3_2 = printLog
      L4_2 = "前往坐标地点"
      L3_2(L4_2)
      L3_2 = randomTap
      L4_2 = A1_2
      L5_2 = A2_2
      L6_2 = 10
      L3_2(L4_2, L5_2, L6_2)
      L3_2 = mSleep
      L4_2 = math
      L4_2 = L4_2.random
      L5_2 = 1000
      L6_2 = 2000
      L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L4_2(L5_2, L6_2)
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
    else
      L3_2 = mSleep
      L4_2 = 100
      L3_2(L4_2)
    end
    ::lbl_44::
  end
end

_ENV["到达地图位置"] = L0_1
