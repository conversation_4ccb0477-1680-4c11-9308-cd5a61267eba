local L0_1, L1_1, L2_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = getAppInfo
  L1_2 = "com.netease.mhxyhtb"
  L0_2 = L0_2(L1_2)
  L1_2 = L0_2.versionName
  if L1_2 ~= nil then
    L2_2 = strSplit
    L3_2 = L1_2
    L4_2 = "."
    L2_2 = L2_2(L3_2, L4_2)
    L3_2 = tonumber
    L4_2 = L2_2[1]
    L5_2 = L2_2[2]
    L6_2 = L2_2[3]
    L4_2 = L4_2 .. L5_2 .. L6_2
    L3_2 = L3_2(L4_2)
    if L3_2 < 1170 then
      L4_2 = dialog
      L5_2 = "请将游戏升级到 1.17.0 以上版本"
      L4_2(L5_2)
      L4_2 = luaExit
      L4_2()
    end
  else
    L2_2 = dialog
    L3_2 = "无法获取游戏版本号..."
    L2_2(L3_2)
    L2_2 = luaExit
    L2_2()
  end
end

getGameVersion = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2
  L1_2 = ""
  L2_2 = {}
  L3_2 = "q"
  L4_2 = "w"
  L5_2 = "e"
  L6_2 = "r"
  L7_2 = "t"
  L8_2 = "y"
  L9_2 = "u"
  L10_2 = "i"
  L11_2 = "o"
  L12_2 = "p"
  L13_2 = "a"
  L14_2 = "s"
  L15_2 = "d"
  L16_2 = "f"
  L17_2 = "g"
  L18_2 = "h"
  L19_2 = "j"
  L20_2 = "k"
  L21_2 = "l"
  L22_2 = "z"
  L23_2 = "x"
  L24_2 = "c"
  L25_2 = "v"
  L26_2 = "b"
  L27_2 = "n"
  L28_2 = "m"
  L29_2 = "Q"
  L30_2 = "W"
  L31_2 = "E"
  L32_2 = "R"
  L33_2 = "T"
  L34_2 = "Y"
  L35_2 = "U"
  L36_2 = "I"
  L37_2 = "O"
  L38_2 = "P"
  L39_2 = "A"
  L40_2 = "S"
  L41_2 = "D"
  L42_2 = "F"
  L43_2 = "G"
  L44_2 = "H"
  L45_2 = "J"
  L46_2 = "K"
  L47_2 = "L"
  L48_2 = "Z"
  L49_2 = "X"
  L50_2 = "C"
  L51_2 = "V"
  L52_2 = "B"
  L2_2[1] = L3_2
  L2_2[2] = L4_2
  L2_2[3] = L5_2
  L2_2[4] = L6_2
  L2_2[5] = L7_2
  L2_2[6] = L8_2
  L2_2[7] = L9_2
  L2_2[8] = L10_2
  L2_2[9] = L11_2
  L2_2[10] = L12_2
  L2_2[11] = L13_2
  L2_2[12] = L14_2
  L2_2[13] = L15_2
  L2_2[14] = L16_2
  L2_2[15] = L17_2
  L2_2[16] = L18_2
  L2_2[17] = L19_2
  L2_2[18] = L20_2
  L2_2[19] = L21_2
  L2_2[20] = L22_2
  L2_2[21] = L23_2
  L2_2[22] = L24_2
  L2_2[23] = L25_2
  L2_2[24] = L26_2
  L2_2[25] = L27_2
  L2_2[26] = L28_2
  L2_2[27] = L29_2
  L2_2[28] = L30_2
  L2_2[29] = L31_2
  L2_2[30] = L32_2
  L2_2[31] = L33_2
  L2_2[32] = L34_2
  L2_2[33] = L35_2
  L2_2[34] = L36_2
  L2_2[35] = L37_2
  L2_2[36] = L38_2
  L2_2[37] = L39_2
  L2_2[38] = L40_2
  L2_2[39] = L41_2
  L2_2[40] = L42_2
  L2_2[41] = L43_2
  L2_2[42] = L44_2
  L2_2[43] = L45_2
  L2_2[44] = L46_2
  L2_2[45] = L47_2
  L2_2[46] = L48_2
  L2_2[47] = L49_2
  L2_2[48] = L50_2
  L2_2[49] = L51_2
  L2_2[50] = L52_2
  L3_2 = "N"
  L4_2 = "M"
  L5_2 = "~"
  L6_2 = "@"
  L7_2 = "#"
  L8_2 = "$"
  L9_2 = "%"
  L10_2 = "^"
  L11_2 = "&"
  L12_2 = "*"
  L13_2 = "("
  L14_2 = ")"
  L15_2 = "{"
  L16_2 = "}"
  L17_2 = "["
  L18_2 = "]"
  L19_2 = ":"
  L20_2 = ";"
  L21_2 = "'"
  L22_2 = "\""
  L23_2 = "|"
  L24_2 = ""
  L25_2 = ","
  L26_2 = "<"
  L27_2 = ">"
  L28_2 = "."
  L29_2 = "/"
  L30_2 = "?"
  L31_2 = "_"
  L32_2 = "-"
  L33_2 = "="
  L34_2 = "+"
  L35_2 = "1"
  L36_2 = "2"
  L37_2 = "3"
  L38_2 = "4"
  L39_2 = "5"
  L40_2 = "6"
  L41_2 = "7"
  L42_2 = "8"
  L43_2 = "9"
  L44_2 = "0"
  L2_2[51] = L3_2
  L2_2[52] = L4_2
  L2_2[53] = L5_2
  L2_2[54] = L6_2
  L2_2[55] = L7_2
  L2_2[56] = L8_2
  L2_2[57] = L9_2
  L2_2[58] = L10_2
  L2_2[59] = L11_2
  L2_2[60] = L12_2
  L2_2[61] = L13_2
  L2_2[62] = L14_2
  L2_2[63] = L15_2
  L2_2[64] = L16_2
  L2_2[65] = L17_2
  L2_2[66] = L18_2
  L2_2[67] = L19_2
  L2_2[68] = L20_2
  L2_2[69] = L21_2
  L2_2[70] = L22_2
  L2_2[71] = L23_2
  L2_2[72] = L24_2
  L2_2[73] = L25_2
  L2_2[74] = L26_2
  L2_2[75] = L27_2
  L2_2[76] = L28_2
  L2_2[77] = L29_2
  L2_2[78] = L30_2
  L2_2[79] = L31_2
  L2_2[80] = L32_2
  L2_2[81] = L33_2
  L2_2[82] = L34_2
  L2_2[83] = L35_2
  L2_2[84] = L36_2
  L2_2[85] = L37_2
  L2_2[86] = L38_2
  L2_2[87] = L39_2
  L2_2[88] = L40_2
  L2_2[89] = L41_2
  L2_2[90] = L42_2
  L2_2[91] = L43_2
  L2_2[92] = L44_2
  L3_2 = 1
  L4_2 = #A0_2
  L5_2 = 1
  for L6_2 = L3_2, L4_2, L5_2 do
    L7_2 = 1
    L8_2 = #L2_2
    L9_2 = 1
    for L10_2 = L7_2, L8_2, L9_2 do
      L11_2 = L2_2[L10_2]
      L12_2 = string
      L12_2 = L12_2.sub
      L13_2 = A0_2
      L14_2 = L6_2
      L15_2 = L6_2
      L12_2 = L12_2(L13_2, L14_2, L15_2)
      if L11_2 == L12_2 then
        L11_2 = L1_2
        L12_2 = L2_2[L10_2]
        L1_2 = L11_2 .. L12_2
        break
      end
    end
  end
  return L1_2
end

_ENV["匹配字符"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  while true do
    L0_2 = runApp
    L1_2 = "com.sollyu.xposed.hook.model"
    L0_2 = L0_2(L1_2)
    if L0_2 then
      L0_2 = _print
      L1_2 = "千机变启动成功！"
      L0_2(L1_2)
      L0_2 = mSleep
      L1_2 = 5000
      L0_2(L1_2)
    end
    L0_2 = os
    L0_2 = L0_2.time
    L0_2 = L0_2()
    while true do
      L1_2 = init
      L2_2 = 0
      L1_2(L2_2)
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["红尘"]
      L2_2 = L2_2["千机变梦幻西游"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = _ENV["_随机延时"]
        L2_2 = 811
        L1_2(L2_2)
      end
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["进入游戏"]
      L2_2 = L2_2["千机变加号2"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = _Sleep
        L2_2 = 1100
        L3_2 = 2000
        L1_2(L2_2, L3_2)
      end
      L1_2 = _find
      L2_2 = Color
      L2_2 = L2_2["红尘"]
      L2_2 = L2_2["千机变运行"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        L1_2 = _ENV["_随机延时"]
        L2_2 = 811
        L1_2(L2_2)
      else
        L1_2 = _find
        L2_2 = Color
        L2_2 = L2_2["红尘"]
        L2_2 = L2_2["千机变运行二"]
        L1_2 = L1_2(L2_2)
        if L1_2 then
          L1_2 = _ENV["_随机延时"]
          L2_2 = 811
          L1_2(L2_2)
        end
      end
      L1_2 = os
      L1_2 = L1_2.time
      L1_2 = L1_2()
      L1_2 = L1_2 - L0_2
      if 180 < L1_2 then
        L1_2 = closeApp
        L2_2 = "com.netease.mhxyhtb"
        L1_2(L2_2)
        L1_2 = mSleep
        L2_2 = 2000
        L1_2(L2_2)
        L1_2 = os
        L1_2 = L1_2.execute
        L2_2 = "input keyevent KEYCODE_HOME"
        L1_2(L2_2)
        L1_2 = mSleep
        L2_2 = 2000
        L1_2(L2_2)
      end
      L1_2 = _cmp
      L2_2 = Color
      L2_2 = L2_2["进入游戏"]
      L2_2 = L2_2["千机变后登录界面"]
      L1_2 = L1_2(L2_2)
      if L1_2 then
        break
      end
    end
  end
end

_ENV["千机变进入"] = L0_1

function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2
  L1_2 = init
  L2_2 = 0
  L1_2(L2_2)
  L1_2 = writeFileString
  L2_2 = userPath
  L2_2 = L2_2()
  L3_2 = "/res/UI.txt"
  L2_2 = L2_2 .. L3_2
  L3_2 = "0"
  L1_2(L2_2, L3_2)
  while true do
    L1_2 = appIsRunning
    L2_2 = "com.netease.mhxyhtb"
    L1_2 = L1_2(L2_2)
    if L1_2 ~= 0 then
      L2_2 = gameStart
      if L2_2 then
        -- 替换 goto lbl_1194，直接跳转到对应逻辑
        L2_2 = _ENV["UI_当前循环"]
        if L2_2 == "从当前循环" then
          gameStart = false
          _ENV["从当前"] = true
        else
          L2_2 = _ENV["循环上号"]
          if L2_2 == "普通循环" then
            L2_2 = _ENV["UI_当前循环"]
            if L2_2 == "从当前循环" then
              L2_2 = _ENV["从当前"]
              if L2_2 == true then
                L2_2 = init
                L3_2 = 0
                L2_2(L3_2)
                _ENV["从当前"] = false
                L2_2 = true
                L3_2 = os
                L3_2 = L3_2.time
                L3_2 = L3_2()
                while true do
                  L4_2 = os
                  L4_2 = L4_2.time
                  L4_2 = L4_2()
                  L4_2 = L4_2 - L3_2
                  if 120 < L4_2 then
                    L4_2 = _ENV["UI_千机变进入"]
                    if L4_2 == "千机变进入" then
                      L4_2 = _ENV["千机变进入"]
                      L4_2()
                    else
                      L4_2 = _ENV["UI_千机变进入"]
                      if L4_2 == "虚拟机开游戏" then
                        L4_2 = dialog
                        L5_2 = "手动打开虚拟机梦幻登录界面"
                        L4_2(L5_2)
                        L4_2 = mSleep
                        L5_2 = 5555
                        L4_2(L5_2)
                      else
                        L4_2 = runApp
                        L5_2 = "com.netease.mhxyhtb"
                        L4_2(L5_2)
                      end
                    end
                    L4_2 = os
                    L4_2 = L4_2.time
                    L4_2 = L4_2()
                    L3_2 = L4_2
                  end
                  L4_2 = _find
                  L5_2 = Color
                  L5_2 = L5_2["抓鬼"]
                  L5_2 = L5_2["出现删除角色"]
                  L4_2 = L4_2(L5_2)
                  if L4_2 then
                    L4_2 = randomClick
                    L5_2 = 2
                    L6_2 = 1500
                    L7_2 = 925
                    L8_2 = 1229
                    L9_2 = 10
                    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
                  end
                  L4_2 = getColour
                  L5_2 = colorList
                  L6_2 = "删除角色"
                  L4_2 = L4_2(L5_2, L6_2)
                  if L4_2 then
                    L4_2 = printLog
                    L5_2 = "关闭删除角色"
                    L4_2(L5_2)
                    L2_2 = true
                    L4_2 = randomClick
                    L5_2 = 2
                    L6_2 = 1500
                    L7_2 = 925
                    L8_2 = 1229
                    L9_2 = 10
                    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
                  else
                    L4_2 = _find
                    L5_2 = Color
                    L5_2 = L5_2["起号"]
                    L5_2 = L5_2["在游戏中"]
                    L4_2 = L4_2(L5_2)
                    if not L4_2 then
                      L4_2 = _find
                      L5_2 = Color
                      L5_2 = L5_2["起号"]
                      L5_2 = L5_2["新春在游戏中"]
                      L4_2 = L4_2(L5_2)
                      if not L4_2 then
                        -- 继续其他逻辑...
                        break
                      end
                    end
                    L4_2 = _ENV["进入互通版"]
                    L5_2 = A0_2
                    L6_2 = true
                    return L4_2(L5_2, L6_2)
                  end
                end
              end
            end
          else
            L2_2 = dialog
            L3_2 = "请选择循环上号模式"
            L2_2(L3_2)
            return
          end
        end
        -- 继续到主循环结束
        break
      end
    end
    L2_2 = gameStart
    if L2_2 or L1_2 == 0 then
      L2_2 = _ENV["UI_千机变进入"]
      if L2_2 == "千机变进入" then
        L2_2 = _ENV["千机变进入"]
        L2_2()
      else
        L2_2 = _ENV["UI_千机变进入"]
        if L2_2 == "虚拟机开游戏" then
        else
          L2_2 = runApp
          L3_2 = "com.netease.mhxyhtb"
          L2_2(L3_2)
        end
      end
      L2_2 = printLog
      L3_2 = "游戏登录"
      L2_2(L3_2)
      L2_2 = mSleep
      L3_2 = math
      L3_2 = L3_2.random
      L4_2 = 3000
      L5_2 = 5000
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L3_2(L4_2, L5_2)
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
    end
    L2_2 = true
    L3_2 = os
    L3_2 = L3_2.time
    L3_2 = L3_2()
    while true do
      L4_2 = getColour
      L5_2 = GameFB
      L6_2 = "新登录界面"
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = getColour
        L5_2 = GameFB
        L6_2 = "显示角色"
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          -- 替换 goto lbl_75，直接执行对应逻辑
          gameStart = false
          L4_2 = printLog
          L5_2 = "等待登录界面.."
          L4_2(L5_2)
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 3000
          L7_2 = 5000
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
        end
      end
      L4_2 = getColour
      L5_2 = GameFB
      L6_2 = "新登录界面"
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 then
        L4_2 = getColour
        L5_2 = GameFB
        L6_2 = "添加角色"
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 then
          -- 移除标签，直接执行逻辑
          gameStart = false
          L4_2 = printLog
          L5_2 = "等待登录界面.."
          L4_2(L5_2)
          L4_2 = mSleep
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = 3000
          L7_2 = 5000
          L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L5_2(L6_2, L7_2)
          L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
          L4_2 = _ENV["循环上号"]
          if L4_2 == "文本循环" then
            L4_2 = true
            L5_2 = true
            L6_2 = strSplit
            L7_2 = A0_2
            L8_2 = "----"
            L6_2 = L6_2(L7_2, L8_2)
            L7_2 = string
            L7_2 = L7_2.gsub
            L8_2 = L6_2[1]
            L9_2 = "^%s*(.-)%s*$"
            L10_2 = "%1"
            L7_2 = L7_2(L8_2, L9_2, L10_2)
            L8_2 = string
            L8_2 = L8_2.gsub
            L9_2 = L6_2[2]
            L10_2 = "^%s*(.-)%s*$"
            L11_2 = "%1"
            L8_2 = L8_2(L9_2, L10_2, L11_2)
            L9_2 = string
            L9_2 = L9_2.gsub
            L10_2 = L6_2[3]
            L11_2 = "^%s*(.-)%s*$"
            L12_2 = "%1"
            L9_2 = L9_2(L10_2, L11_2, L12_2)
            L10_2 = string
            L10_2 = L10_2.gsub
            L11_2 = L6_2[4]
            L12_2 = "^%s*(.-)%s*$"
            L13_2 = "%1"
            L10_2 = L10_2(L11_2, L12_2, L13_2)
            L11_2 = string
            L11_2 = L11_2.gsub
            L12_2 = L6_2[5]
            L13_2 = "^%s*(.-)%s*$"
            L14_2 = "%1"
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            _ENV["门派选择"] = L11_2
            L11_2 = string
            L11_2 = L11_2.gsub
            L12_2 = L6_2[6]
            L13_2 = "^%s*(.-)%s*$"
            L14_2 = "%1"
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            _ENV["人物攻击"] = L11_2
            L11_2 = string
            L11_2 = L11_2.gsub
            L12_2 = L6_2[7]
            L13_2 = "^%s*(.-)%s*$"
            L14_2 = "%1"
            L11_2 = L11_2(L12_2, L13_2, L14_2)
            _ENV["宠物攻击"] = L11_2
            while true do
              L11_2 = getColour
              L12_2 = GameFB
              L13_2 = "添加角色"
              L11_2 = L11_2(L12_2, L13_2)
              if L11_2 then
                L11_2 = randomClick
                L12_2 = 0
                L13_2 = 800
                L14_2 = 423
                L15_2 = 1144
                L16_2 = 510
                L17_2 = 1237
                L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
              else
                L11_2 = getColour
                L12_2 = GameFB
                L13_2 = "登录按钮"
                L11_2 = L11_2(L12_2, L13_2)
                if L11_2 then
                  L11_2 = randomClick
                  L12_2 = 0
                  L13_2 = 800
                  L14_2 = 664
                  L15_2 = 1165
                  L16_2 = 708
                  L17_2 = 1219
                  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                else
                  if L4_2 then
                    L11_2 = getColour
                    L12_2 = GameFB
                    L13_2 = "输入账号"
                    L11_2 = L11_2(L12_2, L13_2)
                    if L11_2 then
                      L4_2 = false
                      L11_2 = randomClick
                      L12_2 = 1
                      L13_2 = 800
                      L14_2 = 791
                      L15_2 = 1207
                      L11_2(L12_2, L13_2, L14_2, L15_2)
                      L11_2 = inputText
                      L12_2 = _ENV["匹配字符"]
                      L13_2 = L7_2
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L12_2(L13_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 500
                      L14_2 = 1000
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                      L11_2 = randomClick
                      L12_2 = 0
                      L13_2 = 800
                      L14_2 = 631
                      L15_2 = 792
                      L16_2 = 670
                      L17_2 = 1039
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                  end
                  else
                    L11_2 = getColour
                    L12_2 = GameFB
                    L13_2 = "输入密码"
                    L11_2 = L11_2(L12_2, L13_2)
                    if L11_2 then
                      L11_2 = inputText
                      L12_2 = _ENV["匹配字符"]
                      L13_2 = L8_2
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L12_2(L13_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                      L11_2 = mSleep
                      L12_2 = math
                      L12_2 = L12_2.random
                      L13_2 = 500
                      L14_2 = 1000
                      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L12_2(L13_2, L14_2)
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                      L11_2 = randomClick
                      L12_2 = 0
                      L13_2 = 800
                      L14_2 = 605
                      L15_2 = 826
                      L16_2 = 656
                      L17_2 = 1120
                      L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
                      L11_2 = os
                      L11_2 = L11_2.time
                      L11_2 = L11_2()
                      L12_2 = false
                      while true do
                        L13_2 = os
                        L13_2 = L13_2.time
                        L13_2 = L13_2()
                        L13_2 = L13_2 - L11_2
                        if 10 < L13_2 then
                          L13_2 = getColour
                          L14_2 = GameFB
                          L15_2 = "新确定登录"
                          L13_2 = L13_2(L14_2, L15_2)
                          if L13_2 then
                            L13_2 = os
                            L13_2 = L13_2.time
                            L13_2 = L13_2()
                            L13_2 = L13_2 - L11_2
                            if L13_2 < 60 then
                              L13_2 = printLog
                              L14_2 = "连接超时，等待重启游戏"
                              L13_2(L14_2)
                              L13_2 = randomClick
                              L14_2 = 0
                              L15_2 = 2000
                              L16_2 = 368
                              L17_2 = 1048
                              L18_2 = 432
                              L19_2 = 1310
                              L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
                            else
                              L13_2 = printLog
                              L14_2 = "连接超时，重启游戏"
                              L13_2(L14_2)
                              gameStart = true
                              L13_2 = GameLogin
                              L14_2 = A0_2
                              return L13_2(L14_2)
                            end
                        end
                        else
                          L13_2 = os
                          L13_2 = L13_2.time
                          L13_2 = L13_2()
                          L13_2 = L13_2 - L11_2
                          if 10 < L13_2 and L12_2 == false then
                            L13_2 = getColour
                            L14_2 = GameFB
                            L15_2 = "登录按钮I"
                            L13_2 = L13_2(L14_2, L15_2)
                            if L13_2 then
                              L13_2 = randomClick
                              L14_2 = 0
                              L15_2 = 800
                              L16_2 = 392
                              L17_2 = 728
                              L18_2 = 444
                              L19_2 = 1117
                              L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
                              L12_2 = true
                          end
                          else
                            if L12_2 then
                              L13_2 = os
                              L13_2 = L13_2.time
                              L13_2 = L13_2()
                              L13_2 = L13_2 - L11_2
                              if 15 < L13_2 then
                                L13_2 = 1
                                L14_2 = 10
                                L15_2 = 1
                                for L16_2 = L13_2, L14_2, L15_2 do
                                  L17_2 = getColour
                                  L18_2 = GameFB
                                  L19_2 = "登录按钮I"
                                  L17_2 = L17_2(L18_2, L19_2)
                                  if L17_2 then
                                    L17_2 = randomClick
                                    L18_2 = 2
                                    L19_2 = 800
                                    L20_2 = 829
                                    L21_2 = 1462
                                    L17_2(L18_2, L19_2, L20_2, L21_2)
                                  else
                                    break
                                  end
                                end
                                L4_2 = true
                                -- 替换 goto lbl_391，直接跳出循环
                                break
                            end
                            else
                              L13_2 = getColour
                              L14_2 = GameFB
                              L15_2 = "连接失败I"
                              L13_2 = L13_2(L14_2, L15_2)
                              if L13_2 then
                                L13_2 = getColour
                                L14_2 = GameFB
                                L15_2 = "重试链接I"
                                L13_2 = L13_2(L14_2, L15_2)
                                if L13_2 then
                                  L13_2 = printLog
                                  L14_2 = "连接失败，重试链接"
                                  L13_2(L14_2)
                                  L13_2 = randomClick
                                  L14_2 = 0
                                  L15_2 = 1000
                                  L16_2 = 264
                                  L17_2 = 1132
                                  L18_2 = 323
                                  L19_2 = 1379
                                  L13_2(L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
                              end
                              else
                                L13_2 = getColour
                                L14_2 = GameFB
                                L15_2 = "选择大区"
                                L13_2 = L13_2(L14_2, L15_2)
                                if L13_2 or
                                   (function()
                                     L13_2 = getColour
                                     L14_2 = GameFB
                                     L15_2 = "服务器选择"
                                     return L13_2(L14_2, L15_2)
                                   end)() or
                                   (function()
                                     L13_2 = getColour
                                     L14_2 = GameFB
                                     L15_2 = "服务器选择I"
                                     return L13_2(L14_2, L15_2)
                                   end)() then
                                  L13_2 = printLog
                                  L14_2 = "服务器选择："
                                  L15_2 = L9_2
                                  L14_2 = L14_2 .. L15_2
                                  L13_2(L14_2)
                                  L13_2 = _ENV["查找角色位置"]
                                  L14_2 = L9_2
                                  L15_2 = tonumber
                                  L16_2 = L10_2
                                  L15_2 = L15_2(L16_2)
                                  L16_2 = A0_2
                                  return L13_2(L14_2, L15_2, L16_2)
                                end
                              end
                            end
                          end
                        end
                        -- 移除 lbl_384 标签
                      end
                    elseif L5_2 then
                      L5_2 = false
                      L11_2 = _ENV["工作室模式"]
                      L11_2()
                    end
                  end
                end
              end
              -- 移除 lbl_391 标签
            end
          else
            L4_2 = _ENV["循环上号"]
            if L4_2 == "普通循环" then
              L4_2 = init
              L5_2 = 0
              L4_2(L5_2)
              L4_2 = true
              L5_2 = os
              L5_2 = L5_2.time
              L5_2 = L5_2()
              while true do
                L6_2 = os
                L6_2 = L6_2.time
                L6_2 = L6_2()
                L6_2 = L6_2 - L5_2
                if 120 < L6_2 then
                  L6_2 = _ENV["UI_千机变进入"]
                  if L6_2 == "千机变进入" then
                    L6_2 = _ENV["千机变进入"]
                    L6_2()
                  else
                    L6_2 = _ENV["UI_千机变进入"]
                    if L6_2 == "虚拟机开游戏" then
                      L6_2 = dialog
                      L7_2 = "手动打开虚拟机梦幻登录界面"
                      L6_2(L7_2)
                      L6_2 = mSleep
                      L7_2 = 5555
                      L6_2(L7_2)
                    else
                      L6_2 = runApp
                      L7_2 = "com.netease.mhxyhtb"
                      L6_2(L7_2)
                    end
                  end
                  L6_2 = os
                  L6_2 = L6_2.time
                  L6_2 = L6_2()
                  L5_2 = L6_2
                end
                L6_2 = _find
                L7_2 = Color
                L7_2 = L7_2["抓鬼"]
                L7_2 = L7_2["出现删除角色"]
                L6_2 = L6_2(L7_2)
                if L6_2 then
                  L6_2 = randomClick
                  L7_2 = 2
                  L8_2 = 1500
                  L9_2 = 925
                  L10_2 = 1229
                  L11_2 = 10
                  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                end
                L6_2 = getColour
                L7_2 = colorList
                L8_2 = "删除角色"
                L6_2 = L6_2(L7_2, L8_2)
                if L6_2 then
                  L6_2 = printLog
                  L7_2 = "关闭删除角色"
                  L6_2(L7_2)
                  L2_2 = true
                  L6_2 = randomClick
                  L7_2 = 2
                  L8_2 = 1500
                  L9_2 = 925
                  L10_2 = 1229
                  L11_2 = 10
                  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                else
                  L6_2 = getColour
                  L7_2 = GameFB
                  L8_2 = "新选择角色"
                  L6_2 = L6_2(L7_2, L8_2)
                  if L6_2 then
                    if L2_2 then
                      L6_2 = mSleep
                      L7_2 = math
                      L7_2 = L7_2.random
                      L8_2 = 2000
                      L9_2 = 2500
                      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L7_2(L8_2, L9_2)
                      L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                    end
                    if L2_2 then
                      L6_2 = tonumber
                      L7_2 = _ENV["角色数量"]
                      L6_2 = L6_2(L7_2)
                      if 3 < L6_2 then
                        L2_2 = false
                        L6_2 = printLog
                        L7_2 = "滑动最低部选择角色"
                        L6_2(L7_2)
                        L6_2 = math
                        L6_2 = L6_2.random
                        L7_2 = 92
                        L8_2 = 124
                        L6_2 = L6_2(L7_2, L8_2)
                        L7_2 = math
                        L7_2 = L7_2.random
                        L8_2 = 706
                        L9_2 = 719
                        L7_2 = L7_2(L8_2, L9_2)
                        L8_2 = math
                        L8_2 = L8_2.random
                        L9_2 = 841
                        L10_2 = 865
                        L8_2 = L8_2(L9_2, L10_2)
                        L9_2 = math
                        L9_2 = L9_2.random
                        L10_2 = 706
                        L11_2 = 719
                        L9_2 = L9_2(L10_2, L11_2)
                        L10_2 = moveTo
                        L11_2 = L6_2
                        L12_2 = L7_2
                        L13_2 = L8_2
                        L14_2 = L9_2
                        L15_2 = {}
                        L15_2.step = 100
                        L15_2.ms = 10
                        L15_2.index = 1
                        L15_2.stop = true
                        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                        L10_2 = mSleep
                        L11_2 = math
                        L11_2 = L11_2.random
                        L12_2 = 800
                        L13_2 = 1500
                        L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L11_2(L12_2, L13_2)
                        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                    end
                    else
                      L6_2 = printLog
                      L7_2 = "选择角色"
                      L6_2(L7_2)
                      L6_2 = tonumber
                      L7_2 = _ENV["角色数量"]
                      L6_2 = L6_2(L7_2)
                      if L6_2 == 3 then
                        L6_2 = randomClick
                        L7_2 = 0
                        L8_2 = 2000
                        L9_2 = 624
                        L10_2 = 1440
                        L11_2 = 729
                        L12_2 = 1504
                        L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                      else
                        L6_2 = tonumber
                        L7_2 = _ENV["角色数量"]
                        L6_2 = L6_2(L7_2)
                        if L6_2 == 2 then
                          L6_2 = randomClick
                          L7_2 = 0
                          L8_2 = 2000
                          L9_2 = 628
                          L10_2 = 951
                          L11_2 = 720
                          L12_2 = 1015
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                        else
                          L6_2 = tonumber
                          L7_2 = _ENV["角色数量"]
                          L6_2 = L6_2(L7_2)
                          if L6_2 == 1 then
                            L6_2 = randomClick
                            L7_2 = 0
                            L8_2 = 2000
                            L9_2 = 619
                            L10_2 = 466
                            L11_2 = 737
                            L12_2 = 523
                            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                          else
                            L6_2 = snapshot
                            L7_2 = "key.png"
                            L8_2 = 400
                            L9_2 = 1220
                            L10_2 = 480
                            L11_2 = 1300
                            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                            L6_2 = getFileSize
                            L7_2 = userPath
                            L7_2 = L7_2()
                            L8_2 = "/res/key.png"
                            L7_2 = L7_2 .. L8_2
                            L6_2 = L6_2(L7_2)
                            L7_2 = getFileSize
                            L8_2 = userPath
                            L8_2 = L8_2()
                            L9_2 = "/res/KdLogin.png"
                            L8_2 = L8_2 .. L9_2
                            L7_2 = L7_2(L8_2)
                            if L6_2 ~= L7_2 then
                              L8_2 = randomClick
                              L9_2 = 0
                              L10_2 = 2000
                              L11_2 = 243
                              L12_2 = 1448
                              L13_2 = 394
                              L14_2 = 1520
                              L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                            else
                              L8_2 = snapshot
                              L9_2 = "key.png"
                              L10_2 = 400
                              L11_2 = 720
                              L12_2 = 480
                              L13_2 = 800
                              L8_2(L9_2, L10_2, L11_2, L12_2, L13_2)
                              L8_2 = getFileSize
                              L9_2 = userPath
                              L9_2 = L9_2()
                              L10_2 = "/res/key.png"
                              L9_2 = L9_2 .. L10_2
                              L8_2 = L8_2(L9_2)
                              L9_2 = getFileSize
                              L10_2 = userPath
                              L10_2 = L10_2()
                              L11_2 = "/res/KdLogin.png"
                              L10_2 = L10_2 .. L11_2
                              L9_2 = L9_2(L10_2)
                              if L8_2 ~= L9_2 then
                                L10_2 = randomClick
                                L11_2 = 0
                                L12_2 = 2000
                                L13_2 = 247
                                L14_2 = 968
                                L15_2 = 403
                                L16_2 = 1036
                                L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                              else
                                L10_2 = randomClick
                                L11_2 = 0
                                L12_2 = 2000
                                L13_2 = 242
                                L14_2 = 467
                                L15_2 = 402
                                L16_2 = 546
                                L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                              end
                            end
                          end
                        end
                      end
                    end
                  else
                    if not L4_2 then
                      L6_2 = getColour
                      L7_2 = GameFB
                      L8_2 = "新登录界面"
                      L6_2 = L6_2(L7_2, L8_2)
                      if L6_2 then
                        L6_2 = getColour
                        L7_2 = GameFB
                        L8_2 = "显示角色"
                        L6_2 = L6_2(L7_2, L8_2)
                        if L6_2 then
                          L6_2 = mSleep
                          L7_2 = math
                          L7_2 = L7_2.random
                          L8_2 = 800
                          L9_2 = 1500
                          L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L7_2(L8_2, L9_2)
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                          L6_2 = randomClick
                          L7_2 = 0
                          L8_2 = 2000
                          L9_2 = 187
                          L10_2 = 955
                          L11_2 = 239
                          L12_2 = 1112
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                          L6_2 = printLog
                          L7_2 = "进入游戏"
                          L6_2(L7_2)
                          L6_2 = _ENV["进入互通版"]
                          L7_2 = A0_2
                          L8_2 = true
                          return L6_2(L7_2, L8_2)
                      end
                    end
                    else
                      L6_2 = _cmp
                      L7_2 = Color
                      L7_2 = L7_2["进入游戏"]
                      L7_2 = L7_2["网络连接已断开"]
                      L6_2 = L6_2(L7_2)
                      if L6_2 then
                        L6_2 = _cmp
                        L7_2 = Color
                        L7_2 = L7_2["进入游戏"]
                        L7_2 = L7_2["出现删除角色"]
                        L6_2 = L6_2(L7_2)
                        if not L6_2 then
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1500
                          L9_2 = 394
                          L10_2 = 1172
                          L11_2 = 10
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                      end
                      else
                        L6_2 = _cmp
                        L7_2 = Color
                        L7_2 = L7_2["进入游戏"]
                        L7_2 = L7_2["出现删除角色"]
                        L6_2 = L6_2(L7_2)
                        if L6_2 then
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1500
                          L9_2 = 397
                          L10_2 = 743
                          L11_2 = 10
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                        elseif L4_2 then
                          L4_2 = false
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1000
                          L9_2 = 466
                          L10_2 = 1315
                          L6_2(L7_2, L8_2, L9_2, L10_2)
                          L6_2 = _cmp_cx
                          L7_2 = Color
                          L7_2 = L7_2["进入游戏"]
                          L7_2 = L7_2["进入界面33"]
                          L8_2 = {}
                          L9_2 = 55
                          L10_2 = 77
                          L8_2[1] = L9_2
                          L8_2[2] = L10_2
                          L6_2 = L6_2(L7_2, L8_2)
                          if L6_2 then
                          end
                        end
                      end
                    end
                  end
                end
              end
            else
              while true do
                L4_2 = init
                L5_2 = 0
                L4_2(L5_2)
                L4_2 = getColour
                L5_2 = GameFB
                L6_2 = "新登录界面"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  L4_2 = getColour
                  L5_2 = GameFB
                  L6_2 = "显示角色"
                  L4_2 = L4_2(L5_2, L6_2)
                  if L4_2 then
                    L4_2 = randomClick
                    L5_2 = 0
                    L6_2 = 2000
                    L7_2 = 187
                    L8_2 = 810
                    L9_2 = 239
                    L10_2 = 1112
                    L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
                    L4_2 = _ENV["进入互通版"]
                    L5_2 = A0_2
                    L6_2 = true
                    return L4_2(L5_2, L6_2)
                  end
                end
                L4_2 = init
                L5_2 = 1
                L4_2(L5_2)
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "动作按钮"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  return
                end
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "设置按钮"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  return
                end
                L4_2 = getColour
                L5_2 = colorList
                L6_2 = "道具按钮"
                L4_2 = L4_2(L5_2, L6_2)
                if L4_2 then
                  return
                end
                -- 移除 goto lbl_790 和标签，继续执行后续逻辑
              end
            end
          end
      end
      else
        L4_2 = _ENV["循环上号"]
        if L4_2 == "普通循环" then
          L4_2 = _ENV["UI_当前循环"]
          if L4_2 == "从当前循环" then
            L4_2 = _ENV["从当前"]
            if L4_2 == true then
              L4_2 = init
              L5_2 = 0
              L4_2(L5_2)
              _ENV["从当前"] = false
              L4_2 = true
              L5_2 = os
              L5_2 = L5_2.time
              L5_2 = L5_2()
              while true do
                L6_2 = os
                L6_2 = L6_2.time
                L6_2 = L6_2()
                L6_2 = L6_2 - L5_2
                if 120 < L6_2 then
                  L6_2 = _ENV["UI_千机变进入"]
                  if L6_2 == "千机变进入" then
                    L6_2 = _ENV["千机变进入"]
                    L6_2()
                  else
                    L6_2 = _ENV["UI_千机变进入"]
                    if L6_2 == "虚拟机开游戏" then
                      L6_2 = dialog
                      L7_2 = "手动打开虚拟机梦幻登录界面"
                      L6_2(L7_2)
                      L6_2 = mSleep
                      L7_2 = 5555
                      L6_2(L7_2)
                    else
                      L6_2 = runApp
                      L7_2 = "com.netease.mhxyhtb"
                      L6_2(L7_2)
                    end
                  end
                  L6_2 = os
                  L6_2 = L6_2.time
                  L6_2 = L6_2()
                  L5_2 = L6_2
                end
                L6_2 = _find
                L7_2 = Color
                L7_2 = L7_2["抓鬼"]
                L7_2 = L7_2["出现删除角色"]
                L6_2 = L6_2(L7_2)
                if L6_2 then
                  L6_2 = randomClick
                  L7_2 = 2
                  L8_2 = 1500
                  L9_2 = 925
                  L10_2 = 1229
                  L11_2 = 10
                  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                end
                L6_2 = getColour
                L7_2 = colorList
                L8_2 = "删除角色"
                L6_2 = L6_2(L7_2, L8_2)
                if L6_2 then
                  L6_2 = printLog
                  L7_2 = "关闭删除角色"
                  L6_2(L7_2)
                  L2_2 = true
                  L6_2 = randomClick
                  L7_2 = 2
                  L8_2 = 1500
                  L9_2 = 925
                  L10_2 = 1229
                  L11_2 = 10
                  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                else
                  L6_2 = _find
                  L7_2 = Color
                  L7_2 = L7_2["起号"]
                  L7_2 = L7_2["在游戏中"]
                  L6_2 = L6_2(L7_2)
                  if not L6_2 then
                    L6_2 = _find
                    L7_2 = Color
                    L7_2 = L7_2["起号"]
                    L7_2 = L7_2["新春在游戏中"]
                    L6_2 = L6_2(L7_2)
                    if L6_2 then
                      L6_2 = _ENV["进入互通版"]
                      L7_2 = A0_2
                      L8_2 = true
                      return L6_2(L7_2, L8_2)
                    end
                  else
                    L6_2 = _ENV["进入互通版"]
                    L7_2 = A0_2
                    L8_2 = true
                    return L6_2(L7_2, L8_2)
                  end
                  -- 替换 goto lbl_890，直接执行对应逻辑
                  L6_2 = getColour
                  L7_2 = GameFB
                  L8_2 = "新选择角色"
                  L6_2 = L6_2(L7_2, L8_2)
                  if L6_2 then
                    if L2_2 then
                      L6_2 = mSleep
                      L7_2 = math
                      L7_2 = L7_2.random
                      L8_2 = 2000
                      L9_2 = 2500
                      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L7_2(L8_2, L9_2)
                      L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                    end
                    if L2_2 then
                      L6_2 = tonumber
                      L7_2 = _ENV["角色数量"]
                      L6_2 = L6_2(L7_2)
                      if 3 < L6_2 then
                        L2_2 = false
                        L6_2 = printLog
                        L7_2 = "滑动最低部选择角色"
                        L6_2(L7_2)
                        L6_2 = math
                        L6_2 = L6_2.random
                        L7_2 = 92
                        L8_2 = 124
                        L6_2 = L6_2(L7_2, L8_2)
                        L7_2 = math
                        L7_2 = L7_2.random
                        L8_2 = 706
                        L9_2 = 719
                        L7_2 = L7_2(L8_2, L9_2)
                        L8_2 = math
                        L8_2 = L8_2.random
                        L9_2 = 841
                        L10_2 = 865
                        L8_2 = L8_2(L9_2, L10_2)
                        L9_2 = math
                        L9_2 = L9_2.random
                        L10_2 = 706
                        L11_2 = 719
                        L9_2 = L9_2(L10_2, L11_2)
                        L10_2 = moveTo
                        L11_2 = L6_2
                        L12_2 = L7_2
                        L13_2 = L8_2
                        L14_2 = L9_2
                        L15_2 = {}
                        L15_2.step = 100
                        L15_2.ms = 10
                        L15_2.index = 1
                        L15_2.stop = true
                        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                        L10_2 = mSleep
                        L11_2 = math
                        L11_2 = L11_2.random
                        L12_2 = 800
                        L13_2 = 1500
                        L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L11_2(L12_2, L13_2)
                        L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                    end
                    else
                      L6_2 = printLog
                      L7_2 = "选择角色"
                      L6_2(L7_2)
                      L6_2 = tonumber
                      L7_2 = _ENV["角色数量"]
                      L6_2 = L6_2(L7_2)
                      if L6_2 == 3 then
                        L6_2 = randomClick
                        L7_2 = 0
                        L8_2 = 2000
                        L9_2 = 624
                        L10_2 = 1440
                        L11_2 = 729
                        L12_2 = 1504
                        L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                      else
                        L6_2 = tonumber
                        L7_2 = _ENV["角色数量"]
                        L6_2 = L6_2(L7_2)
                        if L6_2 == 2 then
                          L6_2 = randomClick
                          L7_2 = 0
                          L8_2 = 2000
                          L9_2 = 628
                          L10_2 = 951
                          L11_2 = 720
                          L12_2 = 1015
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                        else
                          L6_2 = tonumber
                          L7_2 = _ENV["角色数量"]
                          L6_2 = L6_2(L7_2)
                          if L6_2 == 1 then
                            L6_2 = randomClick
                            L7_2 = 0
                            L8_2 = 2000
                            L9_2 = 619
                            L10_2 = 466
                            L11_2 = 737
                            L12_2 = 523
                            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                          else
                            L6_2 = snapshot
                            L7_2 = "key.png"
                            L8_2 = 400
                            L9_2 = 1220
                            L10_2 = 480
                            L11_2 = 1300
                            L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                            L6_2 = getFileSize
                            L7_2 = userPath
                            L7_2 = L7_2()
                            L8_2 = "/res/key.png"
                            L7_2 = L7_2 .. L8_2
                            L6_2 = L6_2(L7_2)
                            L7_2 = getFileSize
                            L8_2 = userPath
                            L8_2 = L8_2()
                            L9_2 = "/res/KdLogin.png"
                            L8_2 = L8_2 .. L9_2
                            L7_2 = L7_2(L8_2)
                            if L6_2 ~= L7_2 then
                              L8_2 = randomClick
                              L9_2 = 0
                              L10_2 = 2000
                              L11_2 = 243
                              L12_2 = 1448
                              L13_2 = 394
                              L14_2 = 1520
                              L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
                            else
                              L8_2 = snapshot
                              L9_2 = "key.png"
                              L10_2 = 400
                              L11_2 = 720
                              L12_2 = 480
                              L13_2 = 800
                              L8_2(L9_2, L10_2, L11_2, L12_2, L13_2)
                              L8_2 = getFileSize
                              L9_2 = userPath
                              L9_2 = L9_2()
                              L10_2 = "/res/key.png"
                              L9_2 = L9_2 .. L10_2
                              L8_2 = L8_2(L9_2)
                              L9_2 = getFileSize
                              L10_2 = userPath
                              L10_2 = L10_2()
                              L11_2 = "/res/KdLogin.png"
                              L10_2 = L10_2 .. L11_2
                              L9_2 = L9_2(L10_2)
                              if L8_2 ~= L9_2 then
                                L10_2 = randomClick
                                L11_2 = 0
                                L12_2 = 2000
                                L13_2 = 247
                                L14_2 = 968
                                L15_2 = 403
                                L16_2 = 1036
                                L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                              else
                                L10_2 = randomClick
                                L11_2 = 0
                                L12_2 = 2000
                                L13_2 = 242
                                L14_2 = 467
                                L15_2 = 402
                                L16_2 = 546
                                L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                              end
                            end
                          end
                        end
                      end
                    end
                  else
                    if not L4_2 then
                      L6_2 = getColour
                      L7_2 = GameFB
                      L8_2 = "新登录界面"
                      L6_2 = L6_2(L7_2, L8_2)
                      if L6_2 then
                        L6_2 = getColour
                        L7_2 = GameFB
                        L8_2 = "显示角色"
                        L6_2 = L6_2(L7_2, L8_2)
                        if L6_2 then
                          L6_2 = mSleep
                          L7_2 = math
                          L7_2 = L7_2.random
                          L8_2 = 800
                          L9_2 = 1500
                          L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L7_2(L8_2, L9_2)
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
                          L6_2 = randomClick
                          L7_2 = 0
                          L8_2 = 2000
                          L9_2 = 187
                          L10_2 = 955
                          L11_2 = 239
                          L12_2 = 1112
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
                          L6_2 = printLog
                          L7_2 = "进入游戏"
                          L6_2(L7_2)
                          L6_2 = _ENV["进入互通版"]
                          L7_2 = A0_2
                          L8_2 = true
                          return L6_2(L7_2, L8_2)
                      end
                    end
                    else
                      L6_2 = _cmp
                      L7_2 = Color
                      L7_2 = L7_2["进入游戏"]
                      L7_2 = L7_2["网络连接已断开"]
                      L6_2 = L6_2(L7_2)
                      if L6_2 then
                        L6_2 = _cmp
                        L7_2 = Color
                        L7_2 = L7_2["进入游戏"]
                        L7_2 = L7_2["出现删除角色"]
                        L6_2 = L6_2(L7_2)
                        if not L6_2 then
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1500
                          L9_2 = 394
                          L10_2 = 1172
                          L11_2 = 10
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                      end
                      else
                        L6_2 = _cmp
                        L7_2 = Color
                        L7_2 = L7_2["进入游戏"]
                        L7_2 = L7_2["出现删除角色"]
                        L6_2 = L6_2(L7_2)
                        if L6_2 then
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1500
                          L9_2 = 397
                          L10_2 = 743
                          L11_2 = 10
                          L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
                        elseif L4_2 then
                          L4_2 = false
                          L6_2 = randomClick
                          L7_2 = 2
                          L8_2 = 1000
                          L9_2 = 466
                          L10_2 = 1315
                          L6_2(L7_2, L8_2, L9_2, L10_2)
                          L6_2 = _cmp_cx
                          L7_2 = Color
                          L7_2 = L7_2["进入游戏"]
                          L7_2 = L7_2["进入界面33"]
                          L8_2 = {}
                          L9_2 = 77
                          L10_2 = 100
                          L8_2[1] = L9_2
                          L8_2[2] = L10_2
                          L6_2 = L6_2(L7_2, L8_2)
                          if L6_2 then
                          end
                        end
                      end
                    end
                  end
                end
                -- 移除 lbl_1164 标签
              end
          end
        end
        else
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["在游戏中"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = _ENV["进入互通版"]
            L5_2 = A0_2
            L6_2 = true
            return L4_2(L5_2, L6_2)
          end
          L4_2 = _find
          L5_2 = Color
          L5_2 = L5_2["起号"]
          L5_2 = L5_2["新春在游戏中"]
          L4_2 = L4_2(L5_2)
          if L4_2 then
            L4_2 = _ENV["进入互通版"]
            L5_2 = A0_2
            L6_2 = true
            return L4_2(L5_2, L6_2)
          end
          -- 替换 goto lbl_1186，直接执行对应逻辑
          L4_2 = os
          L4_2 = L4_2.time
          L4_2 = L4_2()
          L4_2 = L4_2 - L3_2
          if 30 < L4_2 then
            -- 替换 goto lbl_1227，直接跳出循环
            break
          end
        end
      end
      -- 移除 lbl_1192 标签
    end
    -- 移除 goto lbl_1227 和未使用的标签
    -- 这部分逻辑已经在前面处理过了
  end
end

GameLogin = L0_1

function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L2_2 = os
  L2_2 = L2_2.time
  L2_2 = L2_2()
  while true do
    L3_2 = printLog
    L4_2 = "进入互通版f"
    L3_2(L4_2)
    L3_2 = init
    L4_2 = 1
    L3_2(L4_2)
    L3_2 = _ENV["UI_网易大神"]
    if L3_2 == "网易大神" then
      L3_2 = _cmp
      L4_2 = Color
      L4_2 = L4_2["进入游戏"]
      L4_2 = L4_2["将军令1"]
      L3_2 = L3_2(L4_2)
      if L3_2 then
        L3_2 = _Sleep
        L4_2 = 5100
        L5_2 = 10100
        L3_2(L4_2, L5_2)
      end
      L3_2 = _cmp
      L4_2 = Color
      L4_2 = L4_2["进入游戏"]
      L4_2 = L4_2["将军令2"]
      L3_2 = L3_2(L4_2)
      if L3_2 then
        L3_2 = randomClick
        L4_2 = 0
        L5_2 = 300
        L6_2 = 642
        L7_2 = 674
        L8_2 = 813
        L9_2 = 731
        L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
        L3_2 = _Sleep
        L4_2 = 500
        L5_2 = 1000
        L3_2(L4_2, L5_2)
      end
    end
    L3_2 = getColour
    L4_2 = colorList
    L5_2 = "我知道了"
    L3_2 = L3_2(L4_2, L5_2)
    if L3_2 then
      L3_2 = randomClick
      L4_2 = 0
      L5_2 = 300
      L6_2 = 875
      L7_2 = 675
      L8_2 = 1036
      L9_2 = 714
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
      L3_2 = _Sleep
      L4_2 = 500
      L5_2 = 1000
      L3_2(L4_2, L5_2)
    else
      L3_2 = _find
      L4_2 = Color
      L4_2 = L4_2["起号"]
      L4_2 = L4_2["出现手势"]
      L3_2 = L3_2(L4_2)
      if L3_2 then
        L3_2 = _cmp_tb
        L4_2 = Color
        L4_2 = L4_2["起号"]
        L4_2 = L4_2["出现手势1"]
        L3_2 = L3_2(L4_2)
        if L3_2 then
          L3_2 = _moveto
          L4_2 = 963
          L5_2 = 369
          L6_2 = 1300
          L7_2 = 357
          L3_2(L4_2, L5_2, L6_2, L7_2)
          L3_2 = _Sleep
          L4_2 = 500
          L5_2 = 1000
          L3_2(L4_2, L5_2)
      end
      else
        L3_2 = _cmp
        L4_2 = Color
        L4_2 = L4_2["抓鬼"]
        L4_2 = L4_2["全局手势"]
        L3_2 = L3_2(L4_2)
        if L3_2 then
          L3_2 = _find_tb
          L4_2 = Color
          L4_2 = L4_2["起号"]
          L4_2 = L4_2["绘卷"]
          L3_2 = L3_2(L4_2)
          if L3_2 == false then
            L3_2 = _moveto
            L4_2 = 963
            L5_2 = 369
            L6_2 = 1300
            L7_2 = 357
            L3_2(L4_2, L5_2, L6_2, L7_2)
            L3_2 = mSleep
            L4_2 = 500
            L3_2(L4_2)
        end
        else
          L3_2 = _cmp
          L4_2 = Color
          L4_2 = L4_2["抓鬼"]
          L4_2 = L4_2["老玩家回流"]
          L3_2 = L3_2(L4_2)
          if L3_2 then
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 800
            L6_2 = 1640
            L7_2 = 49
            L8_2 = 1683
            L9_2 = 85
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          else
            L3_2 = _find
            L4_2 = Color
            L4_2 = L4_2["起号"]
            L4_2 = L4_2["空窗口是"]
            L3_2 = L3_2(L4_2)
            if L3_2 then
              L3_2 = _ENV["_随机延时"]
              L4_2 = 511
              L3_2(L4_2)
            else
              L3_2 = _cmp
              L4_2 = Color
              L4_2 = L4_2["进入游戏"]
              L4_2 = L4_2["梦幻精灵"]
              L3_2 = L3_2(L4_2)
              if L3_2 then
                L3_2 = _ENV["通用功能"]
                L3_2 = L3_2["关闭"]
                L3_2()
              else
                L3_2 = _cmp
                L4_2 = Color
                L4_2 = L4_2["进入游戏"]
                L4_2 = L4_2["梦幻三界"]
                L3_2 = L3_2(L4_2)
                if L3_2 then
                  L3_2 = _Sleep
                  L4_2 = 500
                  L5_2 = 1000
                  L3_2(L4_2, L5_2)
                else
                  L3_2 = _cmp
                  L4_2 = Color
                  L4_2 = L4_2["进入游戏"]
                  L4_2 = L4_2["取消每日签到"]
                  L3_2 = L3_2(L4_2)
                  if L3_2 then
                    L3_2 = _Sleep
                    L4_2 = 500
                    L5_2 = 1000
                    L3_2(L4_2, L5_2)
                    L3_2 = 1
                    L4_2 = 10
                    L5_2 = 1
                    for L6_2 = L3_2, L4_2, L5_2 do
                      L7_2 = _cmp
                      L8_2 = Color
                      L8_2 = L8_2["进入游戏"]
                      L8_2 = L8_2["取消每日签到是"]
                      L7_2 = L7_2(L8_2)
                      if L7_2 then
                        L7_2 = _Sleep
                        L8_2 = 500
                        L9_2 = 1000
                        L7_2(L8_2, L9_2)
                      end
                      L7_2 = _cmp
                      L8_2 = Color
                      L8_2 = L8_2["进入游戏"]
                      L8_2 = L8_2["取消每日签到对勾"]
                      L7_2 = L7_2(L8_2)
                      if L7_2 then
                        L7_2 = _Sleep
                        L8_2 = 500
                        L9_2 = 1000
                        L7_2(L8_2, L9_2)
                      end
                      L7_2 = _cmp
                      L8_2 = Color
                      L8_2 = L8_2["进入游戏"]
                      L8_2 = L8_2["取消每日签到"]
                      L7_2 = L7_2(L8_2)
                      if L7_2 then
                        L7_2 = _cmp
                        L8_2 = Color
                        L8_2 = L8_2["进入游戏"]
                        L8_2 = L8_2["取消每日签到对勾"]
                        L7_2 = L7_2(L8_2)
                        if L7_2 == false then
                          L7_2 = _Sleep
                          L8_2 = 500
                          L9_2 = 1000
                          L7_2(L8_2, L9_2)
                          L7_2 = _cmp
                          L8_2 = Color
                          L8_2 = L8_2["进入游戏"]
                          L8_2 = L8_2["关闭每日签到1"]
                          L7_2 = L7_2(L8_2)
                          if L7_2 then
                            L7_2 = _Sleep
                            L8_2 = 500
                            L9_2 = 1000
                            L7_2(L8_2, L9_2)
                          end
                          -- 替换 goto lbl_593，继续下一次循环
                          break
                        end
                      end
                      L7_2 = _Sleep
                      L8_2 = 300
                      L9_2 = 500
                      L7_2(L8_2, L9_2)
                    end
                  else
                    L3_2 = _cmp
                    L4_2 = Color
                    L4_2 = L4_2["弹窗"]
                    L4_2 = L4_2["关闭对话"]
                    L3_2 = L3_2(L4_2)
                    if L3_2 then
                      L3_2 = _Sleep
                      L4_2 = 500
                      L5_2 = 1000
                      L3_2(L4_2, L5_2)
                    else
                      L3_2 = _find_tb
                      L4_2 = Color
                      L4_2 = L4_2["进入游戏"]
                      L4_2 = L4_2["新版签到"]
                      L3_2 = L3_2(L4_2)
                      if L3_2 then
                        L3_2 = _Sleep
                        L4_2 = 500
                        L5_2 = 1000
                        L3_2(L4_2, L5_2)
                      else
                        L3_2 = _find
                        L4_2 = Color
                        L4_2 = L4_2["进入游戏"]
                        L4_2 = L4_2["新版本下载"]
                        L3_2 = L3_2(L4_2)
                        if L3_2 then
                          L3_2 = tap
                          L4_2 = 676
                          L5_2 = 711
                          L3_2(L4_2, L5_2)
                          L3_2 = _Sleep
                          L4_2 = 500
                          L5_2 = 1000
                          L3_2(L4_2, L5_2)
                        else
                          L3_2 = myBlockcolor
                          L4_2 = sevenColor
                          L4_2 = L4_2["叉叉按钮"]
                          L3_2 = L3_2(L4_2)
                          if L3_2 then
                            L3_2 = randomTap
                            L4_2 = x
                            L5_2 = y
                            L6_2 = 15
                            L3_2(L4_2, L5_2, L6_2)
                            L3_2 = _ENV["_随机延时"]
                            L4_2 = 511
                            L3_2(L4_2)
                          else
                            L3_2 = _cmp
                            L4_2 = Color
                            L4_2 = L4_2["进入游戏"]
                            L4_2 = L4_2["星辰投票"]
                            L3_2 = L3_2(L4_2)
                            if L3_2 then
                              L3_2 = _Sleep
                              L4_2 = 500
                              L5_2 = 1000
                              L3_2(L4_2, L5_2)
                            else
                              L3_2 = _cmp
                              L4_2 = Color
                              L4_2 = L4_2["进入游戏"]
                              L4_2 = L4_2["年卡礼包"]
                              L3_2 = L3_2(L4_2)
                              if L3_2 then
                                L3_2 = _Sleep
                                L4_2 = 500
                                L5_2 = 1000
                                L3_2(L4_2, L5_2)
                              else
                                L3_2 = _cmp
                                L4_2 = Color
                                L4_2 = L4_2["进入游戏"]
                                L4_2 = L4_2["狰战投票"]
                                L3_2 = L3_2(L4_2)
                                if L3_2 then
                                  L3_2 = _Sleep
                                  L4_2 = 500
                                  L5_2 = 1000
                                  L3_2(L4_2, L5_2)
                                else
                                  L3_2 = getColour
                                  L4_2 = GameFB
                                  L5_2 = "误触模式"
                                  L3_2 = L3_2(L4_2, L5_2)
                                  if not L3_2 then
                                    L3_2 = getColour
                                    L4_2 = colorList
                                    L5_2 = "登录奖励"
                                    L3_2 = L3_2(L4_2, L5_2)
                                    if L3_2 then
                                      L3_2 = randomClick
                                      L4_2 = 2
                                      L5_2 = 300
                                      L6_2 = 1500
                                      L7_2 = 1500
                                      L8_2 = 1500
                                      L9_2 = 1500
                                      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                      break
                                    end
                                  end
                                  L3_2 = randomClick
                                  L4_2 = 2
                                  L5_2 = 300
                                  L6_2 = 1426
                                  L7_2 = 191
                                  L3_2(L4_2, L5_2, L6_2, L7_2)
                                  L3_2 = _Sleep
                                  L4_2 = 500
                                  L5_2 = 1000
                                  L3_2(L4_2, L5_2)
                                  L3_2 = 1
                                  L4_2 = 10
                                  L5_2 = 1
                                  for L6_2 = L3_2, L4_2, L5_2 do
                                    L7_2 = getColour
                                    L8_2 = GameFB
                                    L9_2 = "误触模式"
                                    L7_2 = L7_2(L8_2, L9_2)
                                    if L7_2 then
                                      break
                                    end
                                    L7_2 = getColour
                                    L8_2 = colorList
                                    L9_2 = "登录奖励"
                                    L7_2 = L7_2(L8_2, L9_2)
                                    if L7_2 then
                                      break
                                    end
                                    L7_2 = mSleep
                                    L8_2 = 100
                                    L7_2(L8_2)
                                  end
                                  -- 检查游戏状态
                                  L3_2 = getColour
                                  L4_2 = colorList
                                  L5_2 = "动作按钮"
                                  L3_2 = L3_2(L4_2, L5_2)
                                  if L3_2 then
                                    return 1
                                  end
                                  L3_2 = getColour
                                  L4_2 = colorList
                                  L5_2 = "设置按钮"
                                  L3_2 = L3_2(L4_2, L5_2)
                                  if L3_2 then
                                    return 1
                                  end
                                  L3_2 = getColour
                                  L4_2 = colorList
                                  L5_2 = "道具按钮"
                                  L3_2 = L3_2(L4_2, L5_2)
                                  if L3_2 then
                                    return 1
                                  end
                                  -- 替换 goto lbl_389，直接执行对应逻辑
                                  L3_2 = getWordStock
                                  L4_2 = WordStock
                                  L5_2 = "对应版本"
                                  L6_2 = 573
                                  L7_2 = 459
                                  L8_2 = 729
                                  L9_2 = 511
                                  L10_2 = 84
                                  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
                                  if L3_2 then
                                    if A1_2 then
                                      L3_2 = randomClick
                                      L4_2 = 0
                                      L5_2 = 500
                                      L6_2 = 1089
                                      L7_2 = 655
                                      L8_2 = 1253
                                      L9_2 = 703
                                      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                      gameStart = true
                                      L3_2 = GameLogin
                                      L4_2 = A0_2
                                      return L3_2(L4_2)
                                    else
                                      L3_2 = 0
                                      return L3_2
                                    end
                                  else
                                    L3_2 = getColour
                                    L4_2 = GameFB
                                    L5_2 = "版本更新"
                                    L3_2 = L3_2(L4_2, L5_2)
                                    if L3_2 then
                                      L3_2 = randomClick
                                      L4_2 = 0
                                      L5_2 = 500
                                      L6_2 = 852
                                      L7_2 = 622
                                      L8_2 = 1060
                                      L9_2 = 665
                                      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                    else
                                      L3_2 = os
                                      L3_2 = L3_2.time
                                      L3_2 = L3_2()
                                      L3_2 = L3_2 - L2_2
                                      if 60 < L3_2 then
                                        L3_2 = touchUp
                                        L4_2 = 581
                                        L5_2 = 1061
                                        L3_2(L4_2, L5_2)
                                        L3_2 = mSleep
                                        L4_2 = 600
                                        L3_2(L4_2)
                                      else
                                        L3_2 = init
                                        L4_2 = 0
                                        L3_2(L4_2)
                                        L3_2 = os
                                        L3_2 = L3_2.time
                                        L3_2 = L3_2()
                                        L3_2 = L3_2 - L2_2
                                        if 30 < L3_2 then
                                          L3_2 = getColour
                                          L4_2 = GameFB
                                          L5_2 = "新登录界面"
                                          L3_2 = L3_2(L4_2, L5_2)
                                          if L3_2 then
                                            L3_2 = randomClick
                                            L4_2 = 0
                                            L5_2 = 2000
                                            L6_2 = 187
                                            L7_2 = 810
                                            L8_2 = 239
                                            L9_2 = 1112
                                            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                            L3_2 = os
                                            L3_2 = L3_2.time
                                            L3_2 = L3_2()
                                            L2_2 = L3_2
                                        end
                                        else
                                          L3_2 = getColour
                                          L4_2 = GameFB
                                          L5_2 = "连接失败"
                                          L3_2 = L3_2(L4_2, L5_2)
                                          if L3_2 then
                                            L3_2 = getColour
                                            L4_2 = GameFB
                                            L5_2 = "重试链接"
                                            L3_2 = L3_2(L4_2, L5_2)
                                            if L3_2 then
                                              L3_2 = printLog
                                              L4_2 = "连接失败"
                                              L3_2(L4_2)
                                              L3_2 = randomClick
                                              L4_2 = 0
                                              L5_2 = 1000
                                              L6_2 = 1109
                                              L7_2 = 743
                                              L8_2 = 1387
                                              L9_2 = 820
                                              L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                          end
                                          else
                                            L3_2 = _cmp
                                            L4_2 = Color
                                            L4_2 = L4_2["进入游戏"]
                                            L4_2 = L4_2["网络连接已断开"]
                                            L3_2 = L3_2(L4_2)
                                            if L3_2 then
                                              L3_2 = _cmp
                                              L4_2 = Color
                                              L4_2 = L4_2["进入游戏"]
                                              L4_2 = L4_2["出现删除角色"]
                                              L3_2 = L3_2(L4_2)
                                              if not L3_2 then
                                                L3_2 = randomClick
                                                L4_2 = 2
                                                L5_2 = 1500
                                                L6_2 = 394
                                                L7_2 = 1172
                                                L8_2 = 10
                                                L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
                                            end
                                            else
                                              L3_2 = _cmp
                                              L4_2 = Color
                                              L4_2 = L4_2["进入游戏"]
                                              L4_2 = L4_2["出现删除角色"]
                                              L3_2 = L3_2(L4_2)
                                              if L3_2 then
                                                L3_2 = randomClick
                                                L4_2 = 2
                                                L5_2 = 1500
                                                L6_2 = 397
                                                L7_2 = 743
                                                L8_2 = 10
                                                L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
                                              else
                                                L3_2 = _cmp
                                                L4_2 = Color
                                                L4_2 = L4_2["进入游戏"]
                                                L4_2 = L4_2["排队已满"]
                                                L3_2 = L3_2(L4_2)
                                                if L3_2 then
                                                  L3_2 = randomClick
                                                  L4_2 = 0
                                                  L5_2 = 2000
                                                  L6_2 = 368
                                                  L7_2 = 826
                                                  L8_2 = 432
                                                  L9_2 = 849
                                                  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
                                                else
                                                  L3_2 = os
                                                  L3_2 = L3_2.time
                                                  L3_2 = L3_2()
                                                  L3_2 = L3_2 - L2_2
                                                  if 60 < L3_2 then
                                                    L3_2 = isFrontApp
                                                    L4_2 = "com.netease.mhxyhtb"
                                                    L3_2 = L3_2(L4_2)
                                                    if L3_2 == 0 then
                                                      L3_2 = _ENV["UI_千机变进入"]
                                                      if L3_2 == "千机变进入" then
                                                        L3_2 = _ENV["千机变进入"]
                                                        L3_2()
                                                      else
                                                        L3_2 = _ENV["UI_千机变进入"]
                                                        if L3_2 == "虚拟机开游戏" then
                                                          L3_2 = dialog
                                                          L4_2 = "手动进入到虚拟机登录角色界面或游戏内"
                                                          L3_2(L4_2)
                                                          L3_2 = mSleep
                                                          L4_2 = 5555
                                                          L3_2(L4_2)
                                                        else
                                                          L3_2 = runApp
                                                          L4_2 = "com.netease.mhxyhtb"
                                                          L3_2(L4_2)
                                                        end
                                                      end
                                                      L3_2 = mSleep
                                                      L4_2 = 3000
                                                      L3_2(L4_2)
                                                      L3_2 = os
                                                      L3_2 = L3_2.time
                                                      L3_2 = L3_2()
                                                      L2_2 = L3_2
                                                    end
                                                  end
                                                  L3_2 = mSleep
                                                  L4_2 = 300
                                                  L3_2(L4_2)
                                                end
                                              end
                                            end
                                          end
                                        end
                                      end
                                    end
                                  end
                                end
                              end
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
    -- 移除 lbl_593 标签
  end
end

_ENV["进入互通版"] = L0_1

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = true
  while true do
    L1_2 = mSleep
    L2_2 = 100
    L1_2(L2_2)
    L1_2 = getColour
    L2_2 = GameFB
    L3_2 = "添加角色"
    L1_2 = L1_2(L2_2, L3_2)
    if L1_2 then
      L1_2 = printLog
      L2_2 = "完成清理角色"
      L1_2(L2_2)
      return
    else
      L1_2 = printLog
      L2_2 = "清理角色"
      L1_2(L2_2)
      L1_2 = getColour
      L2_2 = GameFB
      L3_2 = "新选择角色"
      L1_2 = L1_2(L2_2, L3_2)
      if L1_2 then
        L1_2 = getColour
        L2_2 = GameFB
        L3_2 = "绑定角色"
        L1_2 = L1_2(L2_2, L3_2)
        if L1_2 then
          L0_2 = false
          L1_2 = printLog
          L2_2 = "选择清理绑定的角色"
          L1_2(L2_2)
          while true do
            L1_2 = mSleep
            L2_2 = 100
            L1_2(L2_2)
            L1_2 = getColour
            L2_2 = GameFB
            L3_2 = "绑定角色"
            L1_2 = L1_2(L2_2, L3_2)
            if L1_2 then
              while true do
                L1_2 = getColour
                L2_2 = GameFB
                L3_2 = "新确定登录"
                L1_2 = L1_2(L2_2, L3_2)
                if L1_2 then
                  L1_2 = randomClick
                  L2_2 = 0
                  L3_2 = 500
                  L4_2 = 373
                  L5_2 = 1074
                  L6_2 = 425
                  L7_2 = 1279
                  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
                  -- 替换 goto lbl_143，继续循环
                  break
                else
                  L1_2 = randomClick
                  L2_2 = 2
                  L3_2 = 500
                  L4_2 = 740
                  L5_2 = 404
                  L6_2 = 10
                  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
                end
              end
            else
              L1_2 = getColour
              L2_2 = GameFB
              L3_2 = "新确定登录"
              L1_2 = L1_2(L2_2, L3_2)
              if L1_2 then
                L1_2 = randomClick
                L2_2 = 0
                L3_2 = 500
                L4_2 = 373
                L5_2 = 1074
                L6_2 = 425
                L7_2 = 1279
                L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
              else
                L1_2 = getColour
                L2_2 = GameFB
                L3_2 = "搜索录像"
                L1_2 = L1_2(L2_2, L3_2)
                if L1_2 then
                  L1_2 = randomClick
                  L2_2 = 2
                  L3_2 = 500
                  L4_2 = 932
                  L5_2 = 1342
                  L6_2 = 10
                  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
                  L1_2 = _ENV["工作室模式"]
                  return L1_2()
                else
                  L1_2 = getColour
                  L2_2 = GameFB
                  L3_2 = "添加角色"
                  L1_2 = L1_2(L2_2, L3_2)
                  if L1_2 then
                    L1_2 = printLog
                    L2_2 = "完成清理角色"
                    L1_2(L2_2)
                    return
                  else
                    L1_2 = getColour
                    L2_2 = GameFB
                    L3_2 = "连接失败I"
                    L1_2 = L1_2(L2_2, L3_2)
                    if L1_2 then
                      L1_2 = getColour
                      L2_2 = GameFB
                      L3_2 = "重试链接I"
                      L1_2 = L1_2(L2_2, L3_2)
                      if L1_2 then
                        L1_2 = printLog
                        L2_2 = "连接失败，重试链接"
                        L1_2(L2_2)
                        L1_2 = randomClick
                        L2_2 = 0
                        L3_2 = 1000
                        L4_2 = 264
                        L5_2 = 1132
                        L6_2 = 323
                        L7_2 = 1379
                        L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
                    end
                    else
                      L1_2 = randomClick
                      L2_2 = 2
                      L3_2 = 500
                      L4_2 = 1031
                      L5_2 = 1757
                      L6_2 = 10
                      L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
                    end
                  end
                end
              end
            end
            -- 移除 lbl_143 标签
          end
        elseif L0_2 then
          L1_2 = randomClick
          L2_2 = 2
          L3_2 = 1500
          L4_2 = 925
          L5_2 = 1229
          L6_2 = 10
          L1_2(L2_2, L3_2, L4_2, L5_2, L6_2)
        end
      else
        L1_2 = getColour
        L2_2 = GameFB
        L3_2 = "连接失败I"
        L1_2 = L1_2(L2_2, L3_2)
        if L1_2 then
          L1_2 = getColour
          L2_2 = GameFB
          L3_2 = "重试链接I"
          L1_2 = L1_2(L2_2, L3_2)
          if L1_2 then
            L1_2 = printLog
            L2_2 = "连接失败，重试链接"
            L1_2(L2_2)
            L1_2 = randomClick
            L2_2 = 0
            L3_2 = 1000
            L4_2 = 264
            L5_2 = 1132
            L6_2 = 323
            L7_2 = 1379
            L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
        end
        else
          L1_2 = getColour
          L2_2 = GameFB
          L3_2 = "新确定登录"
          L1_2 = L1_2(L2_2, L3_2)
          if L1_2 then
            L1_2 = randomClick
            L2_2 = 0
            L3_2 = 2000
            L4_2 = 368
            L5_2 = 1048
            L6_2 = 432
            L7_2 = 1310
            L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
          elseif L0_2 then
            L1_2 = randomClick
            L2_2 = 0
            L3_2 = 2000
            L4_2 = 443
            L5_2 = 1285
            L6_2 = 497
            L7_2 = 1342
            L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
          end
        end
      end
    end
  end
end

_ENV["工作室模式"] = L0_1

function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  while true do
    L3_2 = init
    L4_2 = 0
    L3_2(L4_2)
    L3_2 = getColour
    L4_2 = GameFB
    L5_2 = "服务器选择"
    L3_2 = L3_2(L4_2, L5_2)
    if not L3_2 then
      L3_2 = getColour
      L4_2 = GameFB
      L5_2 = "服务器选择I"
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        L3_2 = moveTo
        L4_2 = 782
        L5_2 = 948
        L6_2 = 260
        L7_2 = 948
        L8_2 = {}
        L8_2.step = 30
        L8_2.ms = 10
        L8_2.index = 1
        L8_2.stop = true
        L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
      else
        -- 处理 goto lbl_159 的逻辑
        L3_2 = getColour
        L4_2 = GameFB
        L5_2 = "新确定登录"
        L3_2 = L3_2(L4_2, L5_2)
        if L3_2 then
          L3_2 = getColour
          L4_2 = GameFB
          L5_2 = "将军令"
          L3_2 = L3_2(L4_2, L5_2)
          if not L3_2 then
            L3_2 = printLog
            L4_2 = "确定登入"
            L3_2(L4_2)
            L3_2 = randomClick
            L4_2 = 0
            L5_2 = 2000
            L6_2 = 368
            L7_2 = 1048
            L8_2 = 432
            L9_2 = 1310
            L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          end
        end
        return
      end
    L3_2 = mSleep
    L4_2 = math
    L4_2 = L4_2.random
    L5_2 = 500
    L6_2 = 800
    L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L4_2(L5_2, L6_2)
    L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
    L3_2 = init
    L4_2 = 1
    L3_2(L4_2)
    L3_2 = _ENV["大区名字"]
    L3_2 = L3_2[A0_2]
    if L3_2 == nil then
      L3_2 = dialog
      L4_2 = "没有 \""
      L5_2 = A0_2
      L6_2 = "\" 的数据，请联系作者添加。。"
      L4_2 = L4_2 .. L5_2 .. L6_2
      L3_2(L4_2)
      L3_2 = lueExit
      L3_2()
    else
      while true do
        L3_2 = 1
        L4_2 = 5
        L5_2 = 1
        for L6_2 = L3_2, L4_2, L5_2 do
          L7_2 = getWordName
          L8_2 = _ENV["大区名字"]
          L9_2 = A0_2
          L10_2 = 850
          L11_2 = 210
          L12_2 = 1050
          L13_2 = 1010
          L14_2 = 80
          L7_2, L8_2, L9_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
          if L7_2 then
            L10_2 = printLog
            L11_2 = "识别成功："
            L12_2 = A0_2
            L13_2 = " 查找:"
            L14_2 = A1_2
            L15_2 = " 号位角色"
            L11_2 = L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
            L10_2(L11_2)
            L10_2 = _ENV["选择登录角色"]
            L11_2 = L8_2
            L12_2 = L9_2
            L13_2 = A0_2
            L14_2 = A1_2
            L15_2 = A2_2
            return L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
          else
            L10_2 = getColour
            L11_2 = GameFB
            L12_2 = "连接失败"
            L10_2 = L10_2(L11_2, L12_2)
            if L10_2 then
              L10_2 = getColour
              L11_2 = GameFB
              L12_2 = "重试链接"
              L10_2 = L10_2(L11_2, L12_2)
              if L10_2 then
                L10_2 = printLog
                L11_2 = "连接失败"
                L10_2(L11_2)
                L10_2 = randomClick
                L11_2 = 0
                L12_2 = 1000
                L13_2 = 1109
                L14_2 = 743
                L15_2 = 1387
                L16_2 = 820
                L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
                L10_2 = _ENV["查找角色位置"]
                L11_2 = A0_2
                L12_2 = A1_2
                L13_2 = A2_2
                return L10_2(L11_2, L12_2, L13_2)
            end
            else
              if L6_2 == 3 and not L7_2 then
                L10_2 = getColour
                L11_2 = GameFB
                L12_2 = "连接失败"
                L10_2 = L10_2(L11_2, L12_2)
                if not L10_2 then
                  L10_2 = moveTo
                  L11_2 = 941
                  L12_2 = 943
                  L13_2 = 941
                  L14_2 = 234
                  L15_2 = {}
                  L15_2.step = 30
                  L15_2.ms = 10
                  L15_2.index = 1
                  L15_2.stop = true
                  L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
                  L10_2 = mSleep
                  L11_2 = math
                  L11_2 = L11_2.random
                  L12_2 = 500
                  L13_2 = 800
                  L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2(L12_2, L13_2)
                  L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
              end
              elseif L6_2 == 5 then
                L10_2 = printLog
                L11_2 = "识别不到服务器重新查找"
                L10_2(L11_2)
                L10_2 = _ENV["查找角色位置"]
                L11_2 = A0_2
                L12_2 = A1_2
                L13_2 = A2_2
                return L10_2(L11_2, L12_2, L13_2)
              else
                L10_2 = printLog
                L11_2 = "服务器名字识别不到"
                L10_2(L11_2)
              end
            end
          end
        end
      end
      -- 移除 goto lbl_235 和 lbl_159 标签
      L3_2 = getColour
      L4_2 = GameFB
      L5_2 = "新确定登录"
      L3_2 = L3_2(L4_2, L5_2)
      if L3_2 then
        L3_2 = getColour
        L4_2 = GameFB
        L5_2 = "将军令"
        L3_2 = L3_2(L4_2, L5_2)
        if not L3_2 then
          L3_2 = printLog
          L4_2 = "确定登入"
          L3_2(L4_2)
          L3_2 = randomClick
          L4_2 = 0
          L5_2 = 2000
          L6_2 = 368
          L7_2 = 1048
          L8_2 = 432
          L9_2 = 1310
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          L3_2 = mSleep
          L4_2 = math
          L4_2 = L4_2.random
          L5_2 = 4000
          L6_2 = 6000
          L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L4_2(L5_2, L6_2)
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      end
      else
        L3_2 = getColour
        L4_2 = GameFB
        L5_2 = "服务器繁忙"
        L3_2 = L3_2(L4_2, L5_2)
        if L3_2 then
          L3_2 = mSleep
          L4_2 = math
          L4_2 = L4_2.random
          L5_2 = 40000
          L6_2 = 60000
          L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L4_2(L5_2, L6_2)
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
          L3_2 = randomClick
          L4_2 = 0
          L5_2 = 1000
          L6_2 = 373
          L7_2 = 647
          L8_2 = 424
          L9_2 = 826
          L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
        else
          L3_2 = getColour
          L4_2 = GameFB
          L5_2 = "连接失败I"
          L3_2 = L3_2(L4_2, L5_2)
          if L3_2 then
            L3_2 = getColour
            L4_2 = GameFB
            L5_2 = "重试链接I"
            L3_2 = L3_2(L4_2, L5_2)
            if L3_2 then
              L3_2 = printLog
              L4_2 = "连接失败，重试链接"
              L3_2(L4_2)
              L3_2 = randomClick
              L4_2 = 0
              L5_2 = 1000
              L6_2 = 264
              L7_2 = 1132
              L8_2 = 323
              L9_2 = 1379
              L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
            end
          end
        end
      end
    end
    -- 移除 lbl_235 标签
  end
end

_ENV["查找角色位置"] = L0_1

function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L5_2 = {}
  L6_2 = {}
  L7_2 = 1224
  L8_2 = 251
  L9_2 = 1359
  L10_2 = 392
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L6_2[4] = L10_2
  L7_2 = {}
  L8_2 = 1219
  L9_2 = 437
  L10_2 = 1366
  L11_2 = 585
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  L8_2 = {}
  L9_2 = 1218
  L10_2 = 630
  L11_2 = 1359
  L12_2 = 772
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L8_2[4] = L12_2
  L9_2 = {}
  L10_2 = 1222
  L11_2 = 818
  L12_2 = 1361
  L13_2 = 960
  L9_2[1] = L10_2
  L9_2[2] = L11_2
  L9_2[3] = L12_2
  L9_2[4] = L13_2
  L10_2 = {}
  L11_2 = 1221
  L12_2 = 461
  L13_2 = 1362
  L14_2 = 604
  L10_2[1] = L11_2
  L10_2[2] = L12_2
  L10_2[3] = L13_2
  L10_2[4] = L14_2
  L11_2 = {}
  L12_2 = 1225
  L13_2 = 653
  L14_2 = 1359
  L15_2 = 787
  L11_2[1] = L12_2
  L11_2[2] = L13_2
  L11_2[3] = L14_2
  L11_2[4] = L15_2
  L5_2[1] = L6_2
  L5_2[2] = L7_2
  L5_2[3] = L8_2
  L5_2[4] = L9_2
  L5_2[5] = L10_2
  L5_2[6] = L11_2
  L6_2 = randomClick
  L7_2 = 2
  L8_2 = 800
  L9_2 = A0_2 + 50
  L10_2 = A1_2 - 20
  L11_2 = 20
  L6_2(L7_2, L8_2, L9_2, L10_2, L11_2)
  if 5 <= A3_2 and 5 <= A3_2 then
    L6_2 = 1
    L7_2 = 3
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = getColors
      L11_2 = GameFB
      L12_2 = "创建角色"
      L13_2 = L5_2[A3_2]
      L13_2 = L13_2[1]
      L14_2 = L5_2[A3_2]
      L14_2 = L14_2[2]
      L15_2 = L5_2[A3_2]
      L15_2 = L15_2[3]
      L16_2 = L5_2[A3_2]
      L16_2 = L16_2[4]
      L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
      if L10_2 then
        break
      end
      L10_2 = moveTo
      L11_2 = 1475
      L12_2 = 933
      L13_2 = 1454
      L14_2 = 286
      L15_2 = {}
      L15_2.step = 30
      L15_2.ms = 10
      L15_2.index = 1
      L15_2.stop = true
      L10_2(L11_2, L12_2, L13_2, L14_2, L15_2)
      L10_2 = mSleep
      L11_2 = math
      L11_2 = L11_2.random
      L12_2 = 500
      L13_2 = 800
      L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2(L12_2, L13_2)
      L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
    end
  end
  L6_2 = getColors
  L7_2 = GameFB
  L8_2 = "创建角色"
  L9_2 = L5_2[A3_2]
  L9_2 = L9_2[1]
  L10_2 = L5_2[A3_2]
  L10_2 = L10_2[2]
  L11_2 = L5_2[A3_2]
  L11_2 = L11_2[3]
  L12_2 = L5_2[A3_2]
  L12_2 = L12_2[4]
  L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  if L6_2 then
    L6_2 = dialog
    L7_2 = "error:没发现'"
    L8_2 = A2_2
    L9_2 = "'"
    L10_2 = A3_2
    L11_2 = "号角色"
    L7_2 = L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
    L6_2(L7_2)
    L6_2 = luaExit
    L6_2()
  else
    L6_2 = randomClick
    L7_2 = 0
    L8_2 = 500
    L9_2 = L5_2[A3_2]
    L9_2 = L9_2[1]
    L9_2 = L9_2 + 50
    L10_2 = L5_2[A3_2]
    L10_2 = L10_2[2]
    L10_2 = L10_2 + 10
    L11_2 = L5_2[A3_2]
    L11_2 = L11_2[3]
    L11_2 = L11_2 + 200
    L12_2 = L5_2[A3_2]
    L12_2 = L12_2[4]
    L12_2 = L12_2 - 10
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    L6_2 = os
    L6_2 = L6_2.time
    L6_2 = L6_2()
    while true do
      L7_2 = init
      L8_2 = 0
      L7_2(L8_2)
      L7_2 = _ENV["UI_网易大神"]
      if L7_2 == "网易大神" then
        L7_2 = _find
        L8_2 = Color
        L8_2 = L8_2["进入游戏"]
        L8_2 = L8_2["将军令33"]
        L7_2 = L7_2(L8_2)
        if L7_2 then
          L7_2 = _Sleep
          L8_2 = 1500
          L9_2 = 2000
          L7_2(L8_2, L9_2)
        end
        L7_2 = _find
        L8_2 = Color
        L8_2 = L8_2["进入游戏"]
        L8_2 = L8_2["将军令44"]
        L7_2 = L7_2(L8_2)
        if L7_2 then
          L7_2 = randomClick
          L8_2 = 0
          L9_2 = 1000
          L10_2 = 596
          L11_2 = 398
          L12_2 = 793
          L13_2 = 603
          L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
          L7_2 = _Sleep
          L8_2 = 1500
          L9_2 = 2000
          L7_2(L8_2, L9_2)
        end
      end
      L7_2 = getColour
      L8_2 = GameFB
      L9_2 = "服务器繁忙"
      L7_2 = L7_2(L8_2, L9_2)
      if L7_2 then
        L7_2 = mSleep
        L8_2 = math
        L8_2 = L8_2.random
        L9_2 = 4000
        L10_2 = 6000
        L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L8_2(L9_2, L10_2)
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
        L7_2 = randomClick
        L8_2 = 0
        L9_2 = 1000
        L10_2 = 373
        L11_2 = 647
        L12_2 = 424
        L13_2 = 826
        L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L7_2 = _ENV["查找角色位置"]
        L8_2 = A2_2
        L9_2 = A3_2
        L10_2 = A4_2
        return L7_2(L8_2, L9_2, L10_2)
      else
        L7_2 = os
        L7_2 = L7_2.time
        L7_2 = L7_2()
        L7_2 = L7_2 - L6_2
        if 30 < L7_2 and L6_2 ~= 0 then
          L7_2 = _ENV["查找角色位置"]
          L8_2 = A2_2
          L9_2 = A3_2
          L10_2 = A4_2
          return L7_2(L8_2, L9_2, L10_2)
        else
          L7_2 = getColour
          L8_2 = GameFB
          L9_2 = "新登录界面"
          L7_2 = L7_2(L8_2, L9_2)
          if L7_2 then
            L7_2 = mSleep
            L8_2 = math
            L8_2 = L8_2.random
            L9_2 = 800
            L10_2 = 1500
            L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L8_2(L9_2, L10_2)
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
            L7_2 = printLog
            L8_2 = "选定角色进入游戏"
            L7_2(L8_2)
            L7_2 = randomClick
            L8_2 = 0
            L9_2 = 2000
            L10_2 = 187
            L11_2 = 810
            L12_2 = 239
            L13_2 = 1112
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
            L7_2 = _ENV["进入互通版"]
            L8_2 = A4_2
            L9_2 = true
            return L7_2(L8_2, L9_2)
          else
            L7_2 = getColour
            L8_2 = GameFB
            L9_2 = "连接失败"
            L7_2 = L7_2(L8_2, L9_2)
            if L7_2 then
              L7_2 = getColour
              L8_2 = GameFB
              L9_2 = "重试链接"
              L7_2 = L7_2(L8_2, L9_2)
              if L7_2 then
                L7_2 = printLog
                L8_2 = "连接失败"
                L7_2(L8_2)
                L7_2 = randomClick
                L8_2 = 0
                L9_2 = 1000
                L10_2 = 1109
                L11_2 = 743
                L12_2 = 1387
                L13_2 = 820
                L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
              end
            end
          end
        end
      end
    end
  end
end

_ENV["选择登录角色"] = L0_1
